package com.ctsi.hndx.exception;


import com.ctsi.hndx.enums.BusinessExceptionEnum;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.utils.StringUtils;
import lombok.Data;

/**
 * @desc 业务异常类
 *
 * <AUTHOR>
 * @since 9/18/2017 3:00 PM
 */
@Data
public class BusinessException extends RuntimeException {

	private static final long serialVersionUID = 194906846739586856L;

	protected String code;

	protected String message;

	protected ResultCode resultCode;

	protected Object data;

	public BusinessException() {
		BusinessExceptionEnum exceptionEnum = BusinessExceptionEnum.getByEClass(this.getClass());
		if (exceptionEnum != null) {
			resultCode = exceptionEnum.getResultCode();
			code = exceptionEnum.getResultCode().code().toString();
			message = exceptionEnum.getResultCode().message();
		}

	}

	public BusinessException(String message) {
		this.resultCode = ResultCode.ERROR;
		this.code = String.valueOf(resultCode.code());
		this.message = message;
	}

	public BusinessException(String format, Object... objects) {
		this.resultCode = ResultCode.ERROR;
		this.code = String.valueOf(resultCode.code());
		this.message = StringUtils.formatIfArgs(format, "{}", objects);
	}




	public BusinessException(ResultCode resultCode, Object data) {
		this(resultCode);
		this.data = data;
	}

	public BusinessException(ResultCode resultCode) {
		this.resultCode = resultCode;
		this.code = resultCode.code().toString();
		this.message = resultCode.message();
	}

	public BusinessException(ResultCode resultCode, String appendMessage) {
		this.resultCode = resultCode;
		this.code = resultCode.code().toString();
		this.message = resultCode.message() + " " + appendMessage;
	}

}
