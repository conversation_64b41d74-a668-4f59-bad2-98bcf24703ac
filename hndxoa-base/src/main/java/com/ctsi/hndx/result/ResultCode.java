package com.ctsi.hndx.result;

/**
 * @desc API 统一返回状态码
 */
public enum ResultCode {

	/* 成功状态码 */
	SUCCESS(20000, "成功"),

	ERROR(10001, "失败，其他原因"),

	/* 参数错误：10001-19999 */
	PARAM_IS_INVALID(10001, "参数无效"),
	PARAM_IS_BLANK(10002, "参数为空"),
	PARAM_TYPE_BIND_ERROR(10003, "参数类型错误"),
	PARAM_NOT_COMPLETE(10004, "参数缺失"),
	PARAM_NOT_UPDATE_DELETE(10005,"没有删除或者更新数据，请检查传入id是否正确或者数据是否还存在"),
	PARAM_PK_INVALID(10006, "必须传入主键参数"),
	ARCHIVE_BOX_DELETE(10007,"档案盒存在归档公文，不能删除"),
	PARAM_NOT_UPDATE(10008,"没有个人自定义数据，请设置后再进行此操作"),
	/* 用户，角色，部门信息错误：20001-29999*/
	USER_NOT_LOGGED_IN(20001, "用户未登录"),
	USER_LOGIN_ERROR(20002, "用户名或密码错误"),
	USER_ACCOUNT_FORBIDDEN(20003, "账号已被禁用"),
	USER_NOT_EXIST(20004, "用户不存在"),
	USER_HAS_EXISTED(20005, "用户已存在"),
	USER_PASSWORD_ERROR(20006, "用户密码错误"),
	USER_NO_DEFAULT_LOGIN_ERROR(20007, "此用户存在多个单位或者部门，没有设置默认登录的单位或者部门"),
	LOGIN_CREDENTIAL_EXISTED(20008, "凭证已存在"),
	USER_TABLE_SORTED_IS_NULL(20009, "数据库用户表 order_by 字段全部为空"),
	USER_MOBILE_EXISTED(20010, "用户的手机号码已经在系统存在"),
	USER_CAPTCHA_ERROR(20011,"验证码错误"),
	USER_IS_LOCKED(20004, "用户被锁定"),
	PHONE_NOT_EXIST(20005, "手机号未在系统注册"),
	/* 流程错误 21000 ---22000 * */
	PROC_NODE_NO_FORM(21000, "当前流程节点没有设置表单项"),

	USER_ID_CARD_NO_EXISTED(20012, "身份证号码已存在"),
	USERNAME_HAS_EXISTED(20013, "用户名已存在"),




	/* 业务错误：30001-39999 */
	SPECIFIED_QUESTIONED_USER_NOT_EXIST(30001, "业务错误"),

	/* 系统错误：40001-49999 */
	SYSTEM_INNER_ERROR(40001, "系统繁忙，请稍后重试"),

	/* 数据错误：50000-599999 */
	SYSTEM_ERROR(50000, "服务器内部错误"),
	RESULE_DATA_NONE(50001, "数据未找到"),
	DATA_IS_WRONG(50002, "数据有误"),
	DATA_ALREADY_EXISTED(50003, "数据已存在"),
	DATA_CRMTENANTTYPE_EXISTED(50004, "数据已存在，业务类型只能唯一"),
	NO_MAINTARIFF_SURCHARGE(50005,"没有获取到主套餐和附套餐信息"),
	FILE_SAVE_ERROR(500100, "文件保存异常"),
	FILE_OVER_SIZE(500101, "文件超过大小限制"),

	/* 接口错误：60001-69999 */
	INTERFACE_INNER_INVOKE_ERROR(60001, "内部系统接口调用异常"),
	INTERFACE_OUTTER_INVOKE_ERROR(60002, "外部系统接口调用异常"),
	INTERFACE_FORBID_VISIT(60003, "该接口禁止访问"),
	INTERFACE_ADDRESS_INVALID(60004, "接口地址无效"),
	INTERFACE_REQUEST_TIMEOUT(60005, "接口请求超时"),
	INTERFACE_EXCEED_LOAD(60006, "接口负载过高"),

	/* 权限错误：70001-79999 */
	PERMISSION_NO_ACCESS(70001, "无访问权限"),
	RESOURCE_EXISTED(70002, "资源已存在"),
	RESOURCE_NOT_EXISTED(70003, "资源不存在"),


	APPSECRET_CHECK(90001, "验签失败"),
	PARAMS_CHECK(90003, "属性必填"),

	SIZE_TOO_LARGE(100001,"操作失败,子事项每天新增数量不能超过一个"),

	SYNC_DATA_FAIL(100002,"推送失败"),
	SYNC_DATA_PARTIAL(100003,"推送部分成功"),

	/* 数据不完整错误：80001-89999 */
	DATA_NOT_INTEGRITY(80001, "数据不完整，请联系管理员"),

	/* 拉取业务专用码段：120000-129999（全新独立段） */
	// 拉取系统错误（120000-120999）
	PULL_SYSTEM_ERROR(120000, "拉取系统内部错误"),
	PULL_SYSTEM_BUSY(120001, "拉取服务繁忙，请稍后再试"),
	PULL_SERVICE_UNAVAILABLE(120002, "拉取服务暂不可用"),
	PULL_DB_ERROR(120003, "拉取数据时数据库连接失败"),
	PULL_CACHE_ERROR(120004, "拉取缓存操作异常"),

	// 拉取权限错误（121000-121999）
	PULL_PERMISSION_DENIED(121000, "无拉取操作权限"),
	PULL_APP_CODE_INVALID(121001, "拉取应用编码无效或已过期"),
	PULL_TOKEN_EXPIRED(121002, "拉取令牌已过期"),
	PULL_TOKEN_INVALID(121003, "拉取令牌无效"),
	PULL_APP_CODE_NOT_EXIST(121004, "拉取应用信息不存在"),

	// 拉取参数错误（122000-122999）
	PULL_PARAM_NULL(122000, "拉取参数不能为空"),
	PULL_PARAM_INVALID(122001, "拉取参数格式无效"),
	PULL_HISTORY_ID_INVALID(122002, "拉取历史ID不存在或无效"),

	// 拉取业务错误（123000-123999）
	PULL_DATA_EMPTY(123000, "拉取数据为空"),
	PULL_TIMEOUT(123001, "拉取数据超时"),
	PULL_FREQUENCY_LIMIT(123002, "拉取频率超限"),
	PULL_DATA_SYNC_FAILED(123003, "拉取数据同步失败"),
	PULL_DATA_NOT_EXIST(123004, "拉取目标数据不存在"),
	PULL_DATA_DUPLICATE(123005, "拉取数据已存在，无法重复操作");

	private Integer code;

	private String message;

	ResultCode(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer code() {
		return this.code;
	}

	public String message() {
		return this.message;
	}

	public static String getMessage(String name) {
		for (ResultCode item : ResultCode.values()) {
			if (item.name().equals(name)) {
				return item.message;
			}
		}
		return name;
	}

	public static Integer getCode(String name) {
		for (ResultCode item : ResultCode.values()) {
			if (item.name().equals(name)) {
				return item.code;
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return this.name();
	}


}
