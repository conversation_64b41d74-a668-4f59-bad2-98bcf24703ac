package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.enums.RoleTypeEnum;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.*;
import com.ctsi.ssdc.admin.domain.dto.TAppRoleDTO;
import com.ctsi.ssdc.admin.domain.dto.TAppUserRoleDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpOrgRoleRepository;
import com.ctsi.ssdc.admin.repository.TAppRoleRepository;
import com.ctsi.ssdc.admin.service.*;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */

@Slf4j
@Service
public class TAppRoleServiceImpl extends SysBaseServiceImpl<TAppRoleRepository, TAppRole> implements ITAppRoleService {

    @Autowired
    private TAppRoleRepository tAppRoleMapper;

    @Autowired
    private ITAppUserRoleService itAppUserRoleService;

    @Autowired
    private ITAppRolePermissionService itAppRolePermissionService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private CscpOrgRoleRepository cscpOrgRoleRepository;
    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private WestoneEncryptService westoneEncryptService;
    @Autowired
    private CscpUserService cscpUserService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<TAppRoleDTO> queryListPage(TAppRoleDTO entityDTO, BasePageForm basePageForm) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        Long currentUserId = cscpUserDetail.getId();

        Integer rangeType = entityDTO.getRangeType();

        LambdaQueryWrapper<TAppRole> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(entityDTO.getLoginName()) || StringUtils.isNotBlank(entityDTO.getRealName())){
            List<CscpUser> cscpUsers = cscpUserService.selectListNoAdd(new LambdaQueryWrapper<CscpUser>()
                    .eq(StringUtils.isNotBlank(entityDTO.getLoginName()), CscpUser::getLoginName, entityDTO.getLoginName())
                    .eq(StringUtils.isNotBlank(entityDTO.getRealName()), CscpUser::getRealName, westoneEncryptService.isCipherMachine() ?
                            westoneEncryptService.divisionEncryptRealNameWithFPE(entityDTO.getRealName()) : this.division(entityDTO.getRealName()))
                    .eq(CscpUser::getDeleted, 0));

            if (CollectionUtils.isEmpty(cscpUsers)){
                return null;
            }
            List<Long> userIds = cscpUsers.stream().map(CscpUser::getId).collect(Collectors.toList());

            String formatUserIds = String.format("'%s'", StringUtils.join(userIds, "','"));
            queryWrapper.exists("select 1 from t_app_user_role ur where ur.user_id in (" + formatUserIds + ") and t_app_role.id = ur.app_role_id and ur.deleted = 0");
        }

        // 普通用户过滤角色处理
        if (rangeType != null && rangeType.intValue() == 10) {
            queryWrapper.eq(TAppRole::getRangeType, rangeType)
                    .like(StringUtils.isNotBlank(entityDTO.getName()), TAppRole::getName, entityDTO.getName())
                    .orderByDesc(TAppRole::getCreateTime);

            IPage<TAppRole> pageData = tAppRoleMapper.selectPage(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

            IPage<TAppRoleDTO> data = pageData.convert(entity -> {
                TAppRoleDTO dto = BeanConvertUtils.copyProperties(entity, TAppRoleDTO.class);
                // 设置可删除和编辑标识
                int canEdit = currentUserId.equals(entity.getCreateBy()) ? 1 : 0;
                dto.setCanEditDelete(canEdit);
                return dto;
            });
            // 批量设置组织信息
            setRoleOrgInfo(data.getRecords());
            return data;
        }

        // 根据用户角色类型查询数据
        if (SecurityUtils.isSystemName()) {
            // admin：展示所有admin创建的角色
            queryAdminRoles(entityDTO,queryWrapper);

        } else if (SecurityUtils.isRegionAdmin()) {
            // 区划管理员：展示admin创建的角色+当前账号创建的角色
            queryRegionAdminRoles(entityDTO, cscpUserDetail.getCompanyId(), queryWrapper);

        } else if (SecurityUtils.isUnitAdmin()) {
            // 单位管理员：展示admin创建的角色+当前单位所属区划的管理员新建的角色+当前账号创建的角色
            queryUnitAdminRoles(entityDTO, currentUserId, cscpUserDetail,queryWrapper);

        } else {
            // 其他情况保持原有逻辑
            LambdaQueryWrapper<TAppRole> queryWrapper2 = buildOtherUserQuery(entityDTO, cscpUserDetail);
            IPage<TAppRole> pageData = tAppRoleMapper.selectPageNoAdd(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper2);

            IPage<TAppRoleDTO> data = pageData.convert(entity -> {
                TAppRoleDTO dto = BeanConvertUtils.copyProperties(entity, TAppRoleDTO.class);
                int canEdit = currentUserId.equals(entity.getCreateBy()) ? 1 : 0;
                dto.setCanEditDelete(canEdit);
                return dto;
            });

            // 批量设置组织信息
            setRoleOrgInfo(data.getRecords());
            return data;
        }

        queryWrapper.orderByDesc(TAppRole::getCreateTime);
        IPage<TAppRole> page = tAppRoleMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        // 实现组合排序：系统管理员>区划管理员>单位管理员，同级别按创建时间倒序
        page.getRecords().sort((r1, r2) -> {
            // 获取角色类型优先级
            int priority1 = getRoleCreatorPriority(r1);
            int priority2 = getRoleCreatorPriority(r2);

            // 先按优先级排序
            if (priority1 != priority2) {
                return Integer.compare(priority1, priority2);
            }

            // 优先级相同，按创建时间倒序
            return r2.getCreateTime().compareTo(r1.getCreateTime());
        });

        // 转换为DTO并设置编辑删除权限
        List<TAppRoleDTO> dtoList = page.getRecords().stream().map(entity -> {
            TAppRoleDTO dto = BeanConvertUtils.copyProperties(entity, TAppRoleDTO.class);
            int canEdit = currentUserId.equals(entity.getCreateBy()) ? 1 : 0;
            dto.setCanEditDelete(canEdit);
            // 根据type设置typeLabel
            String typeLabel = "";
            if (entity.getType() != null) {
                switch (entity.getType()) {
                    case 0: // 单位定义
                        typeLabel = "单位管理员创建";
                        break;
                    case 4: // 区划定义
                        typeLabel = "区划管理员创建";
                        break;
                    case 2: // 系统定义
                    case 3: // 平台默认
                        typeLabel = "系统管理员创建";
                        break;
                    default:
                        typeLabel = "其他";
                }
            }
            dto.setTypeLabel(typeLabel);
            return dto;
        }).collect(Collectors.toList());

        // 批量设置组织信息
        setRoleOrgInfo(dtoList);
        Page<TAppRoleDTO> dtoPage = new Page<>();
        dtoPage.setRecords(dtoList);
        dtoPage.setTotal(page.getTotal());
        dtoPage.setSize(page.getSize());
        dtoPage.setCurrent(page.getCurrent());
        return dtoPage;
    }

    /**
     * 批量设置角色的组织信息
     *
     * @param dtoList 角色DTO列表
     */
    private void setRoleOrgInfo(List<TAppRoleDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        // 提取所有角色ID
        List<Long> roleIds = dtoList.stream()
                .map(TAppRoleDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(roleIds)) {
            dtoList.forEach(dto -> dto.setOrgList(new ArrayList<>()));
            return;
        }

        // 1. 查找所有角色下的用户角色关系
        LambdaQueryWrapper<TAppUserRole> userRoleWrapper = new LambdaQueryWrapper<>();
        userRoleWrapper.in(TAppUserRole::getAppRoleId, roleIds);
        List<TAppUserRole> userRoleList = itAppUserRoleService.selectListNoAdd(userRoleWrapper);

        if (CollectionUtils.isEmpty(userRoleList)) {
            dtoList.forEach(dto -> dto.setOrgList(new ArrayList<>()));
            return;
        }

        // 2. 收集所有departmentId（即orgId） 和 companyId
        Set<Long> orgIdSet = userRoleList.stream()
                .map(TAppUserRole::getDepartmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 3. 查询机构信息
        Map<Long, String> orgNameMap = new HashMap<>();
        if (!orgIdSet.isEmpty()) {
            LambdaQueryWrapper<CscpOrg> orgWrapper = new LambdaQueryWrapper<>();
            orgWrapper.in(CscpOrg::getId, orgIdSet)
                    .select(CscpOrg::getId, CscpOrg::getOrgName);
            List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(orgWrapper);
            orgNameMap = orgList.stream()
                    .collect(Collectors.toMap(CscpOrg::getId, CscpOrg::getOrgName, (k1, k2) -> k1));
        }

        // 4. 构建角色ID到机构信息列表的映射
        Map<Long, List<TAppRoleDTO.RoleOrgInfo>> roleOrgMap = new HashMap<>();
        for (TAppUserRole userRole : userRoleList) {
            Long roleId = userRole.getAppRoleId();
            Long orgId = userRole.getDepartmentId();
            String orgName = orgNameMap.get(orgId);
            if (orgId != null && orgName != null) {
                roleOrgMap.computeIfAbsent(roleId, k -> new ArrayList<>())
                        .add(new TAppRoleDTO.RoleOrgInfo(orgId, orgName));
            }
        }

        // 5. 去重（同一个角色下同机构可能有多个用户）
        for (Map.Entry<Long, List<TAppRoleDTO.RoleOrgInfo>> entry : roleOrgMap.entrySet()) {
            List<TAppRoleDTO.RoleOrgInfo> orgInfos = entry.getValue();
            Map<Long, TAppRoleDTO.RoleOrgInfo> orgInfoMap = orgInfos.stream()
                    .collect(Collectors.toMap(TAppRoleDTO.RoleOrgInfo::getOrgId, o -> o, (o1, o2) -> o1));
            entry.setValue(new ArrayList<>(orgInfoMap.values()));
        }

        // 6. 设置到dto
        dtoList.forEach(dto -> {
            List<TAppRoleDTO.RoleOrgInfo> orgInfoList = roleOrgMap.get(dto.getId());
            dto.setOrgList(orgInfoList != null ? orgInfoList : new ArrayList<>());
        });
    }

    /**
     * 查询admin创建的角色
     */
    private void queryAdminRoles(TAppRoleDTO entityDTO,LambdaQueryWrapper<TAppRole>  queryWrapper) {

        // 查询系统管理员创建的角色(type=2,3)
        queryWrapper.in(TAppRole::getType, Arrays.asList(
                        RoleTypeEnum.RoleTypeEnum_2.getCode(),
                        RoleTypeEnum.RoleTypeEnum_3.getCode()))
                .like(StringUtils.isNotBlank(entityDTO.getName()), TAppRole::getName, entityDTO.getName());

        if (entityDTO.getId() != null) {
            queryWrapper.eq(TAppRole::getId, entityDTO.getId());
        }

    }

    /**
     * 查询区划管理员可见的角色
     */
    private void queryRegionAdminRoles(TAppRoleDTO entityDTO, Long currentCompanyId,LambdaQueryWrapper<TAppRole>  queryWrapper) {
        // admin创建的角色(type=2,3) + 当前账号创建的角色
        queryWrapper.and(wrapper ->
                        wrapper.in(TAppRole::getType, Arrays.asList(
                                        RoleTypeEnum.RoleTypeEnum_2.getCode(),
                                        RoleTypeEnum.RoleTypeEnum_3.getCode()))
                                .or().eq(TAppRole::getCompanyId, currentCompanyId))
                .like(StringUtils.isNotBlank(entityDTO.getName()), TAppRole::getName, entityDTO.getName());

        if (entityDTO.getId() != null) {
            queryWrapper.eq(TAppRole::getId, entityDTO.getId());
        }

    }

    /**
     * 查询单位管理员可见的角色
     */
    private void queryUnitAdminRoles(TAppRoleDTO entityDTO, Long currentUserId, CscpUserDetail cscpUserDetail,LambdaQueryWrapper<TAppRole>  queryWrapper) {
        // admin创建的角色(type=2,3) + 区划管理员创建的角色(type=4) + 当前账号创建的角色

        // 解析 orgIdPathList
        List<Long> orgIdPathList = new ArrayList<>();
        if (StringUtils.isNotBlank(cscpUserDetail.getOrgIdPath())) {
            orgIdPathList = Arrays.stream(cscpUserDetail.getOrgIdPath().split("\\|"))
                    .filter(StringUtils::isNotBlank)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }

        List<Long> finalOrgIdPathList = orgIdPathList;
        queryWrapper.and(wrapper ->{
                        // 1. admin 创建的角色(type=2,3)
                        wrapper.in(TAppRole::getType, Arrays.asList(RoleTypeEnum.RoleTypeEnum_2.getCode(),RoleTypeEnum.RoleTypeEnum_3.getCode()));
                        // 2. 区划管理员创建的角色(type=4) 并且 departmentId 在 orgIdPathList 中
                        if (!finalOrgIdPathList.isEmpty()) {
                            wrapper.or(w -> w.eq(TAppRole::getType, RoleTypeEnum.RoleTypeEnum_4.getCode())
                                    .in(TAppRole::getRegionId, finalOrgIdPathList));
                        }
                        // 3. 当前账号创建的角色
                        wrapper.or().eq(TAppRole::getCompanyId, cscpUserDetail.getCompanyId());
        });

        if (StringUtils.isNotBlank(entityDTO.getName())) {
            queryWrapper.like(TAppRole::getName, entityDTO.getName());
        }

        if (entityDTO.getId() != null) {
            queryWrapper.eq(TAppRole::getId, entityDTO.getId());
        }

    }

    /**
     * 构建其他用户的查询条件（保持原有逻辑）
     */
    private LambdaQueryWrapper<TAppRole> buildOtherUserQuery(TAppRoleDTO entityDTO, CscpUserDetail cscpUserDetail) {
        LambdaQueryWrapper<TAppRole> queryWrapper = new LambdaQueryWrapper<>();

        if (SecurityUtils.isTenantName()) {
            // 租户登录
            queryWrapper.eq(TAppRole::getTenantId, cscpUserDetail.getTenantId());
            if (entityDTO.getType() != null) {
                queryWrapper.eq(TAppRole::getType, entityDTO.getType());
            } else {
                queryWrapper.eq(TAppRole::getTenantId, cscpUserDetail.getTenantId())
                        .eq(TAppRole::getType, RoleTypeEnum.RoleTypeEnum_1.getCode())
                        .like(StringUtils.isNotBlank(entityDTO.getName()), TAppRole::getName, entityDTO.getName())
                        .or().eq(TAppRole::getType, RoleTypeEnum.RoleTypeEnum_2.getCode())
                        .like(StringUtils.isNotBlank(entityDTO.getName()), TAppRole::getName, entityDTO.getName());
            }
        } else {
            // 单位登录获取单位的，租户的，系统除默认之外的
            if (entityDTO.getType() != null) {
                queryWrapper.eq(TAppRole::getType, entityDTO.getType());
            } else {
                queryWrapper.eq(TAppRole::getType, RoleTypeEnum.RoleTypeEnum_2.getCode())
                        .like(StringUtils.isNotBlank(entityDTO.getName()), TAppRole::getName, entityDTO.getName())
                        .or().eq(TAppRole::getTenantId, cscpUserDetail.getTenantId())
                        .eq(TAppRole::getCompanyId, cscpUserDetail.getCompanyId())
                        .isNull(TAppRole::getMainTariff).isNull(TAppRole::getSurcharge);
            }
        }

        if (entityDTO.getId() != null) {
            queryWrapper.eq(TAppRole::getId, entityDTO.getId());
        }

        queryWrapper.orderByDesc(TAppRole::getCreateTime);

        return queryWrapper;
    }

    /**
     * 获取角色的优先级
     * 1: 系统管理员创建的角色(type=2,3), 2: 区划管理员创建的角色(type=4), 3: 单位管理员创建的角色(type=0), 4: 其他(type=1)
     */
    private int getRoleCreatorPriority(TAppRole role) {
        Integer type = role.getType();
        if (type == null) {
            return 4; // 其他
        }

        switch (type) {
            case 2: // 系统定义
            case 3: // 表示整个系统的，只能有系统管理员操作的，平台默认的
                return 1; // 系统管理员
            case 4: // 区划定义
                return 2; // 区划管理员
            case 0: // 单位定义
                return 3; // 单位管理员
            case 1: // 租户定义
            default:
                return 4; // 其他
        }
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TAppRoleDTO> queryList(TAppRoleDTO entityDTO) {
        LambdaQueryWrapper<TAppRole> queryWrapper = new LambdaQueryWrapper();
            List<TAppRole> listData = tAppRoleMapper.selectList(queryWrapper);
            List<TAppRoleDTO> TAppRoleDTOList = ListCopyUtil.copy(listData, TAppRoleDTO.class);
        return TAppRoleDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TAppRoleDTO findOne(Long id) {
        TAppRole  tAppRole =  tAppRoleMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tAppRole,TAppRoleDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TAppRoleDTO create(TAppRoleDTO entityDTO) {
        if (SecurityUtils.isSystemName()) {
            entityDTO.setType(RoleTypeEnum.RoleTypeEnum_2.getCode());
        } else if (SecurityUtils.isTenantName()) {
            entityDTO.setType(RoleTypeEnum.RoleTypeEnum_1.getCode());
        } else if (SecurityUtils.isRegionAdmin())  {
            entityDTO.setType(RoleTypeEnum.RoleTypeEnum_4.getCode());
        }else {
            entityDTO.setType(RoleTypeEnum.RoleTypeEnum_0.getCode());
        }
        this.updateSort(entityDTO);
        TAppRole tAppRole =  BeanConvertUtils.copyProperties(entityDTO,TAppRole.class);
        tAppRole.setRegionId(SecurityUtils.getCurrentCompanyId());
        save(tAppRole);
        return BeanConvertUtils.copyProperties(tAppRole,TAppRoleDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TAppRoleDTO entity) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        int result = tAppRoleMapper.delete(new LambdaQueryWrapper<TAppRole>()
                .eq(TAppRole::getId, entity.getId()).eq(TAppRole::getCreateBy, currentUserId));
        if (result == 0) {
            throw new BusinessException("只能删除自己创建的角色");
        }
        this.updateSort(entity);
        TAppRole tAppRole = BeanConvertUtils.copyProperties(entity,TAppRole.class);
        return tAppRoleMapper.updateById(tAppRole);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        // 查询与改该角色是否存在关联的用户
        TAppUserRoleDTO tAppUserRoleDTO = new TAppUserRoleDTO();
        tAppUserRoleDTO.setAppRoleId(id);
        List<TAppUserRoleDTO> tAppUserRoleDTOList = itAppUserRoleService.queryList(tAppUserRoleDTO);
        if (CollectionUtils.isNotEmpty(tAppUserRoleDTOList)) {
            throw new BusinessException("该角色关联其他用户，不能删除！");
        }

//        // 查询是否有功能模块与该角色相关联
//        List<TAppRolePermissionDTO> tAppPermissionByRoleId = itAppRolePermissionService.getTAppPermissionByRoleId(id);
//        if (CollectionUtils.isNotEmpty(tAppPermissionByRoleId)) {
//            throw new BusinessException("该角色关联其他功能模块，不能删除！");
//        }
        int result = tAppRoleMapper.delete(new LambdaQueryWrapper<TAppRole>()
                .eq(TAppRole::getId, id).eq(TAppRole::getCreateBy, SecurityUtils.getCurrentUserId()));
        if (result == 0) {
            throw new BusinessException("只能删除自己创建的权限角色");
        }

        return result;
    }


    /**
     * 验证是否存在
     *
     * @param TAppRoleId
     * @return
     */
    @Override
    public boolean existByTAppRoleId(Long TAppRoleId) {
        if (TAppRoleId != null) {
            LambdaQueryWrapper<TAppRole> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TAppRole::getId, TAppRoleId);
            List<TAppRole> result = tAppRoleMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional
    public Boolean insertBatch(List<TAppRoleDTO> dataList) {
        List<TAppRole> result = ListCopyUtil.copy(dataList, TAppRole.class);
        return saveBatch(result);
    }

    @Override
    public List<TAppRoleDTO> queryRolesByTenantId(Long tenantId) {
        LambdaQueryWrapper<TAppRole> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TAppRole::getTenantId, tenantId);
        List<TAppRole> cscpRolesList = tAppRoleMapper.selectList(queryWrapper);
        return ListCopyUtil.copy(cscpRolesList, TAppRoleDTO.class);
    }

    @Override
    public List<TAppRoleDTO> queryAppRolesByIdList(List<Long> appRoleIdList) {
        List<TAppRole> tAppRoleList = tAppRoleMapper.selectBatchIds(appRoleIdList);
        return ListCopyUtil.copy(tAppRoleList, TAppRoleDTO.class);
    }

    /**
     * 排序号自增(按公司分组)
     * @param entity
     * @return
     */
    @Override
    public Boolean updateSort(TAppRoleDTO entity){
        //排序号分组情况不明确，先整体自增
        LambdaQueryWrapper<TAppRole> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TAppRole::getSort,entity.getSort());
        int count = tAppRoleMapper.selectCount(lambdaQueryWrapper);
        if (count > 0){
            tAppRoleMapper.updataSort(SortEnum.builder()
                    .sort(entity.getSort())
                    .id(entity.getId())
                    .tableName("t_app_role")
                    .sortName("sort")
                    .additionOrsubtraction("+")
                    .build());
        }
        return true;
    }

    public String division(String realName){
        String realNames = "";
        if (StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames.replace(",","");
    }

    @Override
    @Transactional
    public boolean insertAppOrgRoles(Long roleId, List<Long> orgIdList) {
        for (Long orgId : orgIdList) {
            LambdaQueryWrapper<CscpUserOrg> userOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userOrgLambdaQueryWrapper.eq(CscpUserOrg::getOrgId, orgId);
            List<CscpUserOrg> userOrgs = cscpUserOrgService.selectListNoAdd(userOrgLambdaQueryWrapper);
            if (!userOrgs.isEmpty()) {
                List<Long> userIds = userOrgs.stream().map(CscpUserOrg::getUserId).collect(Collectors.toList());
                List<Long> insertUserIds = new ArrayList<>();
                userIds.stream().forEach(o -> {
                    LambdaQueryWrapper<TAppUserRole> userRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    userRoleLambdaQueryWrapper.eq(TAppUserRole::getAppRoleId, roleId);
                    userRoleLambdaQueryWrapper.eq(TAppUserRole::getUserId, o);
                    List<TAppUserRole> tAppUserRoles = itAppUserRoleService.selectListNoAdd(userRoleLambdaQueryWrapper);
                    if (tAppUserRoles.isEmpty()) {
                        insertUserIds.add(o);
                    }
                });
                List<TAppUserRole> collect = new ArrayList<>();
                for (Long userId : insertUserIds) {
                    List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(new LambdaQueryWrapper<CscpUserOrg>()
                            .eq(CscpUserOrg::getUserId, userId)
                            .eq(CscpUserOrg::getDeleted, 0));

                    for (CscpUserOrg cscpUserOrg : cscpUserOrgs) {
                        TAppUserRole tAppUserRole = new TAppUserRole();
                        tAppUserRole.setAppRoleId(roleId);
                        tAppUserRole.setUserId(userId);
                        tAppUserRole.setCompanyId(cscpUserOrg.getCompanyId());
                        tAppUserRole.setDepartmentId(cscpUserOrg.getOrgId());
                        collect.add(tAppUserRole);
                    }
                }
                itAppUserRoleService.saveBatch(collect);
            }
        }
        return true;
    }
}
