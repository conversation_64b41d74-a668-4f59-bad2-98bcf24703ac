package com.ctsi.ssdc.admin.service;


import com.ctsi.hndx.addrbook.entity.TLabelOrg;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.tree.TreeSelectService;
import com.ctsi.ssdc.admin.domain.CscpMainOrgVO;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.domain.vo.MainOrgVO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * Service Interface for managing CscpOrg.
 *
 * <AUTHOR> biyi generator
 */
public interface CscpOrgService extends TreeSelectService<CscpOrgDTO, CscpOrg> {

    /**
     * insert a cscpOrg.
     *
     * @param cscpOrgDTO the entity to insert
     * @return the persisted entity
     */
    CscpOrgDTO insert(CscpOrgDTO cscpOrgDTO);


    /**
     * update a cscpOrg.
     *
     * @param cscpOrgDTO the entity to update
     * @return the persisted entity
     */
    List<Long>  update(CscpOrgDTO cscpOrgDTO);

    /**
     * save cscporg and cscporg_user cscp_org_workGroup
     */
    CscpOrgDTO save(CscpOrgDTO cscpOrgDTO);


    CscpOrgParamDTO fetchCscpOrgsUpdate(Long parent_id);

    /**
     * Get all the cscpOrgs.
     *
     * @return the list of entities
     */
    PageResult<CscpOrgDTO> findAll();

    /**
     * Get the  cscpOrg.
     *
     * @param id the id of the entity
     * @return the entity
     */
    CscpOrgDTO findOne(Long id);

    /**
     * Get the cscpOrgs.
     *
     * @return the list of entities
     */
    PageResult<CscpOrgDTO> findByCscpOrgDTO(CscpOrgDTO cscpOrgDTO, BasePageForm basePageForm);


    List<CscpOrg> selectRoleslist(Long id);

    Long deleteOrgById(Long id);


    /**
     * 查询该机构ID的所有上级ID
     *
     * @param id
     * @return
     */
    List<Long> listQryParentOrgId(Long id);

    /**
     * 条件查询组织机构
     *
     * @param orgDTO
     * @return
     */
    List<CscpOrgDTO> criteriaQueryOrgDTO(CscpOrgDTO orgDTO);

    List<CscpOrgDTO> criteriaQueryOrgDTONew(CscpOrgDTO orgDTO, String flag, int size);

    /**
     * 查询该机构已勾选的节点树(只包含已选择的节点)
     *
     * @param companyId
     * @param ids
     * @return
     */
    List<Node<CscpOrgDTO>> selectCheckedOrgNodeTree(Long companyId, List<Long> ids);


    /**
     * 查询指定机构的用户，查询部门信息
     * param [id]
     * return java.util.List<com.ctsi.ssdc.admin.domain.CscpUserDTO>
     * createTime 2021/4/13 10:14
     **/
    List<CscpUserDTO> selectUserlist(Long id);


    /**
     * 根据单位id获取单位下面的所有用户,传null的话自动取本单位的
     *
     * @return
     */
    List<CscpUserDTO> selectCompayAllUserByCompanyId(Long companyId);

    /**
     * 根据单位id获取单位下面的所有用户的单位id、单位名称、部门id、部门名称
     *
     * @param companyId 单位id
     * @return CscpUserDTO
     */
    List<CscpUserDTO> selectUserDetailByCompanyId(Long companyId);

    /**
     * 分页查询单位下的用户
     *
     * @param id
     * @param realName
     * @param basePageForm
     * @param userIds
     * @return
     */
    PageResult<CscpUserDTO> pageQueryUserList(Long id, String realName, List<Long> userIds, BasePageForm basePageForm);


    /**
     * 分页查询单位下的用户(不区分是否显示和是否停用)
     *
     * @param id
     * @param realName
     * @param basePageForm
     * @return
     */
    PageResult<CscpUserDTO> pageSelectUsers(Long id, String realName, BasePageForm basePageForm);


    List<CscpOrgDTO> selectChildrenListParentId(Long parentId);

    List<CscpOrgDTO> selectLabelParentOrg(List<Long> labelId);

    List<CscpOrgDTO> selectLabelOrg(List<Long> orgId);

    /**
     * 根据父级机构id查询机构
     */
    List<TLabelOrg> selectParentIdOrg(Long orgId, Long labelId);

    Map<Long, TLabelOrg> selectBatchParentIdOrg(List<Long> orgIds, Long labelId);

    /**
     * 查询下级机构是否存在
     * */
    Boolean selectSubOrgExistFlag(Long orgId, Long labelId);

    /**
     * 查询下级内设机构人数
     * */
    Integer selectSubDeptOrgCount(Long orgId);

    List<CscpOrgDTO> selectSubOrgList(Long parentId, Executor executor);

    /**
     * 查询指定租户下面的所有单位
     *
     * @return
     */
    List<Node<CscpOrgDTO>> selectTenantOrgPage(Long id);

    /**
     * 查询租户下面的所有组织机构，包括单位、虚拟机构和部门
     *
     * @param tenantId
     * @return
     */
    List<Node<CscpOrgDTO>> selectOrgTreeByTenantId(Long tenantId);

    /**
     * 查询单位下的部门
     *
     * @param parentId
     * @param tenantId
     * @return
     */
    List<CscpOrgDTO> selectSubordinateAllOrg(Long parentId, Long tenantId);


    /**
     * 查询单位下面的用户信息
     *
     * @param realName
     * @param basePageForm
     * @return
     */
    PageResult<CscpUserDTO> queryTenantUser(String realName, BasePageForm basePageForm);

    /**
     * 查询单位下所有部门信息(只查和该单位的直接部门或子部门，不会遍历该单位下的虚拟机构和子单位的部门)
     *
     * @param companyId
     * @return
     */
    List<CscpOrgDTO> getAllChildDepartmentList(Long companyId);

    /**
     * 查询子节点
     *
     * @param orgId
     * @return
     */
    List<CscpOrgDTO> getChildOrgList(Long orgId, Integer type);

    /**
     * 查询本单位所有机构(限普通用户)
     *
     * @param parentId
     * @return
     */
    List<Node<CscpOrgDTO>> seletThisUnitAllDepartment(Long parentId, List<Long> ids);

    /**
     * 根据id查询所有下级机构,id为0时查询所有顶层单位
     *
     * @param id
     * @return
     */
    List<CscpOrgDTO> selectOrgById(Long id);
    List<CscpOrgDTO> selectOrgById(Long id,String queryType, String orgCodePath, String orgName);

    List<CscpOrgDTO> selectOrgCityState();

    List<CscpOrgDTO> getOrgWithAllParents(String orgName);

    /**
     * 只能使用在本租户或者本单位中查询所有机构信息,不需要勾选已经选择的节点，带用户名搜索
     *
     * @param parentId
     * @param realName
     * @return
     */
    CscpOrgAndUserDto selectOrgAndQueryRealNameList(Long parentId, String realName, Integer onlyDepart);

    /**
     * 查询实际考核人数
     *
     * @return
     */
    List<CscpOrgDTO> selectAssessmentPeopleCount();

    /**
     * 修改实际考核人数
     *
     * @param cscpAssessmentPeopleCountDTOS
     * @return
     */
    Boolean updateAssessmentPeopleCount(List<CscpAssessmentPeopleCountDTO> cscpAssessmentPeopleCountDTOS);

    //CRM新增单位
    CscpOrgDTO crmSave(CscpOrgDTO cscpOrgDTO);

    //CRM修改单位
    int crmUpdate(CscpOrgDTO cscpOrgDTO);

    /**
     * 获得通讯录所有机构id
     * @param orgId
     * @return
     */
    List<Long> getAllChildOrgIds(Long orgId,List<Long> labelIds);

    /**
     * 根据租户id 查询所有单位 平级展示所有单位信息
     *
     * @param tenantId
     * @return
     */
    List<CscpOrgDTO> selectAllCompany(Long tenantId);

    List<CscpOrgDTO> selectAllCompanyByOrgName(String orgName);

    /**
     * 此接口用来解决，办公厅下面机要局，机要局下面信息中心，但是要取值信息中心
     * 先找人说在的部门，如果上面还有部门，取上面部门信息，如果上面时单位，取单位信息
     * @return
     */
    CscpOrgDTO getCurrentParentOrg();

    /**
     * 获取本人最上级单位名称
     *
     * @param userId
     * @return 响应参数
     */
    CscpOrg getTopCompanyIdByUserId(Long userId);

    /**
     * 在本租户或者本单位中查询所有机构信息(根据角色判断)
     * 可带上用户姓名或部门id去查询
     * @param req
     * @return
     */
    CscpOrgAndUserDto selectTenantTreeOrgAndUser(CscpTenantOrgTreeQueryDTO req);

    /**
     * 根据id 查下一级子单位/机构
     * @param id
     * @return
     */
    List<CscpOrg> selectOrgByParentId(Long id);

    void addService(CscpOrg vo);

    /**
     * 创建单位管理员
     * @param orgCode
     */
    void createCompanyAdmin(String orgCode);

    /**
     * 创建单位管理员
     * @param orgCodePath
     */
    void createCompanyAdmin2(String orgCodePath);

    /**
     * 区划机构管理查询机构列表
     * @param id
     * @return
     */
    List<CscpOrgDTO> selectDivisionOrgById(Long id, String orgCodePath);


    /**
     * 区划机构管理查询机构列表
     *
     * @param id
     * @param orgCodePath
     * @return
     */
    List<CscpOrgDTO> selectDivisionOrgById_new(Long id, String orgCodePath);


    /**
     * 对于同一parent_id的记录，取第一条level不为null的值，
     * 并赋给其他level为null的记录
     *
     * @return 更新的记录数
     */
    Integer processNullLevels();

    /**
     * 初始化org_id_path、org_code_path字段
     */
    void initOrgPath();

    List<CscpOrgDTO> getOrgWithAllParentsNew(String orgName);

    List<CscpOrgDTO> getOrgWithAllParentsNewest(String orgName, Long orgId, Integer allFlag);

    List<CscpOrgDTO> selectOrgListById(Long id);

    List<CscpOrg> selectOrgStartWith(Long id);

    void updateInvalidOrgCodes();

    MainOrgVO queryMainOrgTree();

    List<CscpOrgDTO> selectDivisionOrgByIdName(Long id, String orgName);

    void updateOrgOrderBy(List<CscpOrgDTO> dataList);

    List<CscpOrgDTO> selectAllChildNodesListById(List<Long> ids);

    List<CscpMainOrgVO> queryUnitOrgTree(Long parentId);

    CscpOrg getMaxCodeCscpOrg(CscpOrgDTO cscpOrgDTO);

    MainOrgVO queryUnitOrgTreeByName(String orgName);


    /**
     * 查询已经删除了的机构
     * @param orgName
     * @param orgCode
     * @param basePageForm
     * @return
     */
    PageResult<CscpOrgDTO> selectDeletedList(String orgName,String orgCode,BasePageForm basePageForm);

    boolean deleteRealById(Long id);

    List<CscpOrgDTO> selectOrgTreeByParentId(Long id);

    List<CscpOrg> selectChildrenByCondition(Long orgId, List<String> orgNameList);

    CscpOrgDTO selectOrgCode(String orgCode);


    List<CscpOrgDTO> selectOrgByParentIdAddressBook(Long parentId);

    Map<String, Object> getUserAdminAndCreditCodeStatus();

    boolean updateCreditCode(String creditCode);

    void updateAppCodesById(Long orgId, String appCodes);

    /**
     * 根据规则动态查询湖南省所有市州的机构信息
     * 规则: 机构编码以'43'开头, 且第3-4位不为'00', 5-12位为'00000000'
     *
     * @return 匹配的机构实体列表
     */
    List<CscpOrgDTO> selectOrgRegionCityList();

    /**
     * 根据机构id获取单位id
     *
     * @param orgId 机构id
     * @return 单位id
     */
    Long getUnitIdByOrgId(Long orgId);

    /**
     * 校验统一社会信用代码是否唯一
     * @param id
     * @param creditCode
     * @return
     */
    boolean checkCreditCode(Long id, String creditCode);

    /**
     * 批量修改PushAppCode
     * @param ids
     * @param appCode
     */
    void updateLocateAppCodeBatch(List<Long> ids, String appCode);

    /**
     * 查询指定内设机构所属的单位信息，并设置单位ID和统一社会信用代码到传入的cscpOrg对象中。
     * 若未找到或查询异常，则清空相关字段并记录日志。
     *
     * @param cscpOrg 内设机构对象，type应为3
     */
    void handleOrgBelongCompanyInfo(CscpOrg cscpOrg);

    List<CscpOrg> selectOrgHasDeleted(List<Long> ids);
}
