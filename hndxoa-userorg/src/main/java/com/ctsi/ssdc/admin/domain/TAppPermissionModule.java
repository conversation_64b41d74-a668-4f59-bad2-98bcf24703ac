package com.ctsi.ssdc.admin.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 权限管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Data
@ApiModel(value="TAppPermissionModule对象", description="移动端单元模块表")
public class TAppPermissionModule implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * App功能模块code
     */
    @ApiModelProperty(value = "App功能模块code")
    private String code;

    /**
     * App功能模块所属类型
     */
    @ApiModelProperty(value = "App功能模块所属类型")
    private String type;


    /**
     * App功能模块名称
     */
    @ApiModelProperty(value = "App功能模块名称")
    private String name;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private Long parentId;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    private String icon;


    /**
     * 功能所属系统   1.本系统  2.内部其他系统  3.第三方系统
     */
    @ApiModelProperty(value = "功能所属系统")
    private Integer systemType;

    /**
     * 跳转地址
     */
    @ApiModelProperty(value = "跳转地址")
    private String directUrl;


    /**
     * 是否添加角标  0不加 1加
     */
    @ApiModelProperty(value = "是否添加角标  0不加 1加")
    private Integer addMark;

    /**
     * 是否通用模块：0-否，1-是
     */
    @ApiModelProperty(value = "是否通用模块：0-否，1-是")
    private Integer commonFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String  remark;

    /**
     * 报表相关地址
     */
    @ApiModelProperty(value = "报表相关地址")
    private String bizReportUrl;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private String createName;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.UPDATE,select = false)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.UPDATE,select = false)
    private Long updateBy;

    @TableField(fill = FieldFill.UPDATE,select = false)
    private String updateName;

    @TableLogic
    @TableField(select = false,fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 角色类型 1:admin 2:平台管理员角色 3:租户管理员角色 4:区划管理员角色 5:单位管理员角色 6:其他角色
     */
    private Integer roleType;

    @TableField(fill = FieldFill.INSERT)
    private Long departmentId;

    @TableField(fill = FieldFill.INSERT)
    private Long companyId;

}
