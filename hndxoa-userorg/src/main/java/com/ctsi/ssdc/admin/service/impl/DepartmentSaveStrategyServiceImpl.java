package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.enums.OrgTypeEnum;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DepartmentSaveStrategyServiceImpl extends BaseOrgSaveStrategyServiceImpl {
    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;


    @Override
    public Boolean verifyOrgParam(CscpOrgDTO cscpOrgDTO) {
        long uid = SecurityUtils.getCurrentUserId();
//        if (!SecurityUtils.isGeneralName()){
//            throw new BusinessException("只能由单位创建部门");
//        }
//        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
//            throw new BusinessException("顶级机构只能是单位");
//        }
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        Set<Integer> set = new HashSet<>();
        set.add(OrgTypeEnum.ORG_TYPE_2.getCode());
        set.add(OrgTypeEnum.ORG_TYPE_3.getCode());
//        if (Objects.isNull(parentOrg) || !set.contains(parentOrg.getType())) {
//            throw new BusinessException("部门的上级节点只能是单位或部门");
//        }
        return true;
    }


    @Override
    public Map<String, String> getOrgCodeAndPathCode(CscpOrgDTO cscpOrgDTO) {
        CscpOrg parentOrg;
        if(cscpOrgDTO.getParentId().equals(0L)){
            LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.orderByDesc(CscpOrg::getMaxNumber);
            queryWrapper.last("limit 1");
            parentOrg = cscpOrgRepository.selectOneNoAdd(queryWrapper);
        }else {
            //插入部门的父级信息
            parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        }
        if (StringUtils.isBlank(parentOrg.getOrgCode()) && StringUtils.isBlank(parentOrg.getOrgCode())) {
            throw new BusinessException("部门上级节点的单位编码和单位完整编码不能为空");
        }
        // String parentOrgCode = parentOrg.getCode();
        String parentOrgPathCode = parentOrg.getOrgCode();
        String parentOrgCodePath = parentOrg.getOrgCodePath();
        String parentOrgIdPath = parentOrg.getOrgIdPath();

        // 获取该父节点下的部门最大orgCode + 1
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpOrg::getParentId, parentOrg.getId());
        queryWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isEmpty(orgList)) {
            String curOrgCode = parentOrgPathCode;
            String curOrgCodePath = curOrgCode;
            String curOrgIdPath = StringUtils.isEmpty(parentOrgIdPath) ? parentOrg.getId().toString()
                    : parentOrgIdPath + "|" + parentOrg.getId();
            return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
        }
        String curOrgCode = parentOrgPathCode;
        String curOrgCodePath = parentOrgCodePath + "|" + cscpOrgDTO.getOrgCode();
        String curOrgIdPath = StringUtils.isEmpty(parentOrgIdPath) ? parentOrg.getId().toString()
                : parentOrgIdPath + "|" + parentOrg.getId();
        return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);

    }

    @Override
    public Map<String, String> getRegionCode(CscpOrgDTO cscpOrgDTO) {
        return this.getRegionCode_new(cscpOrgDTO);
    }

    @SuppressWarnings("all")
    public Map<String, String> getRegionCode_new(CscpOrgDTO cscpOrgDTO) {
        //获取上级机构信息
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        // 获取同级中最后一个机构信息
        CscpOrg org = cscpOrgService.getMaxCodeCscpOrg(cscpOrgDTO);
        // 如果是顶级单位
        if (cscpOrgDTO.getParentId().equals(0L)) {
            String orgCode = ROOT_ORG_CODE_DEETAULT;
            if (org != null) orgCode = org.getOrgCode();
            String strOrgCode = org.getOrgCode().trim();
            BigInteger bigInteger = new BigInteger(strOrgCode);
            BigInteger resultCode = bigInteger.add(BigInteger.ONE);
            String orgCodeStr = resultCode.toString();
            String maxNumber = orgCodeStr.substring(orgCodeStr.length() - 4);
            maxNumber = Integer.valueOf(maxNumber).toString();
            String curOrgCodePath = orgCodeStr;
            String curOrgIdPath = "";
            return this.regionMap("", orgCodeStr, maxNumber, curOrgCodePath, curOrgIdPath, "");
        }
        String orgCode = "";
        String strMaxNumber = "";
        if(org == null){
            orgCode = parentOrg.getOrgCode() + "0001";
            strMaxNumber = "1";
        }else{
            String strOrgCode = org.getOrgCode().trim();
            BigInteger bigInteger = new BigInteger(strOrgCode);
            BigInteger resultCode = bigInteger.add(BigInteger.ONE);
            String orgCodeStr = resultCode.toString();
            String maxNumber = orgCodeStr.substring(orgCodeStr.length() - 4);
            orgCode = orgCodeStr;
            strMaxNumber = String.valueOf(maxNumber);
        }
        String curOrgCodePath = StringUtils.isEmpty(parentOrg.getOrgCodePath()) ? orgCode : (parentOrg.getOrgCodePath() + "|" + orgCode);
        String curOrgIdPath = StringUtils.isEmpty(parentOrg.getOrgIdPath()) ? parentOrg.getId().toString()
                : parentOrg.getOrgIdPath() + "|" + parentOrg.getId();
        String orderBy = parentOrg.getOrderBy() != null ? (100000 + parentOrg.getOrderBy()) + "" : "";
        return this.regionMap(parentOrg.getRegionCode(), orgCode,strMaxNumber, curOrgCodePath, curOrgIdPath, orderBy);
    }

    @Override
    public CscpOrgDTO getCompanyId(CscpOrgDTO cscpOrgDTO) {
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        int i = 0;
        //或者找到父节点，或者直接报异常
        while (!OrgTypeEnum.ORG_TYPE_2.getCode().equals(parentOrg.getType())) {
            parentOrg = cscpOrgRepository.selectById(parentOrg.getParentId());
            i++;
            if (i >= 5) {
                throw new BusinessException("找不到内设机构的单位");
            }
            if (null == parentOrg) {
                throw new BusinessException("找不到内设机构的单位");
            }
        }
        cscpOrgDTO.setCompanyId(parentOrg.getId());
        return cscpOrgDTO;
    }

    @Override
    public boolean updateOrgSort(CscpOrgDTO cscpOrgDTO) {
        // 先查询当前单位下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
//        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCountNoAdd(lambdaQueryWrapper);

        // 如果存在，则把该单位下的大于等于该排序的所有部门排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            cscpOrgRepository.selectListNoAdd(queryWrapper).stream().map(i -> {
                i.setOrderBy(i.getOrderBy() + 1);
                return cscpOrgRepository.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }

    /**
     * 新增用户部门关系
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    @Transactional
    public Boolean saveUserOrgRel(CscpOrgDTO cscpOrgDTO) {
        // 部门添加分管领导
        if (!Objects.isNull(cscpOrgDTO.getBranchLeaderId())) {
            // 先删除之前与该用户部门关联的数据
            LambdaUpdateWrapper<CscpOrg> branchUpdateWrapper = Wrappers.lambdaUpdate();
            branchUpdateWrapper.eq(CscpOrg::getId, cscpOrgDTO.getId()).set(CscpOrg::getBranchLeaderId, null);
            cscpOrgService.update(null, branchUpdateWrapper);

            // 再新增记录
            LambdaUpdateWrapper<CscpOrg> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(CscpOrg::getId, cscpOrgDTO.getId())
                    .set(CscpOrg::getBranchLeaderId, cscpOrgDTO.getBranchLeaderId());
            cscpOrgService.update(null, updateWrapper);

        }
        return true;
    }

}
