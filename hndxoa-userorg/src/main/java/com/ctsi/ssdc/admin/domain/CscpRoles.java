package com.ctsi.ssdc.admin.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.enums.RoleTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> Generator
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="cscp_roles", description="角色表")
public class CscpRoles extends BaseEntity {

    @ApiModelProperty(value = "角色名称")
    private String name;

    /**
     *     RoleTypeEnum_0(0,"单位定义"),
     *     RoleTypeEnum_1(1,"租户定义"),
     *     RoleTypeEnum_2(2,"系统定义"),
     *     RoleTypeEnum_3(3,"表示整个系统的，只能有系统管理员操作的，平台默认的");
     */
    @ApiModelProperty(value = "角色的类别，单位自己新增的，租户管理员新增的，平台自带的")
   private Integer roleType;

    @ApiModelProperty(value = "主要资费")
    private String mainTariff;

    @ApiModelProperty(value = "附加资费")
    private String surcharge;


    /**
     * 角色范围类别
     * 10 普通用户新增查询过滤标记 ( 过滤标记)
     */
    @ApiModelProperty(value = "角色范围类别: 10 普通用户新增查询过滤标记(过滤标记)")
    private Integer rangeType ;

    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    @ApiModelProperty(value = "角色说明")
    private String description;

    @ApiModelProperty(value = "版主角色标识（0-否，1-是）")
    private Integer moderatorStatus;
    @TableField(exist = false)
    private Long userId;
}