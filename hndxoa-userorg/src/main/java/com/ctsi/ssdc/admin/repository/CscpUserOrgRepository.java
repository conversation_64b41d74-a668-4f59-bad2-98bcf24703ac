package com.ctsi.ssdc.admin.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.database.annotation.InjectByDataBaseType;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR> Generator
*/
@InjectByDataBaseType(includes= {EnumDatabaseName.ORACLE})
public interface CscpUserOrgRepository extends MybatisBaseMapper<CscpUserOrg> {


    /**
     * 通过用户ID查询其机构信息
     * @param userId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select({
            "select co.id, co.org_name org_name, co.type type, cuo.order_by order_by, cuo.post post, cuo.rank rank, cuo.department_head department_head, cuo.default_department "
                    + "from cscp_org co,cscp_user_org cuo "
                    + "where cuo.deleted = 0 "
                    + "and (co.id = cuo.org_id "
                    + "and cuo.user_id=#{userId,jdbcType=BIGINT} "
                    + "and co.deleted = 0)"
    })
    @Results({
            @Result(column="id", property="id"),
            @Result(column="org_name", property="orgName"),
            @Result(column="order_by", property="userOrgSort"),
            @Result(column="post", property="post"),
            @Result(column="rank", property="rank"),
            @Result(column="department_head", property="departmentHead"),
            @Result(column="type", property="type"),
            @Result(column="default_department", property="defaultDepartment")
    })
    List<CscpOrgDTO> queryOrgByUserId(Long userId);

    /**
     * 修改单位名称
     * @param companyId
     * @param companyName
     */
    @Update({"update cscp_user_org set company_name = #{companyName},org_abbreviation = #{orgAbbreviation} where company_id = #{companyId}"})
   void updateBycompanyId(@Param("companyId") Long companyId,@Param("companyName") String companyName,@Param("orgAbbreviation") String orgAbbreviation);

    /**
     * 修改机构名称和简称
     * @param orgId
     * @param orgName
     * @param orgAbbreviation
     */
    @Update({"update cscp_user_org set org_name = #{orgName},org_abbreviation = #{orgAbbreviation} where org_id = #{orgId}"})
    void updateByOrgId(@Param("orgId") Long orgId,@Param("orgName") String orgName,@Param("orgAbbreviation") String orgAbbreviation);

    /**
     * 修改机构编码 弃用不会执行
     */
    @Update({"update cscp_user_org set path_code = CONCAT(#{newPathCode},substring(path_code,#{len})) where path_code like concat(#{pathCode},'%')"})
    void updateByPathCode(@Param("pathCode") String pathCode,@Param("newPathCode") String newPathCode,@Param("len") int len);

    @Select({
            "SELECT tab.user_id,tab.post,a.sex FROM cscp_user_org tab " +
                    "LEFT JOIN cscp_user a  ON  a.id = tab.USER_ID WHERE tab.deleted=0 AND tab.org_id = #{orgId}"
    })
    @Results({
            @Result(column="user_id", property="userId"),
            @Result(column="post", property="post"),
            @Result(column="sex", property="sex")
    })
    List<CscpUserOrg> queryListCscpUserOrg(Long orgId);
}