package com.ctsi.ssdc.admin.domain;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Generator
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CscpUserOrg", description = "用户部门")
public class CscpUserOrg implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_user_org.id
     *
     * @mbg.generated Thu May 03 10:11:11 CST 2018
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_user_org.user_id
     *
     * @mbg.generated Thu May 03 10:11:11 CST 2018
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_user_org.org_id
     *
     * @mbg.generated Thu May 03 10:11:11 CST 2018
     */
    private Long orgId;

    private Long companyId;

    private Integer orderBy;

    @TableField(exist = false)
    private Integer sex;

    /**
     * 职务
     */
    private String post;

    /**
     * 部门名称
     */
    private String orgName;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 机构完整编码
     */
    private String pathCode;

    /**
     * 机构简称
     */
    private String orgAbbreviation;

    @TableLogic()
    @TableField(select = false, fill = FieldFill.INSERT)
    private Integer deleted;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cscp_user_org
     *
     * @mbg.generated Thu May 03 10:11:11 CST 2018
     */
    private static final long serialVersionUID = 1L;

    /**
     * 是否默认登录账号，同一个账号属于多个单位或者部门时，1表示默认登录账号
     */
    private Integer defaultDepartment;

    // 是否为部门领导(1个部门可以设置多个)
    private Integer departmentHead;

    /**
     * 职级
     */
    private String rank;

    @ApiModelProperty("进入单位时间")
    private LocalDateTime entryTime;

    @ApiModelProperty("机构类型：1-虚拟机构，2-单位，3-内设机构")
    @TableField(exist = false)
    private Integer orgType;


    public static enum DEFAULTDEPARMENT {
        LOGIN(1, "多单位部门默认登录登录账号"),

        NO_LOGIN(0, "多单位部门非默认登录账号");

        private Integer code;

        private String message;

        DEFAULTDEPARMENT(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

    }
}