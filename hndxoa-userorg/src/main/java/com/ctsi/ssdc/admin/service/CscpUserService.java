package com.ctsi.ssdc.admin.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;
import com.ctsi.ssdc.admin.service.impl.CscpUserServiceImpl;
import com.ctsi.ssdc.dto.QuerySystemLogDTO;
import com.ctsi.ssdc.dto.QueryUserExamineDTO;
import com.ctsi.ssdc.dto.QueryUserLockDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserNumberDTO;
import com.ctsi.ssdc.vo.*;
import org.apache.commons.lang3.tuple.Pair;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service Interface for managing CscpUser.
 *
 * <AUTHOR> biyi generator
 */
public interface CscpUserService extends SysBaseServiceI<CscpUser> {

    /**
     * insert a cscpUser.
     *
     * @param cscpUserDTO the entity to insert
     * @return the persisted entity
     */
    CscpUserDTO insert(CscpUserDTO cscpUserDTO);

    CscpUserDTO crmInsert(CscpUserDTO cscpUserDTO, TSysTenant tSysTenant,Long orgId);


    /**
     * update a cscpUser.
     *
     * @param cscpUserDTO the entity to update
     * @return the persisted entity
     */
    CscpUserDTO update(CscpUserDTO cscpUserDTO);

    CscpUserDTO crmUpdate(CscpUserDTO cscpUserDTO,TSysTenant tSysTenant,Long orgId);

    /**
     * 重置所有用户（系统管理员除外）密码
     * @param cscpUserPasswordUpdate
     * @return
     */
    boolean resetAllPassword(CscpUserPasswordUpdate cscpUserPasswordUpdate) throws Exception;

    /**
     * 根据用户ID修改用户密码
     *
     * @param cscpUserPasswordUpdate the entity to update
     * @param checkOld
     * @return the persisted entity
     */
    CscpUserServiceImpl.UpdatePasswordResult updatePassword(CscpUserPasswordUpdate cscpUserPasswordUpdate, boolean checkOld) throws Exception;

    boolean updateUserPassword(CscpUserPasswordUpdate cscpUserPasswordUpdate) throws Exception;

    /**
     * Get all the cscpUsers.
     *
     * @return the list of entities
     */
    PageResult<CscpUserDTO> findAll();

    /**
     * Get the  cscpUser.
     *
     * @param id the id of the entity
     * @return the entity
     */
    CscpUserDTO findByUserId(Long id);

    /**
     * Delete the  cscpUser.
     *
     * @param id the id of the entity
     */
    void delete(Long id, Long orgId);

    List<Long> disable(List<Long> ids);

    List<Long> enable(List<Long> ids);


    /**
     * 通过业务id获取手机号码
     *
     * @param id
     * @return
     */
    String getMobilePhoneById(Long id);

    /**
     * 查询用户不脱敏
     *
     * @param cscpUserDTO
     * @param page
     * @return
     */
    PageResult<CscpUserDTO> findByCscpUserDTO(CscpUserDTO cscpUserDTO, BasePageForm page);

    /**
     * 查询用户脱敏
     *
     * @param cscpUserDTO
     * @param page
     * @return
     */
    PageResult<CscpUserDTO> findByCscpUserDTOAndDesensitized(CscpUserDTO cscpUserDTO, BasePageForm page);


    PageResult<CscpUserDTO> findByOrgId(CscpUserDTO cscpUserDTO, BasePageForm page);


    /**
     * 验证用户名是否存在
     *
     * @param userName
     * @return
     */
    boolean existByUsername(String userName);



    /**
     * 验证口令是否过期 0：未过期 1：初始口令需要修改 2：口令超3个月过期
     *
     * @param userId
     * @param pwd
     * @return
     */
    int passwordNeedChange(long userId, String pwd);


    /**
     * 返回当前登录用户的基础信息，包括多部门时候以何种身份进入
     *
     * @param userName
     * @return
     */
    public Optional<CscpUserDTO> findByCurrentUserName(String userName);

    /**
     * 重置缓存
     * @param userName
     */
    public void clearUserCache(String userName);

    /**
     * 重置缓存
     * @param mobile
     */
    public void clearUserCacheUsernameOrMobile(String mobile);


    void updateUserDetailForLogin(long id);

    /**
     * 获取当前最大排序号 + 1
     *
     * @return
     */
    Integer getUserSorted(CscpUserDTO cscpUserDTO);

    /**
     * 验证手机号码是否存在
     *
     * @param mobile
     * @return
     */
    boolean existByMobile(String mobile);

    public CscpUser selectUserByMobile(String mobile);

    List<CscpUser> selectUsersByMobile(String mobile);

    /**
     * 检查 strId 是否已存在
     * @param strId 用户唯一标识
     * @return true=存在，false=不存在
     */
    boolean existByStrId(String strId);

    /**
     * 根据手机号查询用户
     *
     * @param mobile
     * @return
     */
    CscpUser getUserdata(String mobile);

    /**
     * 判断登录的账号是否存在，登录的账号可能是用户名或者手机号码，先校验手机号码，再校验密码
     *
     * @param userName
     * @return
     */
    CscpUserDTO getUserByUsernameOrMobile(String userName);

    /**
     * 查询不在当前queryCscpUserNotInListDTO对象中list中用户信息，本单位中排除
     *
     * @param iPage
     * @param queryCscpUserNotInListDTO
     * @return
     */
    IPage<CscpUserDTO> queryCscpUserNotInListDTO(IPage iPage, QueryCscpUserNotInListDTO queryCscpUserNotInListDTO);

    /**
     * 获取当前登录用户的基础信息
     *
     * @param uid
     * @return
     */
    CurrentCscpUserDTO findCurrentUserDto(long uid);

    /**
     * 根据用户名查询用户数据
     *
     * @param userName
     * @return
     */
    List<CscpUserDTO> queryCscpUserList(String userName);


    /**
     * 批量查询用户信息，isDisplay为true表示只查询显示的
     *
     * @param userIdList
     * @param isDisplay
     * @return
     */
    List<CscpUserDTO> listQueryUserDTO(List<Long> userIdList, boolean isDisplay);

    /**
     * 根据部门ID查询分管领导信息
     */
    CscpUserDTO selectBranchLeader(Long orgId);

    /**
     * 根据部门ID查询部门领导信息
     *
     * @param orgId
     * @return
     */
    List<CscpUserDTO> selectDepartmentHead(Long orgId);

    /**
     * 根据部门ID查询分管领导和部门领导信息
     *
     * @param orgId
     * @return
     */
    CscpUserDTO selectBranchLeaderAndDepartmentHead(Long orgId);

    /**
     * 设置单个部门领导
     *
     * @param orgId
     * @param userId
     * @return
     */
    boolean saveDepartmentHead(Long orgId, Long userId);

    /**
     * 删除部门领导
     *
     * @param orgId
     * @param userId
     * @return
     */
    boolean deleteDepartmentHead(Long orgId, Long userId);

    /**
     * 删除用户组织机构中间表
     *
     * @param orgId
     * @param userId
     * @return
     */
    boolean deleteUserOrg(Long orgId, Long userId);

    /**
     * 部门添加分管领导和部门领导
     *
     * @param cscpOrgDTO
     * @return
     */
    boolean saveBranchLeaderAndDepartmentHead(CscpOrgDTO cscpOrgDTO);


    /**
     * 断名称是否相同，如果相同返回一个新的名称 名称后面 _数字
     * 如果是
     *
     * @return
     */
    String getUserNameMaxNumber(String userName);

    /**
     * 校验用户状态
     *
     * @param userId
     * @return
     */
    boolean checkUserStatus(Long userId);

    /**
     * 判断用户表排序是否存在
     *
     * @param userId
     * @param sort
     * @return
     */
    boolean checkUserSortExist(Long userId, Integer sort);

    /**
     * crm判断用户表排序是否存在
     *
     * @param userId
     * @param sort
     * @return
     */
    boolean crmCheckUserSortExist(Long userId, Integer sort,Long tenantId);

    /**
     * 修改用户表排序
     *
     * @param cscpUserDTO
     * @return
     */
    boolean updateUserSort(CscpUserDTO cscpUserDTO);

    /**
     * CRM修改用户表排序
     *
     * @param cscpUserDTO
     * @return
     */
    boolean crmUpdateUserSort(CscpUserDTO cscpUserDTO,TSysTenant tSysTenant);

    /**
     * 查询租户下所有用户
     *
     * @param cscpUserDTO
     * @return
     */
    List<CscpUserDTO> selectUserByTenantId(CscpUserDTO cscpUserDTO);

    /**
     * 查询指定租户下的用户
     *
     * @param tenantId
     * @return
     */
    List<CscpUserDTO> selectTenantUsers(Long tenantId);

    /**
     * 条件查询用户
     *
     * @param cscpUserDTO
     * @return
     */
    List<CscpUserDTO> selectUserList(CscpUserDTO cscpUserDTO);

    List<CscpUserExportDTO> selectUserExport(CscpUserDTO cscpUserDTO);
    /**
     * 获取本单位未登录的用户
     *
     * @param singInUserIdList
     * @return
     */
    List<CscpUserNumberDTO> notSingInCompanyUser(List<Long> singInUserIdList);

    /**
     * 获取租户登录人数
     *
     * @return
     */
    CscpStatisticsSignInTenantDTO statisticsSignInTenantPeople();

    /**
     * 获取租户在线人数和本单位在线人数
     *
     * @return
     */
    NumberOfPeopleOnlineDTO numberOfPeopleOnline();

    /**
     * 根据多个用户id获取用户的手机号码
     *
     * @param userId
     * @return
     */
    List<String> getCscpUserMoileAll(List<Long> userId);


    /**
     * 查询用户基本属性
     *
     * @param cscpBaseUserDTO
     * @return
     */
    List<CscpBaseUserDTO> selectBaseUserInfo(CscpBaseUserDTO cscpBaseUserDTO);


    /**
     * 查询用户基本组织机构职位属性
     * @param cscpBaseUserDTO
     * @return
     */
    List<CscpBaseUserDTO> queryBaseUserInfo(CscpBaseUserDTO cscpBaseUserDTO);

    /**
     * 删除租户下的所有用户
     *
     * @param tenantId
     * @return
     */
    Boolean deleteUserByTenantId(Long tenantId);

    /**
     * 查询本单位除指定部门外的所有用户信息
     *
     * @param cscpUserDTO
     * @param basePageForm
     * @return
     */
    PageResult<CscpUserDTO> queryOtherDepartmentUsers(CscpUserDTO cscpUserDTO, BasePageForm basePageForm);

    /**
     * 获取本单位在线人数和未在线人数
     *
     * @return
     */
    StatisticsSignInPeopleDTO statisticsSignInCompanyPeople();


    /**
     * 批量查询用户基本信息
     * @param userIdList
     * @param isDisplay
     * @return
     */
    List<CscpBaseUserDTO> listQueryBaseUserDTO(List<Long> userIdList, boolean isDisplay);

    /**
     * 发送短信验证码
     *
     * @param smsVerificationCode
     * @return
     */
    Boolean sendSmsVerificationCode(SmsVerificationCodeDTO smsVerificationCode);

    /**
     * 验证手机验证码是否正确
     *
     * @param smsVerificationCode
     * @return
     */
    Boolean checkVerificationCode(SmsVerificationCodeDTO smsVerificationCode);

    /**
     * 修改APP版本号
     * @param id
     * @param versionName
     */
    void updateAppVersionName(Long id,String versionName);

    /**
     * 查询工作组成员
     * @param groupId
     * @param realName
     * @return
     */
    IPage<CscpUserDTO> getWorkUserList(Long groupId,String realName, BasePageForm basePageForm);

    PageResult<CscpUserDTO> selectCommCompanyUserList(Long id, String realName, List<Long> userIds, BasePageForm basePageForm);

    /**
     * 根据手机号查询手机号是否在系统已重复。包括备用手机号
     *
     * @param mobiles 电话号码字符串用,分割
     * @param userId 用户Id
     * @return
     */
    List<String> existMobileByMobileList(String mobiles,Long userId);

    /**
     * 根据  商信ID 查找用户
     * @param strId
     * @return
     */
    CscpUser selectUserByStrId(String strId);

    Map<Boolean, String> queryUserUpdatePassword(String loginName);

    /**
     * 获取用户锁定列表
     *
     * @param vo 请求参数
     * @param basePageForm 分页参数
     * @return 响应参数
     */
    PageResult<QueryUserLockDTO> queryUserLock(QueryUserLockVO vo, BasePageForm basePageForm);

    /**
     * 获取用户审核记录列表
     *
     * @param vo 请求参数
     * @param basePageForm 分页参数
     * @return 响应参数
     */
    PageResult<QueryUserExamineDTO> queryUserExamine(QueryUserLockVO vo, BasePageForm basePageForm);

    boolean updateUserExamine(UpdateUserExamineVO vo);

    boolean updateUserSecurityClassification(UpdateUserSecurityClassificationVO vo);

    /**
     * 查询系统日志
     *
     * @param vo 请求参数
     * @param basePageForm 分页参数
     * @return 响应参数
     */
    PageResult<QuerySystemLogDTO> querySystemLog(QuerySystemLogVO vo, BasePageForm basePageForm) throws ParseException;

    /**
     * 获取选中用户列表
     *
     * @param vo 请求参数
     * @return 响应参数
     */
    List<QueryUserSelectedVO.QueryUserSelectedInfoVO> queryUserSelected(QueryUserSelectedVO vo);

    /**
     * 【pc端】呈批件起草，涉m等级判定
     * 对接收人的权限进行校验。判定接收人涉m等级是否与呈批件m级匹配：
     * @param vo
     * @return
     */
    ResultVO<Boolean> checkUserSecurityLevel(CheckUserSecretLevelVO vo);

    CscpUserDTO selectUserIsDisable(Long id);

    int fixDuplicateLoginNames();

    int dealAdminForOrgs(Long orgId);

    /**
     * 根据 BIZ_HRS_USER_INFO 表的数据生成更新指定机构下 CSCP_USER 表身份证号的 SQL 语句，并直接下载到客户端。
     *
     * @param companyId 机构 ID
     * @param response  HttpServletResponse 对象，用于设置响应头和输出流
     * @throws IOException 文件写入或网络传输异常
     */
    void generateUpdateSqlAndDownload(Long companyId, HttpServletResponse response) throws IOException;

    /**
     * 区划用户管理
     * @param cscpUserDTO
     * @param page
     * @return
     */
    PageResult<CscpUserDTO> selectDivisionUserById(CscpUserDTO cscpUserDTO, BasePageForm page);

    PageResult<CscpUserDTO> findByOrgIdNew(CscpUserDTO cscpUserDTO, BasePageForm page);

    PageResult<CscpUserDTO> queryUserByTreeIdAndRoleId(Long orgId , Long roleId , BasePageForm basePageForm);

    Boolean sendSmsVerificationCodeWw(SmsVerificationCodeDTO smsVerificationCode);

    Optional<CscpUserDTO> findByCurrentUserName2(String login);

    Pair<Boolean, String> checkUserOrgByOrgId(CscpUserOrgAlterDTO record);

    IPage<CscpUserDTO> queryCscpUserByRole(IPage mPlusPageByBasePage, QueryCscpUserNotInListDTO build);

    PageResult<CscpUserDTO> queryUserByTreeIdAndRoleCode(Long orgId, List<String> list, BasePageForm basePageForm);

    void judgeUserStatus(List<Long> ids);

    Integer selectUserListCount(CscpUserDTO dto);

    List<CscpUserExportDTO> selectUserListByPage(CscpUserDTO dto, Integer pageNo, Integer pageSize);

    /**
     * 根据用户ID查询用户信息
     * @param userId
     * @return
     */
    CscpUserDTO selectUserById(Long userId);

    void updateAppCodesById(Long userId, String appCodes);

    List<CscpUserDTO> selectPullCscpUser(List<TYJGSyncUserOrgIdDTO> list);
}
