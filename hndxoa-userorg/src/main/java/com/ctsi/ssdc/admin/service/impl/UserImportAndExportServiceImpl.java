package com.ctsi.ssdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.filestore.impl.CscpFileDiskStorageServiceImpl;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.TSyncExportUserRecord;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.repository.TSyncExportUserRecordMapper;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.admin.service.UserImportAndExportService;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname UserImportAndExportServiceImpl
 * @Description
 * @Date 2021/12/29/0029 16:26
 */
@Slf4j
@Service
public class UserImportAndExportServiceImpl implements UserImportAndExportService {

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private ISysImportService iSysImportService;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Resource
    private CscpFileDiskStorageServiceImpl fileDiskStorageService;

    @Autowired
    private TSyncExportUserRecordMapper tSyncExportUserRecordMapper;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^\\d{17}[0-9X]$");

    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]+$");


    @Override
    public Map<String, List<Long>> saveUsers(Long id, List<UserImportDTO> dataList) {
        // 新建一个保存失败用户记录的集合
        List<UserImportDTO> failedList = new ArrayList<>();
        List<UserImportDTO> updateList = new ArrayList<>();
        Set<Long> ids = new HashSet<>();
        Set<Long> updateIds = new HashSet<>();
        String flag = "DW";
        List<String> idCardNoList = new ArrayList<>();
        for (UserImportDTO userImportDTO : dataList) {
            log.info("UserImportAndExportServiceImpl.saveUsers 读取到一条数据: {}", userImportDTO);
            try {
                if (StringUtils.isBlank(userImportDTO.getDepartmentName())) {
                    throw new BusinessException("部门名称不能为空");
                }

                if (StringUtils.isNotEmpty(userImportDTO.getIdCardNo())) {
                    // 过滤重复身份证
                    this.isValidIdCardNo(idCardNoList, userImportDTO.getIdCardNo());
                }
                // 组装数据
                userImportDTO.setCompanyId(id);
                userImportDTO.setFlag(flag);

                // 使用分布式锁确保手机号查询和插入的原子性
                String lockKey = "user_import_lock:" + userImportDTO.getMobile();
                Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 60, TimeUnit.SECONDS);

                if (Boolean.TRUE.equals(locked)) {
                    try {
                        // 在锁保护下重新查询用户是否存在
                        List<CscpUser> user = cscpUserService.selectUsersByMobile(userImportDTO.getMobile());

                        if(CollectionUtils.isEmpty(user)){
                            // 用户不存在，创建新用户
                            CscpUserDTO userDTO = this.assembledUserDTO(userImportDTO);
                            cscpUserService.insert(userDTO);
                            if (null != userDTO.getId()) {
                                ids.add(userDTO.getId());
                            }
                        } else if (user.size() == 1) {
                            // 用户已存在，更新用户信息（不重新生成用户名）
                            CscpUserDTO userDTO = this.assembledUserDTOForUpdate(userImportDTO, user.get(0));
                            cscpUserService.update(userDTO);
                            updateIds.add(userDTO.getId());
                            userImportDTO.setFailedReason("已存在手机号，根据手机号更新了用户信息");
                            updateList.add(userImportDTO);
                        } else {
                            userImportDTO.setFailedReason("手机号码已经在系统存在多个，请不要重复导入");
                            failedList.add(userImportDTO);
                        }
                    } finally {
                        // 确保释放锁
                        redisTemplate.delete(lockKey);
                    }
                } else {
                    // 获取锁失败，说明有其他线程正在处理相同手机号的用户
                    userImportDTO.setFailedReason("系统正在处理相同手机号的用户，请稍后重试");
                    failedList.add(userImportDTO);
                }

            } catch (BusinessException e) {
                userImportDTO.setFailedReason(e.getMessage());
                failedList.add(userImportDTO);
            }
        }
        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(dataList.size());
        sysUserImportDTO.setFailedNo(failedList.size());
        sysUserImportDTO.setSuccessNo(dataList.size() - failedList.size());
        // 导入数据类型
        sysUserImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.USER_IMPORT));

        failedList.addAll(updateList);
        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysUserImportDTO);
        } else {
            // 保存导入记录，并上传Excel失败文件
            iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, UserImportDTO.class, FileBasePathName.USER_IMPORT);
        }
        Map<String, List<Long>> map = new HashMap<>(2);
        map.put("add", new ArrayList<>(ids));
        map.put("update", new ArrayList<>(updateIds));
        return map;
    }

    @Override
    public Map<String, List<Long>> saveUsersNew(List<UserKuaDanWeiImportDTO> dataList) {
        // 新建一个保存失败用户记录的集合
        List<UserKuaDanWeiImportDTO> failedList = new ArrayList<>();
        List<UserKuaDanWeiImportDTO> updateList = new ArrayList<>();
        Set<Long> ids = new HashSet<>();
        Set<Long> updateIds = new HashSet<>();
        String flag = "KDW";
        List<String> idCardNoList = new ArrayList<>();
        for (UserKuaDanWeiImportDTO userImportDTO : dataList) {
            log.info("UserImportAndExportServiceImpl.saveUsers 读取到一条数据: {}", userImportDTO);
            try {
                // 现在根据单位编码获取单位信息;
                LambdaQueryWrapper<CscpOrg> orgLqw = new LambdaQueryWrapper<>();
                if (StringUtils.isEmpty(userImportDTO.getOrgCode())) {
                    throw new BusinessException("单位编码不存在");
                }
                if (StringUtils.isEmpty(userImportDTO.getCompanyName())) {
                    throw new BusinessException("单位名称不存在");
                }
                if (StringUtils.isBlank(userImportDTO.getDepartmentName())) {
                    throw new BusinessException("部门名称不能为空");
                }
                if (StringUtils.isNotEmpty(userImportDTO.getIdCardNo())) {
                    // 过滤重复身份证
                    this.isValidIdCardNo(idCardNoList, userImportDTO.getIdCardNo());
                }
                orgLqw.eq(CscpOrg::getOrgCode,userImportDTO.getOrgCode());
                orgLqw.eq(CscpOrg::getType,2);
                CscpOrg cscpOrg = cscpOrgService.selectOneNoAdd(orgLqw);
                if (null == cscpOrg) {
                    throw new BusinessException("单位不存在");
                } else {
                    Long id = cscpOrg.getId();
                    List<Node<CscpOrgDTO>> nodes = cscpOrgService.selectChildrenListNodeByParentId(id);
                    Set<String> flagDept = new HashSet<>();
                    if (StringUtils.isNotEmpty(userImportDTO.getDepartmentName())) {
                        String [] deptName = userImportDTO.getDepartmentName().split(",");
                        List<String> deptNameList = Arrays.stream(deptName).collect(Collectors.toList());
                        for (String s : deptNameList) {
                            for (Node<CscpOrgDTO> node : nodes) {
                                if (s.equals(node.getDetailsData().getOrgName())) {
                                    flagDept.add(s);
                                }
                            }
                        }
                        List<String> differenceUsingStream = deptNameList.stream()
                                .filter(e ->!flagDept.contains(e))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(differenceUsingStream)) {
                            StringBuilder errorMsg = new StringBuilder();
                            for (String string : differenceUsingStream) {
                                errorMsg.append(string).append(",");
                            }
                            String errorMsgStr = errorMsg.substring(0, errorMsg.length() -1);
                            userImportDTO.setFailedReason("单位下不存在部门: "+ errorMsgStr);
                            failedList.add(userImportDTO);
                            continue;
                        }
                    }
                    Long companyId = compareOrgNameNew(nodes, userImportDTO.getDepartmentName());
                    if (companyId != null) {
                        userImportDTO.setCompanyId(companyId);
                    } else {
                        //没有部门就直接挂在单位下
                        userImportDTO.setCompanyId(cscpOrg.getId());
                    }
                }
                UserImportDTO  userImportDTO1 = new UserImportDTO();
                BeanUtils.copyProperties(userImportDTO,userImportDTO1);
                userImportDTO1.setFlag(flag);
                CscpUserDTO userDTO = this.assembledUserDTO(userImportDTO1);

                List<CscpUser> user = cscpUserService.selectUsersByMobile(userDTO.getMobile());
                //boolean existByMobile = cscpUserService.existByMobile(userDTO.getMobile());
                if(CollectionUtils.isEmpty(user)){
                    cscpUserService.insert(userDTO);
                    if (null != userDTO.getId()) {
                        ids.add(userDTO.getId());
                    }
                } else if (user.size() == 1) {
                    userDTO.setId(user.get(0).getId());
                    userDTO.setStrId(user.get(0).getStrId());
                    if (StringUtils.isEmpty(userImportDTO.getImportLoginName())) {
                        userDTO.setLoginName(user.get(0).getLoginName());
                    } else {
                        userDTO.setLoginName(userImportDTO.getImportLoginName());
                    }
                    cscpUserService.update(userDTO);
                    updateIds.add(userDTO.getId());
                    userImportDTO.setFailedReason("已存在手机号，根据手机号更新了用户信息");
                    updateList.add(userImportDTO);
                } else {
                    userImportDTO.setFailedReason("手机号码已经在系统存在多个，请不要重复导入");
                    failedList.add(userImportDTO);
                }

            } catch (BusinessException e) {
                userImportDTO.setFailedReason(e.getMessage());
                failedList.add(userImportDTO);
            }
        }
        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(dataList.size());
        sysUserImportDTO.setFailedNo(failedList.size());
        sysUserImportDTO.setSuccessNo(dataList.size() - failedList.size());
        // 导入数据类型
        sysUserImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.USER_IMPORT));

        failedList.addAll(updateList);
        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysUserImportDTO);
        } else {
            // 保存导入记录，并上传Excel失败文件
            iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, UserKuaDanWeiImportDTO.class, FileBasePathName.USER_IMPORT);
        }
        Map<String, List<Long>> map = new HashMap<>(2);
        map.put("add", new ArrayList<>(ids));
        map.put("update", new ArrayList<>(updateIds));
        return map;
    }


    Long compareOrgName(List<Node<CscpOrgDTO>> list,String orgName){
        for (Node<CscpOrgDTO> cscpOrgDTONode : list) {
            if(cscpOrgDTONode.getDetailsData().getOrgName().equals(orgName)){
                return cscpOrgDTONode.getDetailsData().getCompanyId();
            }
            if(cscpOrgDTONode.getChildren()!=null&&!cscpOrgDTONode.getChildren().isEmpty()){
                compareOrgName(cscpOrgDTONode.getChildren(),orgName);
            }
        }
        return null;

    }

    Long compareOrgNameNew(List<Node<CscpOrgDTO>> list,String orgName){
        for (Node<CscpOrgDTO> cscpOrgDTONode : list) {
            if(cscpOrgDTONode.getDetailsData().getOrgName().equals(orgName)){
                return cscpOrgDTONode.getDetailsData().getId();
            }
            if(cscpOrgDTONode.getChildren()!=null&&!cscpOrgDTONode.getChildren().isEmpty()){
                compareOrgNameNew(cscpOrgDTONode.getChildren(),orgName);
            }
        }
        return null;

    }

    //分割字符串加密
    public String division(String realName) {
        String realNames = "";
        if (StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames;
    }

    private String patternQuote(String realName) {

        if (realName == null) {
            return realName;
        }

        // 替换逻辑：将+替换为\+
        return realName.replace("+", "\\+");
    }

    /**
     * 查询单位ID下的用户数据，并导出到EXCEL
     * @param dto
     * @param response
     * @return
     */
    @Override
    public void exportUserToExcel(CscpUserDTO dto, HttpServletResponse response) {
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        SecurityContext securityContext = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            try {
                SecurityContextHolder.setContext(securityContext);
                Path tempFile = Files.createTempFile("用户数据", ".xlsx");
                String safeFileName = tempFile.toAbsolutePath().toString();
                // 处理参数
                Long id = dto.getId();
                if (id != null) {
                    dto.setCompanyId(id);
                } else if (currentCompanyId != null) {
                    dto.setCompanyId(currentCompanyId);
                } else if (tenantId != null) {
                    dto.setTenantId(tenantId);
                }
                LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CscpOrg::getId, id);
                CscpOrg cscpOrg = cscpOrgService.selectOneNoAdd(queryWrapper);

                // 敏感数据处理
                String realName = dto.getRealName();
                String encryptedName = westoneEncryptService.isCipherMachine()
                        ? westoneEncryptService.divisionEncryptRealNameWithFPE(realName)
                        : division(realName);
                dto.setRealName(patternQuote(encryptedName));


                // 查询总记录数
                Integer recordCount = cscpUserService.selectUserListCount(dto);
                if (recordCount == 0) {
                    return;
                }

                List<UserExportDTO> exportDTOList = new ArrayList<>();
                // 分页导出（每批5000条）
                int pageSize = 5000;
                int totalPages = (int) Math.ceil((double) recordCount / pageSize);
                for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                    int offset = (pageNum - 1) * pageSize;
                    // 分页查询
                    List<CscpUserExportDTO> pageData = cscpUserService.selectUserListByPage(dto, offset, pageSize);

                    // 数据转换
                    List<UserExportDTO> dataList = ListCopyUtil.copy(pageData, UserExportDTO.class);
                    exportDTOList.addAll(dataList);
                }

                // 写入 Excel 文件
                EasyExcel.write(safeFileName, UserExportDTO.class)
                        .sheet("用户数据")
                        .doWrite(exportDTOList);
                byte[] bytes = Files.readAllBytes(tempFile);

                String filePath = fileStoreTemplateService.createFileUrl(FileBasePathName.USER_EXPORT, ".xlsx");
                boolean b = fileDiskStorageService.uploadFile2(filePath, bytes);

                // 记录导出信息到数据库
                TSyncExportUserRecord exportRecord = new TSyncExportUserRecord();
                exportRecord.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                exportRecord.setFileName(null != cscpOrg ? cscpOrg.getOrgName() + "用户导出" : "用户导出");
                exportRecord.setFilePath(filePath);
                exportRecord.setExportCount(recordCount);
                exportRecord.setTenantId(tenantId);
                exportRecord.setCompanyId(currentCompanyId);
                exportRecord.setExportStatus(b ? "导出成功" : "导出失败");
                exportRecord.setOperatorId(SecurityUtils.getCurrentUserId());
                exportRecord.setOperatorName(SecurityUtils.getCurrentUserName());

                // 插入数据库
                tSyncExportUserRecordMapper.insert(exportRecord);
            } catch (Exception e) {
                log.error("导出数据失败", e);
                throw new BusinessException("导出数据失败", e);
            }
        });
    }

    @Override
    public Map<String, List<Long>> saveQhUsers(List<UserKuaDanWeiImportDTO> dataList) {
        // 新建一个保存失败用户记录的集合
        List<UserKuaDanWeiImportDTO> failedList = new ArrayList<>();
        List<UserKuaDanWeiImportDTO> updateList = new ArrayList<>();
        Set<Long> ids = new HashSet<>();
        Set<Long> updateIds = new HashSet<>();
        String flag = "QH";
        List<String> idCardNoList = new ArrayList<>();
        for (UserKuaDanWeiImportDTO userImportDTO : dataList) {
            log.info("UserImportAndExportServiceImpl.saveUsers 读取到一条数据: {}", userImportDTO);
            try {
                // 现在根据单位编码获取单位信息;
                LambdaQueryWrapper<CscpOrg> orgLqw = new LambdaQueryWrapper<>();
                if (StringUtils.isEmpty(userImportDTO.getOrgCode())) {
                    throw new BusinessException("单位编码不存在");
                }
                if (StringUtils.isEmpty(userImportDTO.getCompanyName())) {
                    throw new BusinessException("单位名称不存在");
                }
                if (StringUtils.isBlank(userImportDTO.getDepartmentName())) {
                    throw new BusinessException("部门名称不能为空");
                }
                if (StringUtils.isNotEmpty(userImportDTO.getIdCardNo())) {
                    // 过滤重复身份证
                    this.isValidIdCardNo(idCardNoList, userImportDTO.getIdCardNo());
                }
                orgLqw.eq(CscpOrg::getOrgCode,userImportDTO.getOrgCode());
                orgLqw.eq(CscpOrg::getType,2);
                CscpOrg cscpOrg = cscpOrgService.selectOneNoAdd(orgLqw);
                if (null == cscpOrg) {
                    throw new BusinessException("单位不存在");
                } else {
                    Long id = cscpOrg.getId();
                    List<Node<CscpOrgDTO>> nodes = cscpOrgService.selectChildrenListNodeByParentId(id);
                    Set<String> flagDept = new HashSet<>();
                    if (StringUtils.isNotEmpty(userImportDTO.getDepartmentName())) {
                        String [] deptName = userImportDTO.getDepartmentName().split(",");
                        List<String> deptNameList = Arrays.stream(deptName).collect(Collectors.toList());
                        for (String s : deptNameList) {
                            for (Node<CscpOrgDTO> node : nodes) {
                                if (s.equals(node.getDetailsData().getOrgName())) {
                                    flagDept.add(s);
                                }
                            }
                        }
                        List<String> differenceUsingStream = deptNameList.stream()
                                .filter(e ->!flagDept.contains(e))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(differenceUsingStream)) {
                            StringBuilder errorMsg = new StringBuilder();
                            for (String string : differenceUsingStream) {
                                errorMsg.append(string).append(",");
                            }
                            String errorMsgStr = errorMsg.substring(0, errorMsg.length() -1);
                            userImportDTO.setFailedReason("单位下不存在部门: "+ errorMsgStr);
                            failedList.add(userImportDTO);
                            continue;
                        }
                    }
                    Long companyId = compareOrgNameNew(nodes, userImportDTO.getDepartmentName());
                    if (companyId != null) {
                        userImportDTO.setCompanyId(companyId);
                    } else {
                        //没有部门就直接挂在单位下
                        userImportDTO.setCompanyId(cscpOrg.getId());
                    }
                }
                UserImportDTO  userImportDTO1 = new UserImportDTO();
                BeanUtils.copyProperties(userImportDTO,userImportDTO1);
                userImportDTO1.setFlag(flag);
                CscpUserDTO userDTO = this.assembledUserDTO(userImportDTO1);

                List<CscpUser> user = cscpUserService.selectUsersByMobile(userDTO.getMobile());
                //boolean existByMobile = cscpUserService.existByMobile(userDTO.getMobile());
                if(CollectionUtils.isEmpty(user)){
                    cscpUserService.insert(userDTO);
                    if (null != userDTO.getId()) {
                        ids.add(userDTO.getId());
                    }
                } else if (user.size() == 1) {
                    userDTO.setId(user.get(0).getId());
                    userDTO.setStrId(user.get(0).getStrId());
                    if (StringUtils.isEmpty(userImportDTO.getImportLoginName())) {
                        userDTO.setLoginName(user.get(0).getLoginName());
                    } else {
                        userDTO.setLoginName(userImportDTO.getImportLoginName());
                    }
                    cscpUserService.update(userDTO);
                    updateIds.add(userDTO.getId());
                    userImportDTO.setFailedReason("已存在手机号，根据手机号更新了用户信息");
                    updateList.add(userImportDTO);
                } else {
                    userImportDTO.setFailedReason("手机号码已经在系统存在多个，请不要重复导入");
                    failedList.add(userImportDTO);
                }

            } catch (BusinessException e) {
                userImportDTO.setFailedReason(e.getMessage());
                failedList.add(userImportDTO);
            }
        }
        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(dataList.size());
        sysUserImportDTO.setFailedNo(failedList.size());
        sysUserImportDTO.setSuccessNo(dataList.size() - failedList.size());
        // 导入数据类型
        sysUserImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.USER_IMPORT));

        failedList.addAll(updateList);
        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysUserImportDTO);
        } else {
            // 保存导入记录，并上传Excel失败文件
            iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, UserKuaDanWeiImportDTO.class, FileBasePathName.USER_IMPORT);
        }

        Map<String, List<Long>> map = new HashMap<>(2);
        map.put("add", new ArrayList<>(ids));
        map.put("update", new ArrayList<>(updateIds));
        return map;
    }

    /**
     * 保存大参林用户信息
     *
     * @param orgId 组织ID
     * @param dataList 用户数据列表
     * @return 包含添加和更新的用户ID的映射
     */
    @Override
    public Map<String, List<Long>> saveDcjUsers(Long orgId, List<UserDcjImportDTO> dataList) {
        List<UserDcjImportDTO> failedList = Collections.synchronizedList(new ArrayList<>());
        List<UserDcjImportDTO> updateList = Collections.synchronizedList(new ArrayList<>());
        Set<Long> ids = Collections.synchronizedSet(new HashSet<>());
        Set<Long> updateIds = Collections.synchronizedSet(new HashSet<>());
        SecurityContext securityContext = SecurityContextHolder.getContext();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (UserDcjImportDTO userImportDTO : dataList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(securityContext);
                try {
                    // 执行基础验证
                    userImportDTO.baseValid();

                    // 创建用户DTO并复制属性
                    CscpUserDTO userDTO = createUserDTO(userImportDTO);
                    // 构建用户组织数据
                    buildUserOrgData(userDTO, orgId, userImportDTO);

                    // 根据手机号查询用户
                    List<CscpUser> users = cscpUserService.selectUsersByMobile(userDTO.getMobile());

                    // 处理用户存在性逻辑
                    handleUserExistence(userDTO, userImportDTO, users, ids, updateIds, updateList, failedList);

                } catch (Exception e) {
                    userImportDTO.setFailedReason(e.getMessage());
                    failedList.add(userImportDTO);
                }
            });

            futures.add(future);
        }

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 创建导入DTO并记录导入结果
        TSysImportDTO sysUserImportDTO = createImportDTO(dataList.size(), failedList.size());
        saveImportResult(sysUserImportDTO, failedList, updateList);

        // 构建并返回结果映射
        Map<String, List<Long>> result = new HashMap<>(2);
        result.put("add", new ArrayList<>(ids));
        result.put("update", new ArrayList<>(updateIds));
        return result;
    }

    /**
     * 创建用户DTO
     *
     * @param userImportDTO 用户导入DTO
     * @return 用户DTO
     */
    private CscpUserDTO createUserDTO(UserDcjImportDTO userImportDTO) {
        CscpUserDTO userDTO = new CscpUserDTO();
        BeanUtils.copyProperties(userImportDTO, userDTO);
        userDTO.setLoginName(userImportDTO.getImportLoginName());
        userDTO.setOrderBy(9999);
        userDTO.setDisplay(true);
        userDTO.setWhetherShow(0);
        userDTO.setStatistics(true);

        // 加默认角色信息
        List<Long> roleIdList = new ArrayList<>();
        roleIdList.add(Long.parseLong(SystemRole.GENERAL_NAME.getId()));
        userDTO.setRoleIds(roleIdList);
        userDTO.setWhetherShow(1);

        if (StringUtils.isNotEmpty(userImportDTO.getImportLoginName())) {
            if (!isValidUsername(userImportDTO.getImportLoginName())) {
                throw new BusinessException("登录名只能包含大小写字母、数字和下划线");
            }
            userDTO.setLoginName(userImportDTO.getImportLoginName());
        } else {
            // 生成唯一的用户名（使用分布式锁确保唯一性）
            String name = SurnamePinyinUtil.getPinyin(userImportDTO.getRealName().trim(),"");
            String mobileSuffix = "";
            if (userImportDTO.getMobile() != null && userImportDTO.getMobile().length() > 6) {
                mobileSuffix = StringUtils.substring(userImportDTO.getMobile(), 6);
            }

            String baseUserName = name + mobileSuffix;
            String userNameLockKey = "username_generation_lock:" + baseUserName;
            Boolean userNameLocked = redisTemplate.opsForValue().setIfAbsent(userNameLockKey, "1", 10, TimeUnit.SECONDS);

            if (Boolean.TRUE.equals(userNameLocked)) {
                try {
                    String userNameMaxNumber = cscpUserService.getUserNameMaxNumber(baseUserName);
                    userDTO.setLoginName(userNameMaxNumber);
                } finally {
                    redisTemplate.delete(userNameLockKey);
                }
            } else {
                // 如果获取锁失败，使用时间戳确保唯一性
                String timestamp = String.valueOf(System.currentTimeMillis() % 100000);
                userDTO.setLoginName(baseUserName + "_" + timestamp);
            }
        }

        return userDTO;
    }

    /**
     * 构建用户组织数据
     *
     * @param userDTO 用户DTO
     * @param orgId 组织ID
     * @param userImportDTO 用户导入DTO
     */
    private void buildUserOrgData(CscpUserDTO userDTO, Long orgId, UserDcjImportDTO userImportDTO) {
        String[] orgNamePaths = StringUtils.split(userImportDTO.getOrgNamePath(), ",");
        if (orgNamePaths == null || orgNamePaths.length == 0) {
            throw new BusinessException("机构层级数据格式不正确");
        }
        buildUserOrgData(userDTO, orgNamePaths, orgId, userImportDTO);
    }

    /**
     * 处理用户存在性逻辑
     *
     * @param userDTO 用户DTO
     * @param userImportDTO 用户导入DTO
     * @param users 用户列表
     * @param ids 添加的用户ID集合
     * @param updateIds 更新的用户ID集合
     * @param updateList 更新列表
     * @param failedList 失败列表
     */
    private void handleUserExistence(CscpUserDTO userDTO, UserDcjImportDTO userImportDTO,
                                     List<CscpUser> users, Set<Long> ids, Set<Long> updateIds,
                                     List<UserDcjImportDTO> updateList, List<UserDcjImportDTO> failedList) {
        if (CollectionUtils.isEmpty(users)) {
            // 验证唯一字段并插入用户
            validateUniqueFields(userDTO);
            cscpUserService.insert(userDTO);
            if (userDTO.getId() != null) {
                ids.add(userDTO.getId());
            }
        } else if (users.size() == 1) {
            // 更新用户信息
            updateUserDTOWithExistingUserInfo(userDTO, users.get(0), userImportDTO);
            if (shouldCheckIdCardChange(userImportDTO, userDTO)) {
                validateIdCardNo(userDTO);
            }
            cscpUserService.update(userDTO);
            updateIds.add(userDTO.getId());
            userImportDTO.setFailedReason("已存在手机号，根据手机号更新了用户信息");
            updateList.add(userImportDTO);
        } else {
            // 处理重复手机号情况
            userImportDTO.setFailedReason("手机号码已经在系统存在多个，请不要重复导入");
            failedList.add(userImportDTO);
        }
    }

    /**
     * 验证唯一字段
     *
     * @param userDTO 用户DTO
     */
    private void validateUniqueFields(CscpUserDTO userDTO) {
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpUser::getIdCardNo, userDTO.getIdCardNo())
                .eq(CscpUser::getDeleted, 0);
        Integer t1 = cscpUserService.selectCountNoAdd(queryWrapper);
        if (t1 != null && t1 > 0) {
            throw new BusinessException("身份证号已存在");
        }

        queryWrapper.clear();
        queryWrapper.eq(CscpUser::getStrId, userDTO.getStrId())
                .eq(CscpUser::getDeleted, 0);
        Integer t2 = cscpUserService.selectCountNoAdd(queryWrapper);
        if (t2 != null && t2 > 0) {
            throw new BusinessException("唯一信任号strId已存在，请不要重复导入");
        }
        queryWrapper.clear();
        queryWrapper.eq(CscpUser::getLoginName, userDTO.getLoginName()).eq(CscpUser::getDeleted, 0);
        int t3 = cscpUserService.selectCountNoAdd(queryWrapper);
        if (t3 > 0) {
            throw new BusinessException("登录名: {}, 已存在!", userDTO.getLoginName());
        }
    }


    /**
     * 更新用户DTO中的现有用户信息
     *
     * @param userDTO 用户DTO
     * @param existingUser 现有用户
     * @param userImportDTO 用户导入DTO
     */
    private void updateUserDTOWithExistingUserInfo(CscpUserDTO userDTO, CscpUser existingUser, UserDcjImportDTO userImportDTO) {
        userDTO.setId(existingUser.getId());
        userDTO.setStrId(existingUser.getStrId());
        if (StringUtils.isEmpty(userImportDTO.getImportLoginName())) {
            userDTO.setLoginName(existingUser.getLoginName());
        } else {
            if (!userImportDTO.getImportLoginName().equals(existingUser.getLoginName())) {
                LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CscpUser::getLoginName, userDTO.getLoginName()).eq(CscpUser::getDeleted, 0);
                int t3 = cscpUserService.selectCountNoAdd(queryWrapper);
                if (t3 > 0) {
                    throw new BusinessException("登录名: {}, 已存在!", userDTO.getLoginName());
                }
            }
            userDTO.setLoginName(userImportDTO.getImportLoginName());
        }
    }

    /**
     * 判断是否需要检查身份证号变化
     *
     * @param userImportDTO 用户导入DTO
     * @param userDTO 用户DTO
     * @return 是否需要检查身份证号变化
     */
    private boolean shouldCheckIdCardChange(UserDcjImportDTO userImportDTO, CscpUserDTO userDTO) {
        return userImportDTO.getIdCardNo() != null && !userImportDTO.getIdCardNo().equals(userDTO.getIdCardNo());
    }

    /**
     * 验证身份证号
     *
     * @param userDTO 用户DTO
     */
    private void validateIdCardNo(CscpUserDTO userDTO) {
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpUser::getIdCardNo, userDTO.getIdCardNo())
                .eq(CscpUser::getDeleted, 0)
                .last(SysConstant.LIMIT_ONE);
        Integer t1 = cscpUserService.selectCountNoAdd(queryWrapper);
        if (t1 != null && t1 > 0) {
            throw new BusinessException("身份证号已存在");
        }
    }

    /**
     * 创建导入DTO
     *
     * @param total 总数
     * @param failed 失败数
     * @return 导入DTO
     */
    private TSysImportDTO createImportDTO(int total, int failed) {
        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(total);
        sysUserImportDTO.setFailedNo(failed);
        sysUserImportDTO.setSuccessNo(total - failed);
        sysUserImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.USER_IMPORT));
        return sysUserImportDTO;
    }

    /**
     * 保存导入结果
     *
     * @param sysUserImportDTO 导入DTO
     * @param failedList 失败列表
     * @param updateList 更新列表
     */
    private void saveImportResult(TSysImportDTO sysUserImportDTO, List<UserDcjImportDTO> failedList, List<UserDcjImportDTO> updateList) {
        failedList.addAll(updateList);
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysUserImportDTO);
        } else {
            iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, UserDcjImportDTO.class, FileBasePathName.USER_IMPORT);
        }
    }

    /**
     * 构建用户组织数据
     *
     * @param userDTO 用户DTO
     * @param orgNamePaths 组织名称路径数组
     * @param orgId 组织ID
     * @param userImportDTO 用户导入DTO
     */
    private void buildUserOrgData(CscpUserDTO userDTO, String[] orgNamePaths, Long orgId, UserDcjImportDTO userImportDTO) {

        List<CscpOrgNameIdListDTO> cscpOrgNameIdList = new ArrayList<>(orgNamePaths.length);
        for (int u = 0; u < orgNamePaths.length; u++) {
            String[] orgNames = StringUtils.split(orgNamePaths[u], "|");
            if (orgNames == null || orgNames.length == 0) {
                throw new BusinessException("机构层级数据格式不正确");
            }
            List<String> orgNameList = Arrays.asList(orgNames);

            List<CscpOrg> list = cscpOrgService.selectChildrenByCondition(orgId, orgNameList);
            if (CollectionUtils.isEmpty(list)) {
                throw new BusinessException("请检查机构层级数据，查询不到机构");
            }
            CscpOrg dgOrg = list.get(0);
            Map<String, List<CscpOrg>> map = list.stream().collect(Collectors.groupingBy(CscpOrg::getOrgName));
            // 检查一下第一条
            if (map.get(dgOrg.getOrgName()).size() > 1) {
                throw new BusinessException("请检查机构层级数据，查询到多个机构:" + dgOrg.getOrgName());
            }
            for (int i = 1; i < orgNames.length; i++) {
                List<CscpOrg> children = map.get(orgNames[i]);

                CscpOrg finalParentOrg = dgOrg;
                List<CscpOrg> realChildren = children.stream().filter(c ->
                        StringUtils.startsWith(c.getOrgCodePath(), finalParentOrg.getOrgCodePath())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(realChildren) || realChildren.size() > 1) {
                    throw new BusinessException("请检查机构层级数据:" + orgNames[i]);
                }
                dgOrg = realChildren.get(0);
            }

            CscpOrgNameIdListDTO cscpOrgNameIdListDTO = new CscpOrgNameIdListDTO();
            cscpOrgNameIdListDTO.setId(dgOrg.getId());
            try {
                cscpOrgNameIdListDTO.setUserOrgSort(Integer.valueOf(StringUtils.split(userImportDTO.getSort(), ",")[u]));
            } catch (Exception e) {
                throw new BusinessException("机构排序(多个用,隔开)数据不正确");
            }
            cscpOrgNameIdListDTO.setTitle(dgOrg.getOrgName());
            try {
                cscpOrgNameIdListDTO.setPost(StringUtils.split(userImportDTO.getPost(), ",")[u]);
            } catch (Exception e) {
                cscpOrgNameIdListDTO.setPost(userImportDTO.getPost());
            }
            cscpOrgNameIdListDTO.setRank(userImportDTO.getRank());
            if (u == 0) {
                cscpOrgNameIdListDTO.setDepartmentHead(1);
            }
            cscpOrgNameIdListDTO.setType(dgOrg.getType());

            cscpOrgNameIdList.add(cscpOrgNameIdListDTO);
        }
        userDTO.setOrgNameList(cscpOrgNameIdList);
        userDTO.setDefaultDepart(cscpOrgNameIdList.get(0).getId());
    }

    /**
     * 组装导入用户入参
     * @param userImportDTO
     * @return
     */
    private CscpUserDTO assembledUserDTO(UserImportDTO userImportDTO) {

       if (StringUtils.isBlank(userImportDTO.getRealName())) {
            throw new BusinessException("姓名不能为空");
        }

//        if (StringUtils.isEmpty(userImportDTO.getIdCardNo())) {
//            throw new BusinessException("身份证号码不能为空");
//        } else {
//            String idCardNo = userImportDTO.getIdCardNo();
//            if (idCardNo == null ||!ID_CARD_PATTERN.matcher(idCardNo).matches()) {
//                throw new BusinessException("身份证号码格式不正确，请输入 18 位数字或前 17 位数字加最后一位大写 X");
//            }
//        }

        if (StringUtils.isNotEmpty(userImportDTO.getIdCardNo())) {
            String idCardNo = userImportDTO.getIdCardNo();
            if (idCardNo == null ||!ID_CARD_PATTERN.matcher(idCardNo).matches()) {
                throw new BusinessException("身份证号码格式不正确，请输入 18 位数字或前 17 位数字加最后一位大写 X");
            }
        }

//        if (StringUtils.isBlank(userImportDTO.getSort())) {
//            throw new BusinessException("部门排序号不能为空");
//        }

        // 如果导入时存在登录名
        if (StringUtils.isNotEmpty(userImportDTO.getImportLoginName())) {
            if (!isValidUsername(userImportDTO.getImportLoginName())) {
                throw new BusinessException("登录名只能包含大小写字母、数字和下划线");
            }
            LambdaQueryWrapper<CscpUser> loginNameLqw = new LambdaQueryWrapper<>();
            loginNameLqw.eq(CscpUser::getLoginName, userImportDTO.getImportLoginName());
            int resultLoginNameRow = cscpUserService.selectCountNoAdd(loginNameLqw);
            if (resultLoginNameRow > 0) {
                throw new BusinessException("登录名: {}, 已存在!", userImportDTO.getImportLoginName());
            }
            userImportDTO.setUserName(userImportDTO.getImportLoginName());
            userImportDTO.setRealName(userImportDTO.getRealName().trim());
        } else {
            // 生成唯一的用户名（使用分布式锁确保唯一性）
            String name = SurnamePinyinUtil.getPinyin(userImportDTO.getRealName().trim(),"");
            String mobileSuffix = "";
            if (userImportDTO.getMobile() != null && userImportDTO.getMobile().length() > 6) {
                mobileSuffix = StringUtils.substring(userImportDTO.getMobile(), 6);
            }

            String baseUserName = name + mobileSuffix;
            String userNameLockKey = "username_generation_lock:" + baseUserName;
            Boolean userNameLocked = redisTemplate.opsForValue().setIfAbsent(userNameLockKey, "1", 10, TimeUnit.SECONDS);

            if (Boolean.TRUE.equals(userNameLocked)) {
                try {
                    String userNameMaxNumber = cscpUserService.getUserNameMaxNumber(baseUserName);
                    userImportDTO.setUserName(userNameMaxNumber);
                } finally {
                    redisTemplate.delete(userNameLockKey);
                }
            } else {
                // 如果获取锁失败，使用时间戳确保唯一性
                String timestamp = String.valueOf(System.currentTimeMillis() % 100000);
                userImportDTO.setUserName(baseUserName + "_" + timestamp);
            }
            userImportDTO.setRealName(userImportDTO.getRealName().trim());
        }

        // 设置手机号码
        String mobile = userImportDTO.getMobile();
        if (!com.ctsi.hndx.utils.StringUtils.isMobile(mobile)){
            throw new BusinessException("手机号码格式错误");
        }

        /*boolean existByMobile = cscpUserService.existByMobile(mobile);
        if (existByMobile){
            throw new BusinessException("手机号码已经在系统存在，请不要重复导入");
        }*/

        // 查询部门
        CscpOrgDTO dto = new CscpOrgDTO();
        String departmentName = userImportDTO.getDepartmentName();
        String sort = userImportDTO.getSort();
        String post = userImportDTO.getPost();
        String[] departmentNames = {departmentName};
        String[] sorts = {sort};
        String[] posts = {post};
        String departMentSplit = ",";
        if (departmentName!=null&&departmentName.indexOf(departMentSplit) > 0){
            departmentNames = departmentName.split(departMentSplit);
            if (sort.indexOf(departMentSplit) > 0 ){
                sorts = sort.split(departMentSplit);
            }else {
                throw new BusinessException("此用户有多个部门，请输入多个例如  1,2");
            }
            //  处理职务，职务可能为空，如果有多个部门，每个部门的职务以逗号隔开
            if (StringUtils.isNotEmpty(post) && post.indexOf(departMentSplit) > 0){
                posts = post.split(departMentSplit);
            }

        }else {
            if (!StringUtils.isNumeric(sort)){
                throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
            }
        }

        CscpUserDTO userDTO = new CscpUserDTO();
        BeanUtils.copyProperties(userImportDTO, userDTO);
        userDTO.setLoginName(userImportDTO.getUserName());
        userDTO.setOrderBy(9999);
        userDTO.setDisplay(true);
        userDTO.setWhetherShow(0);
        userDTO.setStatistics(true);
        List<CscpOrgNameIdListDTO> orgNameList = new ArrayList<>();
        for (int i = 0; i< departmentNames.length; i++){
            dto.setOrgName(departmentNames[i]);
            dto.setCompanyId(userImportDTO.getCompanyId());
            List<CscpOrgDTO> orgDTOList = cscpOrgService.criteriaQueryOrgDTONew(dto, userImportDTO.getFlag(), departmentNames.length);

            if (!"QH".equals(userImportDTO.getFlag()) && !userImportDTO.getDepartmentName().contains(",") && departmentName!=null&&CollectionUtils.isEmpty(orgDTOList)) {
                log.error("在该单位没有找到该部门：{}", userImportDTO.getDepartmentName());
                throw new BusinessException("在该单位没有找到该部门名称：" + userImportDTO.getDepartmentName());
            }
            if (departmentName!=null&&orgDTOList.size() > 1) {
                log.error("在该单位下存在多个相同的部门名称：{}", userImportDTO.getDepartmentName());
                throw new BusinessException("在该单位下存在多个相同的部门名称：" + userImportDTO.getDepartmentName());
            }

            //跨单位导入没有匹配到单位
            if (orgDTOList.isEmpty()) {
                log.error("没有找到该单位：{}", userImportDTO.getCompanyName());
                throw new BusinessException("没有找到该单位：" + userImportDTO.getCompanyName());
            }

//            if (orgDTOList.get(0).getType() != 3) {
//                log.error("用户只允许挂在部门下面：{}", userImportDTO.getDepartmentName());
//                throw new BusinessException("用户只允许挂在部门下面：" + userImportDTO.getDepartmentName());
//            }


            // 加部门信息
            CscpOrgNameIdListDTO cscpOrgNameIdListDTO = new CscpOrgNameIdListDTO();
            cscpOrgNameIdListDTO.setId(orgDTOList.get(0).getId());
            cscpOrgNameIdListDTO.setTitle(orgDTOList.get(0).getOrgName());
            String orgSort = sorts[i];
            if (!StringUtils.isNumeric(orgSort)){
                throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
            }
            if (Integer.parseInt(orgSort) > 99999) {
                throw new BusinessException("部门排序号不能超过99999");
            }
            cscpOrgNameIdListDTO.setUserOrgSort(Integer.valueOf(orgSort));
            cscpOrgNameIdListDTO.setPost(userImportDTO.getPost());
            Integer departmengHead = LanguageConvertUtil.stringToInteger(userImportDTO.getDepartmentHead());
            cscpOrgNameIdListDTO.setDepartmentHead(departmengHead == null ? 0:departmengHead);
            // 设置职务
            if (posts.length > i){
                if (StringUtils.isNotEmpty(posts[i])){
                    cscpOrgNameIdListDTO.setPost(posts[i]);
                }
            }else {
                if (StringUtils.isNotEmpty(posts[0])){
                    cscpOrgNameIdListDTO.setPost(posts[0]);
                }
            }
            // 职级
            String rank = userImportDTO.getRank();
            Map<String, String> dictMap = DictionariesCodeUtil.getDictionariesCodeName("userRank");
            if (null != dictMap.get(rank)) {
                cscpOrgNameIdListDTO.setRank(dictMap.get(rank));
            }

            orgNameList.add(cscpOrgNameIdListDTO);
            userDTO.setOrgNameList(orgNameList);

            if (i == 0){
                // 设置默认部门
                userDTO.setDefaultDepart(orgDTOList.get(0).getId());
            }

        }
        // 加默认角色信息
        List<Long> roleIdList = new ArrayList<>();
        roleIdList.add(Long.parseLong(SystemRole.GENERAL_NAME.getId()));
        userDTO.setRoleIds(roleIdList);

        // 是否显示和是否统计
        Boolean display = LanguageConvertUtil.stringToBoolean(userImportDTO.getDisplay());
        Integer whetherShow = LanguageConvertUtil.stringToInteger(userImportDTO.getWhetherShow());
        userDTO.setDisplay(display == null ? true:display);
        userDTO.setWhetherShow(whetherShow);
        Boolean stais = LanguageConvertUtil.stringToBoolean(userImportDTO.getStatistics());
        userDTO.setStatistics(true);

        if(StrUtil.isNotBlank(userImportDTO.getStrId())){
            boolean existByStrId = cscpUserService.existByStrId(userImportDTO.getStrId());
            boolean existByMobile = cscpUserService.existByMobile(mobile);
            if (!existByMobile && existByStrId) {
                throw new BusinessException("唯一信任号strId已存在，请不要重复导入");
            }
            userDTO.setStrId(userImportDTO.getStrId());
        }
        return userDTO;
    }

    /**
     * 组装更新用户入参（不重新生成用户名）
     * @param userImportDTO 导入的用户数据
     * @param existingUser 现有用户数据
     * @return 组装好的用户DTO
     */
    private CscpUserDTO assembledUserDTOForUpdate(UserImportDTO userImportDTO, CscpUser existingUser) {
        if (StringUtils.isBlank(userImportDTO.getRealName())) {
            throw new BusinessException("姓名不能为空");
        }

        if (StringUtils.isNotEmpty(userImportDTO.getIdCardNo())) {
            String idCardNo = userImportDTO.getIdCardNo();
            if (idCardNo == null ||!ID_CARD_PATTERN.matcher(idCardNo).matches()) {
                throw new BusinessException("身份证号码格式不正确，请输入 18 位数字或前 17 位数字加最后一位大写 X");
            }
        }

        // 设置手机号码
        String mobile = userImportDTO.getMobile();
        if (!com.ctsi.hndx.utils.StringUtils.isMobile(mobile)){
            throw new BusinessException("手机号码格式错误");
        }

        // 处理部门信息
        String[] departmentNames = userImportDTO.getDepartmentName().split(",");
        String[] sorts = null;
        String sort = userImportDTO.getSort();
        if (StringUtils.isNotEmpty(sort)) {
            sorts = sort.split(",");
            if (departmentNames.length != sorts.length) {
                throw new BusinessException("部门名称和部门排序号数量不匹配");
            }
            for (String s : sorts) {
                if (!StringUtils.isNumeric(s)){
                    throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
                }
            }
        } else {
            if (!StringUtils.isNumeric(sort)){
                throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
            }
        }

        CscpUserDTO userDTO = new CscpUserDTO();
        BeanUtils.copyProperties(userImportDTO, userDTO);

        // 保持现有用户的ID、strId和登录名
        userDTO.setId(existingUser.getId());
        userDTO.setStrId(existingUser.getStrId());

        // 处理登录名：如果Excel中有指定登录名且与现有不同，则使用Excel中的；否则保持现有的
        if (StringUtils.isNotEmpty(userImportDTO.getImportLoginName())) {
            if (!isValidUsername(userImportDTO.getImportLoginName())) {
                throw new BusinessException("登录名只能包含大小写字母、数字和下划线");
            }
            // 检查新登录名是否与其他用户冲突（排除当前用户）
            LambdaQueryWrapper<CscpUser> loginNameLqw = new LambdaQueryWrapper<>();
            loginNameLqw.eq(CscpUser::getLoginName, userImportDTO.getImportLoginName())
                       .ne(CscpUser::getId, existingUser.getId());
            int resultLoginNameRow = cscpUserService.selectCountNoAdd(loginNameLqw);
            if (resultLoginNameRow > 0) {
                throw new BusinessException("登录名: {}, 已存在!", userImportDTO.getImportLoginName());
            }
            userDTO.setLoginName(userImportDTO.getImportLoginName());
        } else {
            // 保持现有登录名
            userDTO.setLoginName(existingUser.getLoginName());
        }

        userDTO.setOrderBy(9999);
        userDTO.setDisplay(true);
        userDTO.setWhetherShow(0);
        userDTO.setStatistics(true);

        // 处理组织机构信息
        List<CscpOrgNameIdListDTO> orgNameList = new ArrayList<>();
        for (int i = 0; i< departmentNames.length; i++){
            CscpOrgDTO dto = new CscpOrgDTO();
            dto.setOrgName(departmentNames[i]);
            dto.setCompanyId(userImportDTO.getCompanyId());
            List<CscpOrgDTO> orgDTOList = cscpOrgService.criteriaQueryOrgDTONew(dto, userImportDTO.getFlag(), departmentNames.length);
            if (CollectionUtils.isEmpty(orgDTOList)) {
                throw new BusinessException("部门名称: {}, 不存在!", departmentNames[i]);
            }
            CscpOrgNameIdListDTO cscpOrgNameIdListDTO = new CscpOrgNameIdListDTO();
            cscpOrgNameIdListDTO.setId(orgDTOList.get(0).getId());
            cscpOrgNameIdListDTO.setTitle(orgDTOList.get(0).getOrgName());

            // 处理排序号
            String orgSort = sorts != null ? sorts[i] : sort;
            if (!StringUtils.isNumeric(orgSort)){
                throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
            }
            if (Integer.parseInt(orgSort) > 99999) {
                throw new BusinessException("部门排序号不能超过99999");
            }
            cscpOrgNameIdListDTO.setUserOrgSort(Integer.valueOf(orgSort));

            // 设置职务
            cscpOrgNameIdListDTO.setPost(userImportDTO.getPost());

            // 设置部门领导标识
            Integer departmentHead = LanguageConvertUtil.stringToInteger(userImportDTO.getDepartmentHead());
            cscpOrgNameIdListDTO.setDepartmentHead(departmentHead == null ? 0 : departmentHead);

            // 处理职级
            String rank = userImportDTO.getRank();
            if (StringUtils.isNotEmpty(rank)) {
                Map<String, String> dictMap = DictionariesCodeUtil.getDictionariesCodeName("userRank");
                if (null != dictMap.get(rank)) {
                    cscpOrgNameIdListDTO.setRank(dictMap.get(rank));
                } else {
                    cscpOrgNameIdListDTO.setRank(rank);
                }
            }

            orgNameList.add(cscpOrgNameIdListDTO);

            if (i == 0){
                // 设置默认部门
                userDTO.setDefaultDepart(orgDTOList.get(0).getId());
            }
        }
        userDTO.setOrgNameList(orgNameList);

        // 加默认角色信息
        List<Long> roleIdList = new ArrayList<>();
        roleIdList.add(Long.parseLong(SystemRole.GENERAL_NAME.getId()));
        userDTO.setRoleIds(roleIdList);

        // 是否显示和是否统计
        Boolean display = LanguageConvertUtil.stringToBoolean(userImportDTO.getDisplay());
        Integer whetherShow = LanguageConvertUtil.stringToInteger(userImportDTO.getWhetherShow());
        userDTO.setDisplay(display == null ? true : display);
        userDTO.setWhetherShow(whetherShow == null ? 1 : whetherShow);
        Boolean statistics = LanguageConvertUtil.stringToBoolean(userImportDTO.getStatistics());
        userDTO.setStatistics(statistics == null ? true : statistics);

        // 处理唯一信任号
        if(StrUtil.isNotBlank(userImportDTO.getStrId())){
            boolean existByStrId = cscpUserService.existByStrId(userImportDTO.getStrId());
            boolean existByMobile = cscpUserService.existByMobile(mobile);
            if (!existByMobile && existByStrId) {
                throw new BusinessException("唯一信任号strId已存在，请不要重复导入");
            }
            userDTO.setStrId(userImportDTO.getStrId());
        }

        return userDTO;
    }

    private boolean isValidUsername(String username) {
        return USERNAME_PATTERN.matcher(username).matches();
    }

    private List<String> isValidIdCardNo(List<String> idCardNoList,String idCardNo) {
        if (StringUtils.isNotEmpty(idCardNo)) {
            if (SysConstant.DELAULT_ID_CARD_NO.equals(idCardNo)) {
                return idCardNoList;
            }
            // 过滤重名单位
            if (!idCardNoList.contains(idCardNo)) {
                idCardNoList.add(idCardNo);
            } else {
                log.info("excel中存在身份证号码重复: {}", idCardNo);
                throw new BusinessException("excel中存在身份证号码重复: " + idCardNo);
            }
        }
        return idCardNoList;
    }
}

