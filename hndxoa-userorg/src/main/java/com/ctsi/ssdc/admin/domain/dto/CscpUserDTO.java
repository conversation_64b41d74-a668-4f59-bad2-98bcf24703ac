package com.ctsi.ssdc.admin.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.ctsi.hndx.constant.SysRegEx;
import com.ctsi.hndx.enums.UserType;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Generator
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户的实体DTO")
public class CscpUserDTO extends BaseDtoEntity implements Serializable {
    /**
     * 用户的默认排序号
     */
    public static final Integer DEFAULT_USER_SORT = 999;

    private static final long serialVersionUID = 3169182298684310412L;

    @ApiModelProperty(value = "用户登录名称")
    @NotBlank(message = "登录名不能为空")
    private String loginName;

    @ApiModelProperty(value = "用户密码")
    @JsonIgnore
    private String password;


    /**
     * 姓名
     */
    @NotBlank(message = "真实用户姓名不能为空且长度必须大于2位")
    @Size(min = 2)
    @ApiModelProperty(value = "用户真实姓名")

    private String realName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "用户手机号码")
    @Pattern(regexp = SysRegEx.MOBILE_PATTERN, message = "手机号格式有误")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 单位id,登录时所用的单位的id
     */
    @ApiModelProperty(value = "登录时所用的单位的ID", readOnly = true)
    private Long companyId;

    @ApiModelProperty(value = "登录时所用的单位的ID", readOnly = true)
    private List<Long> companyIdList;

    @ApiModelProperty(value = "单位的顶层机构的Path")
    private List<String> rootOrgCodePath;

    /**
     * 部门名称，登录时所使用的部门名称
     */
    @ApiModelProperty(value = "登录时所使用的单位名称", readOnly = true)
    private String companyName;

    /**
     * 是否加水印
     */
    @ApiModelProperty(value = "是否加水印", readOnly = true)
    private Integer hasWatermark;

    /**
     * 是否分屏
     */
    @ApiModelProperty(value = "正文编辑是否分屏，1表示分屏，0表示不分屏")
    private Integer splitview;

    /**
     * 单位共享云盘空间大小，单位GB，默认2G
     */
    @ApiModelProperty(value = "单位共享云盘空间大小，单位GB，默认2G")
    private Integer cloudDiskSpaceSize;

    /**
     * 部门id，登录时所使用的部门id
     */
    @ApiModelProperty(value = "登录时所使用的部门ID", readOnly = true)
    private Long departmentId;

    /**
     * 部门名称，登录时所使用的部门名称
     */
    @ApiModelProperty(value = "登录时所使用的部门名称", readOnly = true)
    private String departmentName;
    // 部门编码
    private String departmentCode;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码", readOnly = true)
    private String code;



    @ApiModelProperty(value = "租户ID", readOnly = true)
    private Long tenantId;

    @ApiModelProperty(value = "用于租户体系下查询用户")
    private List<Long> tenantIdList;



    /**
     * 最后一次登陆时间
     */
    @ApiModelProperty(value = "最后一次登陆时间")
    private LocalDateTime lastLogin;

    @ApiModelProperty(value = "用户表排序号")
    private Integer orderBy;


    @ApiModelProperty(value = "用户部门中间表排序号")
    private Integer sort;

    @ApiModelProperty(value = "组织机构表的排序号")
    private Integer orgSort;

    @ApiModelProperty(value = "用户状态：1表示激活 0表示禁用")
    private Integer status;

    @ApiModelProperty(value = "用户是否显示")
    private Boolean display;

    @ApiModelProperty(value = "手机端app版本号")
    @JsonIgnore
    private Integer appVersion;

    @ApiModelProperty(value = "是否统计")
    private Boolean statistics;

    @ApiModelProperty(value = "办公电话")
    private String officePhone;

    /**
     * 保存用户角色
     */

    @ApiModelProperty(value = "用户的角色id集合")
    private List<Long> roleIds;

    @ApiModelProperty(value = "是否单位管理员，1-是，0-否")
    private String unitAdminFlag;
    @ApiModelProperty(value = "角色idStr")
    private String roleIdsStr;

    @ApiModelProperty(value = "用户的移动端角色id集合")
    private List<Long> appRoleIds;

    @ApiModelProperty(value = "用户的角色名称集合")
    private List<CscpRoleNameIdsDTO> roleNames;

    @ApiModelProperty(value = "用户的角色名称集合")
    private List<CscpRoleNameIdsDTO> appRoleNames;

    @ApiModelProperty(value = "用户的组织机构ids集合")
    private List<Long> orgIdList;
    private Long orgId;
    @ApiModelProperty(value = "用户单位的统一信用代码")
    @TableField(exist = false)
    private Map<Long, String> creditCodeMap;

    @ApiModelProperty(value = "用户的组织机构名称集合")
    private List<CscpOrgNameIdListDTO> orgNameList;

    @ApiModelProperty(value = "多单位多部门默认登录时此项目必须填写")
    private Long defaultDepart;


    /**
     * 用户的类别
     */
    @ApiModelProperty(value = "当前登录用户的类型")
    private UserType userType;


    @ApiModelProperty(value = "当前登录人的单位信息")
    private CscpOrgDTO cscpOrgDTO;

    @ApiModelProperty(value = "分管领导信息")
    private CscpUserDTO branchLeaderDTO;

    @ApiModelProperty(value = "部门领导信息")
    private List<CscpUserDTO> departmentHeadDTOList;

    /**
     * 是否分管领导(1个部门只能有1个)
     * 1表示为分管领导
     */
    @ApiModelProperty("是否分管领导")
    private Integer branchLeader;

    /**
     * 是否为部门领导(1个部门可以设置多个)
     * 1表示为部门领导
     */
    @ApiModelProperty("是否为部门领导")
    private Integer departmentHead;

    @ApiModelProperty("职务")
    private String post;

    @ApiModelProperty("职级")
    private String rank;

    @ApiModelProperty("需要排除的部门ID")
    private Long excludeDepartmentId;

    /**
     * CRM受理的客户类型（单位类型：1：标准型，2：项目型）
     */
    @TableField(exist = false)
    private String crmTenantType;

    /**
     * 领航群组号
     */
    @TableField(exist = false)
    private String groupNumber;

    @ApiModelProperty(value = "主要资费")
    private String mainTariff;

    @ApiModelProperty(value = "附加资费")
    private String surcharge;

    /**
     * 操作标识
     */
    @TableField(exist = false)
    private String oPFlag;

    /**
     * 新增类型
     */
    @ApiModelProperty(value = "新增类型 1:别的服务调用该服务新增")
    private Integer AddType;

    @ApiModelProperty(value = "是否显示 0：不显示 1：显示 ")
    private Integer whetherShow;

    /**
     * 用户性别 0：男， 1：女
     */
    @ApiModelProperty(value = "用户性别 0：男， 1：女")
    private Integer sex;

    /**
     * 是否手写签批  0-不是 1-是
     */
    @ApiModelProperty(value = "是否手写签批  0-不是 1-是")
    private Integer isWriteSign;

    /**
     * 秘书名称
     */
    @ApiModelProperty(value = "秘书名称")
    private String secretaryName;

    /**
     * 秘书手机号
     */
    @ApiModelProperty(value = "秘书手机号")
    private String secretaryPhone;

    /**
     * APP版本号
     */
    @ApiModelProperty(value = "APP版本号")
    private String appVersionName;

    /**
     * 撤回签批条件  0-下一步处理人未阅 1-无
     */
    @ApiModelProperty(value = "撤回签批条件  0-下一步处理人未阅 1-无")
    private Integer withdrawCondition;

    /**
     * 审批后短信提醒  0-否 1-是
     */
    @ApiModelProperty(value = "审批后短信提醒  0-否 1-是")
    private Integer auditSms;

    /**
     * 通讯录是否显示本单位  0-不是 1-是
     */
    @ApiModelProperty(value = "通讯录是否显示本单位  0-不是 1-是")
    private Integer isAddressUnit;

    /**
     * 备用手机号（用英文逗号分割号码）
     */
    @ApiModelProperty(value = "备用手机号（用英文逗号分割号码）")
    private String backupMobile;


    /**
     * 用于关联商信的ID
     */
    @ApiModelProperty(value = "商信ID")
    private String strId;

    /**
     * 用户密级（10:普通　20:秘密　30:机密）
     */
    @ApiModelProperty(value = "用户密级（10:普通　20:秘密　30:机密）")
    private String strClassified;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号")
    private String strIdCardNo;


    /**
     * 单位信任号
     */
    @ApiModelProperty(value = "单位信任号")
    private String strUnitTrustNo;


    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源,统一支撑平台，为空则是系统")
    private String userOrigin;

    /**
     * 签名图片url
     */
    @ApiModelProperty(value = "签名图片url")
    @TableField(value = "signature_image_url")
    private String signatureImageURL;

    @ApiModelProperty(value = "审核状态 0:待审核 1:通过 2:驳回")
    private Integer examineStatus;

    @ApiModelProperty(value = "审核状态 0:待审核 1:通过 2:驳回")
    private String examineStatusName;

    @ApiModelProperty(value = "涉密类别code")
    private String securityClassificationCode;

    @ApiModelProperty(value = "涉密类别code")
    private String securityClassificationCodeName;


    @ApiModelProperty(value = "盖章图片地址")
    @TableField(value = "stamp_url")
    private String stampUrl;

    @ApiModelProperty(value = "起始号")
    @TableField(value = "start_no")
    private Integer startNo;

    @ApiModelProperty(value = "用户所属部门列表")
    private List<CscpUserOrg> orgList;
    @ApiModelProperty(value = "用户管理部门列表")
    private List<TDeptManagementAuthorityDTO> orgManagementList;

    @ApiModelProperty(value = "卫士通统一身份认证系统用户ID")
    private String westoneUserId;

    @ApiModelProperty(value = "卫士通统一身份认证系统用户推送状态，unPushed-未推送，pushed-已推送")
    private String westoneUserPushedStatus;

    @ApiModelProperty("手机号码HMAC值，完整性校验")
    private String hmacMobile;

    @ApiModelProperty("手机号码是否完整，0-完整，1-不完整")
    private Integer dataIsIntegrity;

    @ApiModelProperty("身份证号码")
    private String idCardNo;

    @ApiModelProperty("同步推送记录id")
    private String syncHistoryId;

    @ApiModelProperty("人员标签集合")
    private List<String> personLabelList;

    @ApiModelProperty("是否显示通讯录,0不显示，1显示")
    private Integer isDisplay;

    @TableField(exist = false)
    private String unitFlag;

    @TableField(exist = false)
    private Long cuoId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人id")
    private Long updateBy;

    @ApiModelProperty("修改人")
    private String updateName;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("人员标签，多个标签用英文逗号分隔")
    private String personLabel;

    @ApiModelProperty("推送应用记录")
    private String pushAppCode;

    @ApiModelProperty("进入单位时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryTime;

    @ApiModelProperty("入职时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate offerTime;

    @ApiModelProperty("新增的推送应用")
    private List<String> addAppCodes;
    @ApiModelProperty("更新的推送应用")
    private List<String> updateAppCodes;
    @ApiModelProperty("被取消授权的推送应用")
    private List<String> removeAppCodes;

    public void setSecurityClassificationCode(String securityClassificationCode) {
        this.securityClassificationCode = securityClassificationCode;
        if (StringUtils.isNotEmpty(securityClassificationCode)) {
            try {
                this.strClassified = ((Integer.parseInt(securityClassificationCode) + 1) * 10) + "";
            } catch (NumberFormatException e) {
                this.strClassified = "10";
            }
        }
    }

    public boolean conditionDepFlag() {
        if (StringUtils.isNotEmpty(departmentName)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(rootOrgCodePath)) {
            return true;
        }
        if (orgId != null) {
            return true;
        }
        return false;
    }
    @ApiModelProperty("userIds")
    private List<Long> userIds;

    public void setNameHandle() {
        if (this.realName != null) {
            this.realName = this.realName.replaceAll(" ", "");
        }
    }

    public void buildAddAppCodes(String oldPushAppCode) {
        List<String> olds;
        List<String> news;
        if (StringUtils.isNotEmpty(oldPushAppCode)) {
            olds = Arrays.asList(oldPushAppCode.split(","));
        } else {
            olds = new ArrayList<>();
        }
        if (StringUtils.isNotEmpty(pushAppCode)) {
            news = Arrays.asList(pushAppCode.split(","));
        } else {
            news = new ArrayList<>();
        }
        addAppCodes = news.stream().filter(appCode -> !olds.contains(appCode)).collect(Collectors.toList());
        removeAppCodes = olds.stream().filter(appCode -> !news.contains(appCode)).collect(Collectors.toList());
        updateAppCodes = olds.stream().filter(news::contains).collect(Collectors.toList());
    }
}
