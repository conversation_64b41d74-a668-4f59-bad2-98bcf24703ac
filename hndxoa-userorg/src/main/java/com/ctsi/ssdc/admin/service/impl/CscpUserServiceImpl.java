package com.ctsi.ssdc.admin.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.addrbook.entity.TAddressBook;
import com.ctsi.hndx.addrbook.entity.dto.CreateTAddressBookDTO;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.annotations.MybatisNoSelect;
import com.ctsi.hndx.cadre.entity.BizCadreInformation;
import com.ctsi.hndx.cadre.mapper.BizCadreInformationMapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.constant.UserConstant;
import com.ctsi.hndx.encryption.Base64Encrypt;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.encryption.Sm4Encrypt;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.enums.UserExamineStatusEnum;
import com.ctsi.hndx.enums.UserType;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.exception.DuplicateUserNameException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.hndx.systenant.mapper.TSysTenantMapper;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.sms.smssend.SmsSendEnum;
import com.ctsi.sms.smssend.SmsSendUtil;
import com.ctsi.ssdc.admin.consts.ComponentConstant;
import com.ctsi.ssdc.admin.domain.*;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;
import com.ctsi.ssdc.admin.mapping.CscpUserMapping;
import com.ctsi.ssdc.admin.repository.*;
import com.ctsi.ssdc.admin.service.*;
import com.ctsi.ssdc.dto.QuerySystemLogDTO;
import com.ctsi.ssdc.dto.QueryUserExamineDTO;
import com.ctsi.ssdc.dto.QueryUserLockDTO;
import com.ctsi.ssdc.entity.BizHrsUserInfo;
import com.ctsi.ssdc.log.SystemLogOperateQueue;
import com.ctsi.ssdc.log.SystemLogOperateStock;
import com.ctsi.ssdc.login.RequestUserContextUtil;
import com.ctsi.ssdc.mapper.BizHrsUserInfoMapper;
import com.ctsi.ssdc.model.*;
import com.ctsi.ssdc.repository.SystemLogOperationHistoryMapper;
import com.ctsi.ssdc.repository.SystemLogOperationMapper;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.CscpUserNumberDTO;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ITDeptManagementAuthorityService;
import com.ctsi.ssdc.util.RSAUtil;
import com.ctsi.ssdc.util.RedisUtil;
import com.ctsi.ssdc.util.RequestUtil;
import com.ctsi.ssdc.util.SpringUtil;
import com.ctsi.ssdc.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing CscpUser.
 *
 * <AUTHOR> biyi generator
 */
@Service
public class CscpUserServiceImpl extends SysBaseServiceImpl<CscpUserRepository, CscpUser> implements CscpUserService {

    private final Logger log = LoggerFactory.getLogger(CscpUserServiceImpl.class);
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^\\d{17}[0-9X]$");

    private static final Set<String> SYSTEM_USERS = Arrays.stream(new String[]{"admin", "aqsjgly", "aqbmgly", "wrdcadmin"}).collect(Collectors.toSet());

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private BizHrsUserInfoMapper bizHrsUserInfoMapper;

    @Autowired
    private CscpUserWorkGroupRepository cscpUserWorkGroupRepository;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpUserRoleRepository cscpUserRoleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private CscpUserPasswordChangeLogRepository cscpUserPasswordChangeLogRepository;

    @Autowired
    private CscpUserRoleService cscpUserRoleService;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;
    @Autowired
    private CscpUserDeleteRecordService cscpUserDeleteRecordService;
    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private TSysTenantMapper tSysTenantMapper;

    @Autowired
    private ITAppUserRoleService itAppUserRoleService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private BizCadreInformationMapper bizCadreInformationMapper;
    @Autowired
    private ITDeptManagementAuthorityService tDeptManagementAuthorityService;
    @Autowired
    private CompanySaveStrategyServiceImpl companySaveStrategyService;
    @Autowired
    private DsOptContext dsOptContext;

    @Value("${ctsi.RSA-prikey:}")
    private String rsaPrikey = "";

    @Value("${ctsi.password-check.expire-months:3}")
    private int expireMonth;

    @Value("${sms.validTime:90}")
    private Long validTime;

    @Value("${sms.sendSmsTime:60}")
    private Long sendSmsTime;

    private String NO_VALIDATION = "noValidation";

    private static final Integer DEPARTMENT_TYPE = 3; //机构类型：部门

    @Autowired
    private ContainDigitCheck containDigitCheck;

    @Autowired
    private KeyBoardCheckNew keyBoardCheckNew;

    @Autowired
    private LengthCheck lengthCheck;

    @Autowired
    private LowerCaseCheck lowerCaseCheck;

    @Autowired
    private UpperCaseCheck upperCaseCheck;

    @Autowired
    private SpecialCharCheck specialCharCheck;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ITAddressBookService tAddressBookService;

    @Autowired
    private SystemLogOperationMapper systemLogOperationMapper;

    @Autowired
    private SystemLogOperationHistoryMapper systemLogOperationHistoryMapper;

    @Autowired
    private CscpLogOperationRepository cscpLogOperationRepository;

    @Autowired
    private CscpUserMapping userMapping;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private Executor asyncServiceExecutor;


    /**
     * 同步用户到统一身份认证系统
     *
     * @param cscpUser
     */
    public void asyncPushUserToWestoneUas(CscpUser cscpUser) {
//        WestoneUserSyncService westoneUserService = new WestoneUserSyncService(this, this.sysConfigService, this.redisUtil);
//        if (westoneUserService.isNeedSyncUserToWestoneUas()) {
//            SecurityContext securityContext = SecurityContextHolder.getContext();
//            CompletableFuture.runAsync(() -> {
//                SecurityContextHolder.setContext(securityContext);
//                westoneUserService.syncUserToWestoneUas(cscpUser);
//            });
//        }
    }

    /**
     * insert a cscpUser.
     *
     * @param cscpUserDTO the entity to insert
     * @return the persisted entity
     */
    @Override
    @Transactional
    public CscpUserDTO insert(CscpUserDTO cscpUserDTO) {
        String defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        if (org.apache.commons.lang.StringUtils.isBlank(cscpUserDTO.getPassword())
                && StringUtils.isNotBlank(defaultPassword)) {
            cscpUserDTO.setPassword(passwordEncoder.encode(defaultPassword));
        }

        if (!SysConstant.DELAULT_MOBILE.equals(cscpUserDTO.getMobile())) {
            if (!"常德".equals(cscpUserDTO.getUserOrigin())) {
                // 先判断手机号码重复,重复不创建用户
                CscpUser user = selectUserByMobile(cscpUserDTO.getMobile());
                if (user.getMobile() != null) {
                    BeanUtils.copyProperties(user, cscpUserDTO);
                    return cscpUserDTO;
                    // throw new BusinessException(ResultCode.USER_MOBILE_EXISTED);
                }
            }
        }

        // 并行执行三个检查
        CompletableFuture<Boolean> usernameCheckFuture = CompletableFuture.supplyAsync(() ->
                existByUsername(cscpUserDTO.getLoginName())
        );

        CompletableFuture<Boolean> idCardCheckFuture = CompletableFuture.supplyAsync(() ->
                this.existIdCardNoRepeat(cscpUserDTO)
        );

        CompletableFuture<Boolean> strIdCheckFuture = CompletableFuture.supplyAsync(() -> {
            if (StrUtil.isNotBlank(cscpUserDTO.getStrId())) {
                return this.existByStrId(cscpUserDTO.getStrId());
            }
            return false;
        });

        try {
            // 等待所有检查完成并获取结果
            boolean existByUsername = usernameCheckFuture.get();
            if (existByUsername) {
                throw new BusinessException(ResultCode.USERNAME_HAS_EXISTED);
            }

            boolean existIdCardNo = idCardCheckFuture.get();
            if (existIdCardNo) {
                throw new BusinessException(ResultCode.USER_ID_CARD_NO_EXISTED);
            }

            boolean existByStrId = strIdCheckFuture.get();
            if (existByStrId) {
                throw new BusinessException("唯一信任号strId已存在，请不要重复保存");
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("并行检查用户信息时发生错误", e);
            throw new BusinessException("检查用户信息失败");
        }

        CscpUser cscpUser = new CscpUser();

        //名字
        RealNameSplitType realNameSplitType = StringSplitUtils.realNameSplit(cscpUserDTO.getRealName());
        // //姓
        // cscpUser.setRealNameStart(realNameSplitType.getRealNameStart());
        // //名
        // cscpUser.setRealNameEnd(realNameSplitType.getRealNameEnd());
        cscpUser.setRealNameStart(cscpUserDTO.getRealName());
        cscpUser.setRealNameEnd(cscpUserDTO.getRealName());

        cscpUser.setMobileStart(cscpUserDTO.getMobile());
        cscpUser.setMobileMiddle(cscpUserDTO.getMobile());
        cscpUser.setMobileEnd(cscpUserDTO.getMobile());
        cscpUser.setMobile(cscpUserDTO.getMobile()); // 重新加密设值
        // 获取人员标签列表（可能为null）
        List<String> labelList = cscpUserDTO.getPersonLabelList();
        // 只有当列表不为空且非空时才处理
        if (labelList != null && !labelList.isEmpty()) {
            String personLabel = String.join(",", labelList);
            cscpUser.setPersonLabel(personLabel);
        }

        // //电话开头
        // MobileSplitType mobileSplitType = StringSplitUtils.mobileSplit(cscpUserDTO.getMobile());
        // cscpUser.setMobileStart(mobileSplitType.getMobileStart());
        // //电话中间
        // cscpUser.setMobileMiddle(mobileSplitType.getMobileMiddle());
        // //电话结尾
        // cscpUser.setMobileEnd(mobileSplitType.getMobileEnd());

        BeanUtils.copyProperties(cscpUserDTO, cscpUser);

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }

        List<CscpOrgNameIdListDTO> orgIdList = cscpUserDTO.getOrgNameList();
        if (CollectionUtils.isNotEmpty(orgIdList) && orgIdList.size() > 1 && Objects.isNull(cscpUserDTO.getDefaultDepart())) {
            throw new BusinessException(ResultCode.USER_NO_DEFAULT_LOGIN_ERROR);
        }
    /*if (cscpUser.getOrderBy() == null) {
        cscpUser.setOrderBy(this.getUserSorted(cscpUserDTO));
    }*/

        // 设置是否显示为true, 是否统计默认为false
        if (Objects.isNull(cscpUser.getDisplay())) {
            cscpUser.setDisplay(true);
        }
        if (Objects.isNull(cscpUser.getStatistics())) {
            cscpUser.setStatistics(false);
        }
        // 设置默认办公电话
        if (StringUtils.isBlank(cscpUser.getOfficePhone())) {
            cscpUser.setOfficePhone(SysConstant.DELAULT_MOBILE);
        }

        // 设置租户id
        cscpUser.setTenantId(Long.parseLong(ComponentConstant.USER_TENANTID));
        Long id = cscpUser.getId();
        if (id == null || id == 0) {
            cscpUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        }
        // 如果商信没有之用主键id补充
        if (com.ctsi.hndx.utils.StringUtils.isEmpty(cscpUser.getStrId())) {
            cscpUser.setStrId(String.valueOf(cscpUser.getId()));
            cscpUser.setStrIdCardNo(cscpUserDTO.getIdCardNo() != null ? cscpUserDTO.getIdCardNo() : null);

        }
        // 保存用户信息
        cscpUser.setNameHandle();
        cscpUserDTO.setNameHandle();
        cscpUserRepository.insert(cscpUser);
        cscpUserDTO.setId(cscpUser.getId());
        if (cscpUserDTO.getOrderBy() == null) {
            cscpUserDTO.setOrderBy(9999);
        }
        cscpUserOrgService.saveUserOrgRel(cscpUser.getId(), cscpUserDTO);

        // 获取当前SecurityContext
        SecurityContext securityContext = SecurityContextHolder.getContext();

        // 使用CompletableFuture并行执行方法
        List<CompletableFuture<Boolean>> futures = Arrays.asList(
                CompletableFuture.supplyAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    return this.updateUserSort(cscpUserDTO);
                }),
                CompletableFuture.supplyAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    return cscpUserOrgService.updateUserOrgSort(cscpUserDTO);
                }),
                CompletableFuture.supplyAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    cscpUserRoleService.saveUserRoles(cscpUser.getId(), cscpUserDTO.getRoleIds());
                    return true;
                }),
                CompletableFuture.supplyAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    return itAppUserRoleService.insertRolesToUser(cscpUserDTO.getAppRoleIds(), cscpUser.getId());
                }),
        /*        CompletableFuture.supplyAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    cscpUserOrgService.saveUserOrgRel(cscpUser.getId(), cscpUserDTO);
                    return true;
                }),*/
                //同步通讯录
                CompletableFuture.supplyAsync(() -> {
                    CreateTAddressBookDTO build = CreateTAddressBookDTO.builder()
                            .defaultPhone(cscpUserDTO.getMobile())
                            .realName(cscpUserDTO.getRealName())
                            .sort(cscpUserDTO.getSort())
                            .whetherShow(1)
                            .AddType(1)
                            .sex(cscpUserDTO.getSex())
                            .telephone(cscpUserDTO.getOfficePhone())
                            .secretaryName(cscpUserDTO.getSecretaryName())
                            .secretaryPhone(cscpUserDTO.getSecretaryPhone())
                            .userId(cscpUser.getId()).build();
                    List<Long> roleIds = cscpUserDTO.getRoleIds();
                    //通讯录有单位管理员角色默认不显示
                    if (CollUtil.isNotEmpty(roleIds) && roleIds.contains(Convert.toLong(SystemRole.COMPANY_ROLE.getId()))) {
                        build.setDisplay(0);
                    } else {
                        build.setDisplay(1);
                    }
                    if (cscpUserDTO.getOrgNameList() != null && cscpUserDTO.getOrgNameList().size() > 0) {
                        build.setJobTitle(cscpUserDTO.getOrgNameList().get(0).getPost());
                    }
                    tAddressBookService.create(build);
                    return true;
                })
        );

        try {
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            // 异步任务异常处理
            log.error("用户创建失败" + e.getMessage());
            throw new BusinessException("用户创建失败", e);
        }

        BeanUtils.copyProperties(cscpUser, cscpUserDTO);

        if (!StringUtils.isEmpty(cscpUserDTO.getStrId())) {
            this.baseMapper.updateSxDataSyncFlag(cscpUserDTO.getStrId());
        }

        // ===================同步用户到统一身份认证系统===================
        asyncPushUserToWestoneUas(cscpUser);
        // ===================同步用户到统一身份认证系统===================

        return cscpUserDTO;
    }


    /**
     * update a cscpUser.
     *
     * @param cscpUserDTO the entity to update
     * @return the persisted entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    /*@Caching(evict = {
            @CacheEvict(value = "userCache-findByCurrentUserName", key = "#cscpUserDTO.loginName"),
            @CacheEvict(value = "userCache-getUserByUsernameOrMobile", key = "#cscpUserDTO.loginName"),
            @CacheEvict(value = "userCache-getUserByUsernameOrMobile", key = "#cscpUserDTO.mobile")
    })*/
    public CscpUserDTO update(CscpUserDTO cscpUserDTO) {
        CscpUser sourceCscpUser = cscpUserRepository.selectById(cscpUserDTO.getId());
        cscpUserDTO.buildAddAppCodes(sourceCscpUser.getPushAppCode());
        // 先判断手机号码是否变更
        if (!cscpUserDTO.getMobile().equals(sourceCscpUser.getMobile())) {

            boolean mobilePhone = existByMobile(cscpUserDTO.getMobile());
            if (mobilePhone) {
                throw new BusinessException(ResultCode.USER_MOBILE_EXISTED);
            }

        }

        if (StringUtils.isNotEmpty(cscpUserDTO.getIdCardNo())) {
            if (null != sourceCscpUser.getIdCardNo() && !cscpUserDTO.getIdCardNo().equals(sourceCscpUser.getIdCardNo())) {

                boolean existIdCardNo = this.existIdCardNoRepeat(cscpUserDTO);
                if (existIdCardNo) {
                    throw new BusinessException(ResultCode.USER_ID_CARD_NO_EXISTED);
                }
            }
        }

        if ("admin".equals(SecurityUtils.getCurrentUserName()) && cscpUserDTO.getId() > 3
                && !sourceCscpUser.getLoginName().equals(cscpUserDTO.getLoginName())) {
            // 修改了登录名
            this.updateLoginName(cscpUserDTO, new AtomicInteger(1));
        }


        if (StringUtils.isNotEmpty(cscpUserDTO.getIdCardNo())) {
            String idCardNo = cscpUserDTO.getIdCardNo();
            if (idCardNo == null || !ID_CARD_PATTERN.matcher(idCardNo).matches()) {
                throw new BusinessException("身份证号码格式不正确，请输入 18 位数字或前 17 位数字加最后一位大写 X");
            }
        }

        CscpUser cscpUser = new CscpUser();
        // RealNameSplitType realNameSplitType = StringSplitUtils.realNameSplit(cscpUserDTO.getRealName());
        // cscpUser.setRealNameStart(realNameSplitType.getRealNameStart());
        // cscpUser.setRealNameEnd(realNameSplitType.getRealNameEnd());
        cscpUser.setRealNameStart(cscpUserDTO.getRealName());
        cscpUser.setRealNameEnd(cscpUserDTO.getRealName());
        // 设置用户默认排序
        if (Objects.isNull(cscpUserDTO.getOrderBy())) {
            cscpUser.setOrderBy(9999);
        }
        // 修改用户表排序号,大于该排序的该租户下的所有用户表的排序号全部 + 1
        this.updateUserSort(cscpUserDTO);
        BeanUtils.copyProperties(cscpUserDTO, cscpUser);

        //保存用户详细信息
        cscpUser.setMobileStart(cscpUserDTO.getMobile());
        cscpUser.setMobileMiddle(cscpUserDTO.getMobile());
        cscpUser.setMobileEnd(cscpUserDTO.getMobile());
        cscpUser.setMobile(cscpUserDTO.getMobile()); // 重新加密设值
        // 获取人员标签列表（可能为null）
        List<String> labelList = cscpUserDTO.getPersonLabelList();
        if (labelList != null && !labelList.isEmpty()) {
            String personLabel = String.join(",", labelList);
            cscpUser.setPersonLabel(personLabel);
        } else {
            cscpUser.setPersonLabel("");
        }

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = sourceCscpUser.getLoginName() + sourceCscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }
        // 如果手机号发生变化，清空卫士通ID，执行卫士通新增逻辑
        if (sourceCscpUser.getMobile() != null && !sourceCscpUser.getMobile().equals(cscpUser.getMobile())) {
            cscpUser.setWestoneUserId(null);
        }

        cscpUserRepository.updateById(cscpUser);

        // 保存用户角色
        cscpUserRoleService.saveUserRoles(cscpUser.getId(), cscpUserDTO.getRoleIds());

        // 保存App角色用户关系
        if (CollectionUtils.isNotEmpty(cscpUserDTO.getAppRoleIds())) {
            // todo  暂时没找到解决方案，先临时只能通过普通用户
            if (SecurityUtils.isGeneralName()) {
                itAppUserRoleService.insertRolesToUser(cscpUserDTO.getAppRoleIds(), cscpUser.getId());
            }
        }

        // 修改组织机构排序，大于该排序的所有组织机构的排序号全部+1
        cscpUserOrgService.updateUserOrgSort(cscpUserDTO);

//        long currentUserId = SecurityUtils.getCurrentUserId();
//        CscpUserDTO byUserId = this.findByUserId(currentUserId);
//        if (byUserId.getCompanyId() != null && byUserId.getTenantId() != null) {
//            //保存用户机构
//            cscpUserOrgService.saveUserOrgRel(cscpUser.getId(), cscpUserDTO.getOrgIdList());
//        }
        cscpUserOrgService.saveUserOrgRel(cscpUser.getId(), cscpUserDTO);
        BeanUtils.copyProperties(cscpUser, cscpUserDTO);

        // 更新管理部门列表
        if (CollectionUtils.isNotEmpty(cscpUserDTO.getOrgManagementList())) {
            tDeptManagementAuthorityService.updateManageDept(cscpUserDTO.getOrgManagementList());
        } else {
            tDeptManagementAuthorityService.deleteManageDept(cscpUserDTO.getId());
        }


        //===================同步通讯录修改开始=============================
        if (Objects.isNull(cscpUserDTO.getAddType())) {
            CreateTAddressBookDTO build = CreateTAddressBookDTO.builder()
                    .realName(cscpUserDTO.getRealName())
                    .defaultPhone(cscpUserDTO.getMobile())
                    .sort(cscpUserDTO.getSort())
                    .AddType(1)
                    .sex(cscpUserDTO.getSex())
                    .telephone(cscpUserDTO.getOfficePhone())
                    .secretaryName(cscpUserDTO.getSecretaryName())
                    .secretaryPhone(cscpUserDTO.getSecretaryPhone())
                    .whetherShow(cscpUserDTO.getWhetherShow())
                    .userId(cscpUserDTO.getId()).build();
            if (cscpUserDTO.getOrgNameList() != null && cscpUserDTO.getOrgNameList().size() > 0) {
                build.setJobTitle(cscpUserDTO.getOrgNameList().get(0).getPost());
            }
            try {
                tAddressBookService.update(build, 1);
            } catch (Exception e) {
                log.error("更新通讯录数据报错{}", e.getMessage());
            }
        }
        //===================同步通讯录修改结束=============================

        //===================同步修改领导干部配置信息=============================
        try {
//        CscpUserDTO cu = this.findByUserId(cscpUserDTO.getId());
            //获取模型名字和
            Map<String, Object> formParams = new HashMap<>();
            formParams.put("leaderId", cscpUserDTO.getId());
            formParams.put("leaderName", cscpUserDTO.getRealName());
//        formParams.put("leaderCompanyName", cu.getCompanyName());
//        formParams.put("leaderCompanyId", cu.getCompanyId());
            formParams.put("leaderDuties", cscpUserDTO.getPost());
            formParams.put("leaderPhone", cscpUserDTO.getMobile());
            ResultVO<String> responseBO = RestTemplateRequestJWT.post(SpringUtil.getLocalUrlPort() + "/api/bizLeaderOutgoConfig/updateByLeaderId", formParams, ResultVO.class);
            if (!ResultCode.SUCCESS.code().equals(responseBO.getResultCode())) {
                throw new BusinessException(responseBO.getResultMsg());
            }
        } catch (Exception e) {
            //更新异常
        }

        //===================同步修改领导干部配置信息结束=============================

        //===================同步修改分发组数据不同步问题=============================
        try {
            //获取模型名字和
            Map<String, Object> GroupPopulationUserParams = new HashMap<>();
            GroupPopulationUserParams.put("templateBusinessId", cscpUserDTO.getId());
            GroupPopulationUserParams.put("templateBusinessName", cscpUserDTO.getRealName());
//        formParams.put("leaderCompanyName", cu.getCompanyName());
//        formParams.put("leaderCompanyId", cu.getCompanyId());
            GroupPopulationUserParams.put("post", cscpUserDTO.getPost());
            GroupPopulationUserParams.put("mobile", cscpUserDTO.getMobile());
            ResultVO<String> responseBO = RestTemplateRequestJWT.post(SpringUtil.getLocalUrlPort() + "/api/populationGroup/updateGroupPopulationUserInfo", GroupPopulationUserParams, ResultVO.class);
            if (!ResultCode.SUCCESS.code().equals(responseBO.getResultCode())) {
                throw new BusinessException(responseBO.getResultMsg());
            }
        } catch (Exception e) {
            //更新异常
        }

        //===================同步修改分发组数据不同步问题=============================

        // ===================同步用户到统一身份认证系统===================
        asyncPushUserToWestoneUas(cscpUser);
        // ===================同步用户到统一身份认证系统===================

        return cscpUserDTO;
    }

    private void updateLoginName(CscpUserDTO cscpUserDTO, AtomicInteger atomicInteger) {
        LambdaQueryWrapper<CscpUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpUser::getLoginName, cscpUserDTO.getLoginName());
        queryWrapper.eq(CscpUser::getDeleted, 0);
        List<CscpUser> cscpUsers = cscpUserRepository.selectListNoAdd(queryWrapper);
        if (!cscpUsers.isEmpty()) {
            // 修改的用户名表里已存在
            throw new BusinessException(ResultCode.USER_HAS_EXISTED);
            /*cscpUserDTO.setLoginName(cscpUserDTO.getLoginName() + "_" + atomicInteger.getAndIncrement());
            updateLoginName(cscpUserDTO, atomicInteger);*/
        }

    }

    @Override
    @Transactional
    public CscpUserDTO crmUpdate(CscpUserDTO cscpUserDTO, TSysTenant tSysTenant, Long orgId) {

        CscpUser sourceCscpUser = cscpUserRepository.selectById(cscpUserDTO.getId());
        // 先判断手机号码是否变更
        if (!cscpUserDTO.getMobile().equals(sourceCscpUser.getMobile())) {

            boolean mobilePhone = existByMobile(cscpUserDTO.getMobile());
            if (mobilePhone) {
                throw new BusinessException(ResultCode.USER_MOBILE_EXISTED);
            }
        }
        CscpUser cscpUser = new CscpUser();
        // RealNameSplitType realNameSplitType = StringSplitUtils.realNameSplit(cscpUserDTO.getRealName());
        // cscpUser.setRealNameStart(realNameSplitType.getRealNameStart());
        // cscpUser.setRealNameEnd(realNameSplitType.getRealNameEnd());
        cscpUser.setRealNameStart(cscpUserDTO.getRealName());
        cscpUser.setRealNameEnd(cscpUserDTO.getRealName());
        // 设置用户默认排序
        if (Objects.isNull(cscpUserDTO.getOrderBy())) {
            cscpUser.setOrderBy(9999);
        }
        // 修改用户表排序号,大于该排序的该租户下的所有用户表的排序号全部 + 1
        this.crmUpdateUserSort(cscpUserDTO, tSysTenant);
        BeanUtils.copyProperties(cscpUserDTO, cscpUser);

        //保存用户详细信息
        cscpUser.setMobileStart(cscpUserDTO.getMobile());
        cscpUser.setMobileMiddle(cscpUserDTO.getMobile());
        cscpUser.setMobileEnd(cscpUserDTO.getMobile());
        cscpUser.setMobile(cscpUserDTO.getMobile()); // 重新加密设值

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = sourceCscpUser.getLoginName() + sourceCscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }

        cscpUserRepository.updateById(cscpUser);

        // 保存用户角色
        cscpUserRoleService.crmSaveUserRoles(cscpUser.getId(), cscpUserDTO.getRoleIds(), tSysTenant, orgId);

        // 保存App角色用户关系
        if (CollectionUtils.isNotEmpty(cscpUserDTO.getAppRoleIds())) {
            // todo  暂时没找到解决方案，先临时只能通过普通用户
//            if (SecurityUtils.isGeneralName()) {
            itAppUserRoleService.insertRolesToUser(cscpUserDTO.getAppRoleIds(), cscpUser.getId());
//            }

        }

        // 修改组织机构排序，大于该排序的所有组织机构的排序号全部+1
//        cscpUserOrgService.updateUserOrgSort(cscpUserDTO);
//
//        cscpUserOrgService.crmSaveUserOrgRel(cscpUser.getId(), cscpUserDTO);
        BeanUtils.copyProperties(cscpUser, cscpUserDTO);


        //===================同步通讯录修改开始=============================
        if (Objects.isNull(cscpUserDTO.getAddType())) {
            CreateTAddressBookDTO build = CreateTAddressBookDTO.builder()
                    .realName(cscpUserDTO.getRealName())
                    .defaultPhone(cscpUserDTO.getMobile())
                    .sort(cscpUserDTO.getSort())
                    .AddType(1)
                    .telephone(cscpUserDTO.getOfficePhone())
                    .secretaryName(cscpUserDTO.getSecretaryName())
                    .secretaryPhone(cscpUserDTO.getSecretaryPhone())
                    .whetherShow(cscpUserDTO.getWhetherShow())
//                    .jobTitle(cscpUserDTO.getOrgNameList().get(0).getPost())
                    .userId(cscpUserDTO.getId()).build();
            if (cscpUserDTO.getOrgNameList() != null && cscpUserDTO.getOrgNameList().size() > 0) {
                build.setJobTitle(cscpUserDTO.getOrgNameList().get(0).getPost());
            }
            try {
                tAddressBookService.update(build, 2);
            } catch (Exception e) {
                log.error("修改通讯录异常", e);
            }
        }
        //===================同步通讯录修改结束=============================

        // ===================同步用户到统一身份认证系统===================
        asyncPushUserToWestoneUas(cscpUser);
        // ===================同步用户到统一身份认证系统===================

        return cscpUserDTO;
    }


    /**
     * Get all the cscpUsers.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public PageResult<CscpUserDTO> findAll() {
        log.debug("Request to get all CscpUsers");

//        List<CscpUserDTO> data = cscpUserRepository.selectByExample(null).stream()
//                .map(cscpUserRepository::toDto)
//                .collect(Collectors.toCollection(LinkedList::new));

        Long id = SecurityUtils.getCurrentUserId();
        List<CscpUser> cscpUserList = cscpUserRepository.selectAllCscpUserList(id);
        List<CscpUserDTO> data = ListCopyUtil.copy(cscpUserList, CscpUserDTO.class);

        long count = 0L;

        if (CollectionUtils.isNotEmpty(data)) {
            count = cscpUserRepository.selectCount(null);
        }

        return new PageResult<CscpUserDTO>(data, count, count);

    }

    /**
     * Get one cscpUser.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public CscpUserDTO findByUserId(Long id) {
        log.debug("Request to get CscpUser : {} ", id);
        CscpUserDTO userDetailDTO = new CscpUserDTO();
        CscpUser cscpUser = cscpUserRepository.selectById(id);
        if (cscpUser != null) {
            if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
                throw new BusinessException("该用户已经被禁用");
            }
            BeanUtils.copyProperties(cscpUser, userDetailDTO);
            //用户的角色信息
            List<CscpRoles> roleList = cscpUserRoleRepository.queryRoleByUserId(cscpUser.getId());
            if (roleList != null && roleList.size() > 0) {
                List<Long> roleIds = new ArrayList<>();
                List<CscpRoleNameIdsDTO> roleNames = new ArrayList<>();
                for (CscpRoles cscpRoles : roleList) {
                    roleIds.add(cscpRoles.getId());
                    CscpRoleNameIdsDTO cscpOrgNameIdListDTO = new CscpRoleNameIdsDTO();
                    cscpOrgNameIdListDTO.setId(cscpRoles.getId());
                    cscpOrgNameIdListDTO.setName(cscpRoles.getName());
                    roleNames.add(cscpOrgNameIdListDTO);//admin,system
                }
                userDetailDTO.setRoleIds(roleIds);
                userDetailDTO.setRoleNames(roleNames);
            }

            // 用户App角色信息
            List<TAppRoleDTO> tAppRoleDTOList = itAppUserRoleService.queryTAppRolesByUserId(cscpUser.getId());
            if (CollectionUtils.isNotEmpty(tAppRoleDTOList)) {
                List<Long> appRoleIdList = new ArrayList<>();
                List<CscpRoleNameIdsDTO> appRoleNames = new ArrayList<>();
                for (TAppRoleDTO tAppRoleDTO : tAppRoleDTOList) {
                    appRoleIdList.add(tAppRoleDTO.getId());
                    CscpRoleNameIdsDTO cscpOrgNameIdListDTO = new CscpRoleNameIdsDTO();
                    cscpOrgNameIdListDTO.setId(tAppRoleDTO.getId());
                    cscpOrgNameIdListDTO.setName(tAppRoleDTO.getName());
                    appRoleNames.add(cscpOrgNameIdListDTO);
                }
                userDetailDTO.setAppRoleIds(appRoleIdList);
                userDetailDTO.setAppRoleNames(appRoleNames);
            }
            List<TDeptManagementAuthorityDTO> tDeptManagementAuthorityDTOList = tDeptManagementAuthorityService.findManageDeptByUserId(id);
            userDetailDTO.setOrgManagementList(tDeptManagementAuthorityDTOList);
            if (StringUtils.isNotEmpty(cscpUser.getPersonLabel())) {
                //回显人员标签
                userDetailDTO.setPersonLabelList(Arrays.asList(cscpUser.getPersonLabel().split(",")));
            }

            List<CscpOrgDTO> orgsList = cscpUserOrgService.queryOrgByUserId(cscpUser.getId());
            List<Long> orgIds = new ArrayList<>();
            List<CscpOrgNameIdListDTO> orgNames = new ArrayList<>();
            orgsList.forEach(cscpOrgDTO -> {
                orgIds.add(cscpOrgDTO.getId());
                CscpOrgNameIdListDTO cscpOrgNameIdListDTO = new CscpOrgNameIdListDTO();
                cscpOrgNameIdListDTO.setId(cscpOrgDTO.getId());
                cscpOrgNameIdListDTO.setTitle(cscpOrgDTO.getOrgName());
                // 设置用户组织机构中间表排序号
                cscpOrgNameIdListDTO.setUserOrgSort(cscpOrgDTO.getUserOrgSort());
                // 设置用户组织机构中间表职务
                cscpOrgNameIdListDTO.setPost(cscpOrgDTO.getPost());
                cscpOrgNameIdListDTO.setRank(cscpOrgDTO.getRank());
                cscpOrgNameIdListDTO.setDepartmentHead(cscpOrgDTO.getDepartmentHead());
                cscpOrgNameIdListDTO.setType(cscpOrgDTO.getType());
                orgNames.add(cscpOrgNameIdListDTO);
                if (cscpOrgDTO.getDefaultDepartment() != null && cscpOrgDTO.getDefaultDepartment() == 1) {
                    userDetailDTO.setDefaultDepart(cscpOrgDTO.getId());
                }
            });
            userDetailDTO.setOrgIdList(orgIds);
            userDetailDTO.setOrgNameList(orgNames);
            userDetailDTO.setDepartmentName(SecurityUtils.getCurrentCscpUserDetail().getDepartmentName());
            userDetailDTO.setCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
            userDetailDTO.setUserType(UserType.getUserType(SecurityUtils.getCurrentCscpUserDetail().getUserType()));

            try {
                LambdaQueryWrapper<TAddressBook> lambdaQueryWrapper = new LambdaQueryWrapper<TAddressBook>();
                lambdaQueryWrapper.eq(TAddressBook::getUserId, id);
                TAddressBook tAddressBook = tAddressBookService.selectOneNoAdd(lambdaQueryWrapper);
                if (tAddressBook != null) {
                    userDetailDTO.setWhetherShow(tAddressBook.getWhetherShow());
                    userDetailDTO.setSecretaryName(tAddressBook.getSecretaryName());
                    userDetailDTO.setSecretaryPhone(tAddressBook.getSecretaryPhone());
                }
            } catch (Exception e) {
                log.error("查询通讯录失败", e);
            }
        }

        return userDetailDTO;
    }


    /**
     * 根据用户ID和部门ID删除用户
     *
     * @param id    the id of the entity
     * @param orgId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id, Long orgId) {
        if (id.equals(SecurityUtils.getCurrentUserId())) {
            throw new BusinessException("不能删除自己");
        }
        log.debug("CscpUserServiceImpl.delete userId = {}, orgId = {} ", id, orgId);

        // 查询当前用户的组织机构信息
        List<CscpUserOrgDTO> userOrgDTOList = cscpUserOrgService.qryUserOrgByUserId(id);

        // 如果当前用户具有多个组织机构，则只删除其用户组织机构中间表的信息
        if (CollectionUtils.isNotEmpty(userOrgDTOList) && userOrgDTOList.size() > 1) {
            LambdaQueryWrapper<CscpUserOrg> queryWrapperUserOrg = new LambdaQueryWrapper();
            queryWrapperUserOrg.eq(CscpUserOrg::getUserId, id);
            queryWrapperUserOrg.eq(CscpUserOrg::getOrgId, orgId);
            cscpUserOrgRepository.delete(queryWrapperUserOrg);
            return;
        }

        CscpUser cscpUser = cscpUserRepository.selectById(id);
        // 2025-03-25 禁用用户将手机号默认为***********;原因手机号存储至办公电话
        cscpUser.setOfficePhone(cscpUser.getMobile());
        cscpUser.setMobileStart(SysConstant.DELAULT_MOBILE);
        cscpUser.setMobileMiddle(SysConstant.DELAULT_MOBILE);
        cscpUser.setMobileEnd(SysConstant.DELAULT_MOBILE);
        cscpUser.setMobile(SysConstant.DELAULT_MOBILE);
        cscpUser.setStatus(UserConstant.USER_FORBIDDEN_STATUS);
        cscpUserRepository.updateById(cscpUser);
        //记录删除用户操作
        CscpUserDeleteRecord cscpUserDeleteRecord = new CscpUserDeleteRecord();
        BeanUtils.copyProperties(cscpUser, cscpUserDeleteRecord);
        cscpUserDeleteRecord.setUserId(id);
        cscpUserDeleteRecord.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        cscpUserDeleteRecord.setDeleteBy(SecurityUtils.getCurrentUserId());
        cscpUserDeleteRecord.setDeleteName(SecurityUtils.getCurrentUserName());
        cscpUserDeleteRecord.setDeleteTime(LocalDateTime.now());
        cscpUserDeleteRecordService.save(cscpUserDeleteRecord);
        // ===================同步用户到统一身份认证系统===================
        asyncPushUserToWestoneUas(cscpUser);
        // ===================同步用户到统一身份认证系统===================

        // TODO 原有删除逻辑，现在改禁用下面逻辑默认不走
        // 保留之前删除的逻辑，不做逻辑删除的前提下。不删除关联关系
        CscpUser oldCscpUser = cscpUserRepository.selectById(id);
        if (null == oldCscpUser) {
            //删除工作组、角色、组织机构中的用户,用户详细信息
            LambdaQueryWrapper<CscpUserWorkGroup> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpUserWorkGroup::getUserId, id);
            cscpUserWorkGroupRepository.delete(queryWrapper);

            LambdaQueryWrapper<CscpUserOrg> queryWrapperUserOrg = new LambdaQueryWrapper();
            queryWrapperUserOrg.eq(CscpUserOrg::getUserId, id);
            cscpUserOrgRepository.delete(queryWrapperUserOrg);

            LambdaQueryWrapper<CscpUserRole> queryWrapperCscpUserRole = new LambdaQueryWrapper();
            queryWrapperCscpUserRole.eq(CscpUserRole::getUserId, id);
            cscpUserRoleRepository.delete(queryWrapperCscpUserRole);
        }
    }

    /**
     * 用户禁用-仅变更用户status，不删除org和user之间的关联
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> disable(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("参数错误");
        }
        List<Long> result = new ArrayList<>();
        for (Long id : ids) {
            try {
                // 查询用户
                CscpUser cscpUser = cscpUserRepository.selectById(id);
                // 修改用户手机号
                cscpUser.setOfficePhone(cscpUser.getMobile());
                cscpUser.setMobileStart(SysConstant.DELAULT_MOBILE);
                cscpUser.setMobileMiddle(SysConstant.DELAULT_MOBILE);
                cscpUser.setMobileEnd(SysConstant.DELAULT_MOBILE);
                cscpUser.setMobile(SysConstant.DELAULT_MOBILE);
                // 修改用户身份证
                cscpUser.setIdCardNoBackup(cscpUser.getIdCardNo());
                cscpUser.setIdCardNo(SysConstant.DELAULT_ID_CARD_NO);

                // 修改用户状态
                cscpUser.setStatus(UserConstant.USER_FORBIDDEN_STATUS);
                cscpUser.setMobile(null);
                cscpUser.setMobileStart(null);
                cscpUser.setMobileMiddle(null);
                cscpUser.setMobileEnd(null);
                cscpUser.setWestoneUserId(null);
                // 更新用户
                cscpUserRepository.updateById(cscpUser);
                // 更新通讯录
                tAddressBookService.updateUserStatus(ListUtil.of(id), UserConstant.USER_FORBIDDEN_STATUS);
                // 同步到认证系统
                asyncPushUserToWestoneUas(cscpUser);
                //记录删除用户操作
                CscpUserDeleteRecord cscpUserDeleteRecord = new CscpUserDeleteRecord();
                BeanUtils.copyProperties(cscpUser, cscpUserDeleteRecord);
                cscpUserDeleteRecord.setUserId(id);
                cscpUserDeleteRecord.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                cscpUserDeleteRecord.setDeleteBy(SecurityUtils.getCurrentUserId());
                cscpUserDeleteRecord.setDeleteName(SecurityUtils.getCurrentUserName());
                cscpUserDeleteRecord.setDeleteTime(LocalDateTime.now());
                cscpUserDeleteRecordService.save(cscpUserDeleteRecord);
                result.add(id);
            } catch (Exception e) {
                log.error("禁用用户失败, id={}", id, e);
                throw new BusinessException("禁用用户过程中发生异常", e);
            }
        }

        return result;
    }

    @Override
    public void judgeUserStatus(List<Long> ids) {

        String value = sysConfigService.getSysConfigValueByCode("delete:ignore.other.library");
        if (StringUtils.isNotEmpty(value) && "true".equals(value)) {
            return;
        }

        SecurityContext securityContext = SecurityContextHolder.getContext();
        final int batchSize = 20;
        List<String> allResults = new ArrayList<>();

        // 分批处理ID列表
        for (int i = 0; i < ids.size(); i += batchSize) {
            List<Long> batchIds = ids.subList(i, Math.min(i + batchSize, ids.size()));

            // 对当前批次进行异步处理
            List<CompletableFuture<String>> futures = batchIds.stream()
                    .map(id -> CompletableFuture.supplyAsync(() -> {
                        SecurityContextHolder.setContext(securityContext);
                        return dsOptContext.selectUserLoginFlag(id);
                    })).collect(Collectors.toList());

            // 等待当前批次的所有任务完成并收集结果
            List<String> batchResults = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

            allResults.addAll(batchResults);
        }

        // 检查所有批次的结果
        if (CollectionUtils.isNotEmpty(allResults)) {
            throw new BusinessException(StringUtils.join(allResults, ";"));
        }
    }

    @Override
    public CscpUserDTO selectUserById(Long userId) {
        return cscpUserRepository.selectUserById(userId);
    }

    @Override
    public void updateAppCodesById(Long userId, String appCodes) {
        cscpUserRepository.updateAppCodesById(userId, appCodes);
    }

    @Override
    public List<CscpUserDTO> selectPullCscpUser(List<TYJGSyncUserOrgIdDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        try {
            // 批量查询用户信息
            List<Long> userIds = list.stream()
                    .map(TYJGSyncUserOrgIdDTO::getUserId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(userIds)) {
                return new ArrayList<>();
            }

            // 批量查询用户基本信息
            List<CscpUser> userList = cscpUserRepository.selectBatchIds(userIds);
            if (CollectionUtils.isEmpty(userList)) {
                return new ArrayList<>();
            }

            // 转换为DTO
            List<CscpUserDTO> userDTOList = (List<CscpUserDTO>) ListCopyUtil.copy(userList, CscpUserDTO.class);

            // 提取所有用户ID用于后续查询
            List<Long> allUserIds = userDTOList.stream()
                    .map(CscpUserDTO::getId)
                    .collect(Collectors.toList());

            // 批量查询用户机构信息
            Map<Long, List<CscpUserOrg>> userOrgMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allUserIds)) {
                LambdaQueryWrapper<CscpUserOrg> orgQueryWrapper = new LambdaQueryWrapper<>();
                orgQueryWrapper.in(CscpUserOrg::getUserId, allUserIds);
                List<CscpUserOrg> allUserOrgs = cscpUserOrgService.selectListNoAdd(orgQueryWrapper);

                // 按用户ID分组
                userOrgMap = allUserOrgs.stream()
                        .collect(Collectors.groupingBy(CscpUserOrg::getUserId));
            }

            // 批量查询单位信息（用于获取信用代码）
            Set<Long> companyIds = userOrgMap.values().stream()
                    .flatMap(List::stream)
                    .map(CscpUserOrg::getCompanyId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Long, String> companyCreditCodeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(companyIds)) {
                LambdaQueryWrapper<CscpOrg> orgQueryWrapper = new LambdaQueryWrapper<>();
                orgQueryWrapper.select(CscpOrg::getId, CscpOrg::getCreditCode)
                        .in(CscpOrg::getId, companyIds);
                List<CscpOrg> companyList = cscpOrgRepository.selectListNoAdd(orgQueryWrapper);

                companyCreditCodeMap = companyList.stream().filter(org -> org.getCreditCode() != null)
                        .collect(Collectors.toMap(CscpOrg::getId, CscpOrg::getCreditCode, (k1, k2) -> k1));
            }

            // 批量查询用户角色信息
            Map<Long, List<CscpRoles>> userRolesMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allUserIds)) {
                List<CscpRoles> allRoles = cscpUserRoleRepository.queryRoleByUserIds(allUserIds);
                userRolesMap = allRoles.stream()
                        .collect(Collectors.groupingBy(CscpRoles::getUserId));
            }

            // 使用CompletableFuture并行处理每个用户的数据填充
            Map<Long, List<CscpUserOrg>> finalUserOrgMap = userOrgMap;
            Map<Long, String> finalCompanyCreditCodeMap = companyCreditCodeMap;
            Map<Long, List<CscpRoles>> finalUserRolesMap = userRolesMap;
            List<CompletableFuture<CscpUserDTO>> futures = userDTOList.stream()
                    .map(userDTO -> CompletableFuture.supplyAsync(() -> {
                        try {
                            Long userId = userDTO.getId();

                            // 设置机构信息
                            List<CscpUserOrg> userOrgs = finalUserOrgMap.getOrDefault(userId, new ArrayList<>());
                            userDTO.setOrgList(userOrgs);

                            // 设置用户所在单位统一社会信用代码
                            if (CollectionUtils.isNotEmpty(userOrgs)) {
                                Map<Long, String> orgCreditCodeMap = new HashMap<>();
                                userOrgs.stream()
                                        .map(CscpUserOrg::getCompanyId)
                                        .filter(Objects::nonNull)
                                        .distinct()
                                        .forEach(companyId -> {
                                            String creditCode = finalCompanyCreditCodeMap.get(companyId);
                                            if (creditCode != null) {
                                                orgCreditCodeMap.put(companyId, creditCode);
                                            }
                                        });
                                userDTO.setCreditCodeMap(orgCreditCodeMap);
                            } else {
                                userDTO.setCreditCodeMap(new HashMap<>());
                            }

                            // 是否推送密码
                            String pushConfig = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PUSH_DEFAULT_PASSWORD);
                            if (StrUtil.isNotBlank(pushConfig) && "0".equals(pushConfig)) {
                                userDTO.setPassword(null);
                            }

                            // 角色信息填充
                            List<CscpRoles> roles = finalUserRolesMap.getOrDefault(userId, new ArrayList<>());
                            if (CollectionUtils.isNotEmpty(roles)) {
                                String roleIds = roles.stream()
                                        .map(CscpRoles::getId)
                                        .map(String::valueOf)
                                        .collect(Collectors.joining(","));
                                userDTO.setRoleIdsStr(roleIds);
                                if (roleIds.contains(SystemRole.COMPANY_ROLE.getId())) {
                                    userDTO.setUnitAdminFlag("1");
                                } else {
                                    userDTO.setUnitAdminFlag("0");
                                }
                            }
                            // 身份证
                            userDTO.setStrIdCardNo(userDTO.getIdCardNo());
                            return userDTO;
                        } catch (Exception e) {
                            log.error("填充用户详细信息失败, userId: {}", userDTO.getId(), e);
                            return userDTO; // 即使出错也返回原始数据
                        }
                    }, asyncServiceExecutor))
                    .collect(Collectors.toList());

            // 设置超时时间，避免无限等待
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // 等待所有任务完成，设置超时时间
            try {
                allFutures.get(100, TimeUnit.SECONDS); // 100秒超时
            } catch (TimeoutException e) {
                log.error("用户数据处理超时，数据量: {}", list.size());
                throw new BusinessException(ResultCode.PULL_TIMEOUT, e.getMessage());
            } catch (Exception e) {
                log.error("用户数据处理异常", e);
                throw new BusinessException(ResultCode.PULL_SYSTEM_ERROR, e.getMessage());
            }

            // 收集结果
            return futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("批量查询用户数据时发生错误，数据量: {}", list.size(), e);
            throw new BusinessException(ResultCode.PULL_SYSTEM_ERROR, e.getMessage());
        }
    }


    public void updateStatus(List<Long> ids,Integer status){
        tAddressBookService.updateUserStatus(ids,status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> enable(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("参数错误");
        }
        SecurityContext securityContext = SecurityContextHolder.getContext();
        List<CompletableFuture<Long>> futures = ids.stream()
                .map(id -> CompletableFuture.supplyAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    try {
                        // 将用户的status改为0
                        CscpUser cscpUser = cscpUserRepository.selectById(id);
                        cscpUser.setStatus(UserConstant.USER_ACTIVE_STATUS);
                        cscpUserRepository.updateById(cscpUser);
                        // 更新通讯录
                        tAddressBookService.updateUserStatus(ListUtil.of(id), UserConstant.USER_ACTIVE_STATUS);
                        // 同步用户到统一身份认证系统
                        asyncPushUserToWestoneUas(cscpUser);
                        return id;
                    } catch (Exception e) {
                        // 记录异常并继续处理其他任务
                        log.error("启用用户失败, id={}", id, e);
                        throw new BusinessException("用户启用失败", e);
                    }
                }))
                .collect(Collectors.toList());
        // 等待所有任务完成并收集结果
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    @Override
    public String getMobilePhoneById(Long id) {
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpUser::getId, id).select(CscpUser::getMobile);
        CscpUser cscpUser = cscpUserRepository.selectOneNoAdd(lambdaQueryWrapper);
        if (cscpUser != null) {
            return cscpUser.getMobile();
        } else {
            return "";
        }
    }

    private String getPageOrderBy(Pageable page) {

        if (page != null && page.getSort() != null) {

            StringBuilder sb = new StringBuilder();

            page.getSort().forEach(sort -> sb.append(sort.getProperty())
                    .append(" ").append(sort.getDirection()).append(","));

            if (sb.length() > 1) {
                return (sb.substring(0, sb.length() - 1));
            }
        }

        return null;
    }

    /**
     * Get the cscpUsers.
     *
     * @return the list of entities
     */
    @Override
    public PageResult<CscpUserDTO> findByCscpUserDTO(CscpUserDTO cscpUserDTO, BasePageForm basePageForm) {
        return findByCscpUserDTOAndDesensitized(cscpUserDTO, basePageForm, false);
    }


    public PageResult<CscpUserDTO> findByCscpUserDTOAndDesensitized(CscpUserDTO cscpUserDTO, BasePageForm basePageForm, boolean isDesensitized) {
        if (SecurityUtils.isGeneralName()) {
            cscpUserDTO.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        }
        if (SecurityUtils.isTenantName()) {
            cscpUserDTO.setTenantId(SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        }
        cscpUserDTO.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(cscpUserDTO.getRealName()) : this.division(cscpUserDTO.getRealName()));
        cscpUserDTO.setMobile(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(cscpUserDTO.getMobile()) : this.division(cscpUserDTO.getMobile()));

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        if (currentCompanyId != null) {
            CscpOrg byId = cscpOrgService.getById(currentCompanyId);
            LambdaQueryWrapper<CscpOrg> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotEmpty(byId.getOrgCodePath())) {
                orgLambdaQueryWrapper.like(CscpOrg::getOrgCodePath, byId.getOrgCodePath());
            } else {
                orgLambdaQueryWrapper.like(CscpOrg::getOrgCode, byId.getOrgCode());
            }
            List<CscpOrg> cscpOrgs = cscpOrgService.selectListNoAdd(orgLambdaQueryWrapper);
            List<Long> idList = cscpOrgs.stream().map(CscpOrg::getId).collect(Collectors.toList());
            cscpUserDTO.setCompanyIdList(idList);
        }

        IPage<CscpUserDTO> cscpUserIPage = criteriaQueryCscpUserListNoAdmin(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), cscpUserDTO);

        List<CompletableFuture<CscpUserDTO>> futures = cscpUserIPage.getRecords().stream()
                .map(x -> CompletableFuture.supplyAsync(() -> {
                    x.setExamineStatusName(UserExamineStatusEnum.obtainType(x.getExamineStatus()));

//                    if (Objects.nonNull(x.getCompanyId())) {
//                        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(x.getCompanyId());
//                        List<CscpUserOrg> cscpUserOrg = cscpUserOrgRepository.selectListNoAdd(
//                                new QueryWrapper<CscpUserOrg>().select("DISTINCT org_id", "post").lambda().eq(CscpUserOrg::getUserId, x.getId()).eq(CscpUserOrg::getDefaultDepartment, 1));
//
//                        if (cscpUserOrg.size() > 0) {
//                            for (CscpUserOrg userOrg : cscpUserOrg) {
//                                if (Objects.nonNull(userOrg)) {
//                                    x.setCompanyName(cscpOrgDTO.getOrgName());
//                                }
//                                if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(userOrg.getPost())) {
//                                    x.setPost(userOrg.getPost());
//                                }
//                            }
//                        }
//                    }

                    if (westoneEncryptService.isCipherMachine()) {
                        // TODO 数据完整性校验
                        String hmacMobile = x.getHmacMobile();
                        String src = x.getLoginName() + x.getPassword() + x.getMobile();
                        boolean isIntegrity = westoneEncryptService.compareSM3HMAC(src, hmacMobile);
                        if (isIntegrity) {
                            x.setDataIsIntegrity(0);
                        } else {
                            x.setDataIsIntegrity(1);
                        }
                    }
                    if (isDesensitized) {
                        x.setRealName(DesensitizeUtil.desensitizedName(x.getRealName()));
                        x.setMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getMobile()));
                    }

                    return x;
                })).collect(Collectors.toList());

        List<CscpUserDTO> cscpUserDTOList = futures.stream().map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());

        return new PageResult<>(cscpUserDTOList, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }

    @Override
    public PageResult<CscpUserDTO> findByCscpUserDTOAndDesensitized(CscpUserDTO cscpUserDTO, BasePageForm basePageForm) {
        return findByCscpUserDTOAndDesensitized(cscpUserDTO, basePageForm, true);

    }

    @Override
    public PageResult<CscpUserDTO> findByOrgId(CscpUserDTO cscpUserDTO, BasePageForm page) {
        if (SecurityUtils.isGeneralName()) {
            cscpUserDTO.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        }
        if (SecurityUtils.isTenantName()) {
            cscpUserDTO.setTenantId(SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        }

        cscpUserDTO.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(cscpUserDTO.getRealName()) : this.division(cscpUserDTO.getRealName()));
        cscpUserDTO.setMobile(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(cscpUserDTO.getMobile()) : this.division(cscpUserDTO.getMobile()));

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        if (currentCompanyId != null) {
            CscpOrg byId = cscpOrgService.getById(currentCompanyId);
            LambdaQueryWrapper<CscpOrg> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotEmpty(byId.getOrgCodePath())) {
                orgLambdaQueryWrapper.like(CscpOrg::getOrgCodePath, byId.getOrgCodePath());
            } else {
                orgLambdaQueryWrapper.like(CscpOrg::getOrgCode, byId.getOrgCode());
            }
            List<CscpOrg> cscpOrgs = cscpOrgService.selectListNoAdd(orgLambdaQueryWrapper);
            List<Long> idList = cscpOrgs.stream().map(CscpOrg::getId).collect(Collectors.toList());
            cscpUserDTO.setCompanyIdList(idList);
        }
        // 查询是否部门管理员，设置管理部门
        List<Long> deptIDList = null;
        if (null != cscpUserDTO.getUserType().name() && StringUtils.equals(UserType.DEPT_USER.name(), cscpUserDTO.getUserType().name())) {
            List<TDeptManagementAuthorityDTO> tDeptManagementAuthorityDTOList = tDeptManagementAuthorityService.findManageDeptByUserId(SecurityUtils.getCurrentUserId());
            deptIDList = tDeptManagementAuthorityDTOList.stream().map(i -> i.getDeptId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deptIDList)) {
                return new PageResult<>(new ArrayList<>(), 0, 0);
            }
        }
        // 普通用户不能查看
        if (SecurityUtils.isNormalName()) {
            if (CollectionUtils.isEmpty(deptIDList)) {
                return new PageResult<>(new ArrayList<>(), 0, 0);
            }
        }

        IPage<CscpUserDTO> cscpUserIPage = null;
        // 转义特殊字符
        String escapedName = patternQuote(cscpUserDTO.getRealName());
        cscpUserDTO.setRealName(escapedName);
        if (cscpUserDTO.getCompanyId() == null) {
            cscpUserIPage = cscpUserRepository.criteriaQueryCscpUserListNoAdmin(
                    PageHelperUtil.getMPlusPageByBasePage(page), cscpUserDTO);
        } else {
            cscpUserIPage = cscpUserRepository.queryCscpUserList(
                    PageHelperUtil.getMPlusPageByBasePage(page), cscpUserDTO, deptIDList);
        }
        List<CompletableFuture<CscpUserDTO>> futures = cscpUserIPage.getRecords().stream()
                .map(x -> CompletableFuture.supplyAsync(() -> {
                    x.setExamineStatusName(UserExamineStatusEnum.obtainType(x.getExamineStatus()));

//                    if (Objects.nonNull(x.getCompanyId())) {
//                        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(x.getCompanyId());
//                        List<CscpUserOrg> cscpUserOrg = cscpUserOrgRepository.selectListNoAdd(
//                                new QueryWrapper<CscpUserOrg>().select(" org_id", "post").lambda().eq(CscpUserOrg::getUserId, x.getId()).eq(CscpUserOrg::getDefaultDepartment, 1));
//
//                        if (cscpUserOrg.size() > 0) {
//                            for (CscpUserOrg userOrg : cscpUserOrg) {
//                                if (Objects.nonNull(userOrg)) {
//                                    x.setCompanyName(cscpOrgDTO.getOrgName());
//                                }
//                                if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(userOrg.getPost())) {
//                                    x.setPost(userOrg.getPost());
//                                }
//                            }
//                        }
//                    }
                    x.setRealName(DesensitizeUtil.desensitizedName(x.getRealName()));
                    x.setMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getMobile()));


                    return x;
                })).collect(Collectors.toList());

        List<CscpUserDTO> cscpUserDTOList = futures.stream().map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());

        return new PageResult<>(cscpUserDTOList, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }

    /**
     * 管理员用户查询优化
     * 查询条件含有部门信息 -> 1、根据部门条件查询部门表和用户部门关系表的到userIds 2、再分页查询用户表
     * 查询条件不含有部门信息 -> 1、分页查询用户表 2、再获取用户的部门信息
     *
     * @param page
     * @param cscpUserDTO
     * @return
     */
    private IPage<CscpUserDTO> criteriaQueryCscpUserListNoAdmin(IPage page, CscpUserDTO cscpUserDTO) {

        if (cscpUserDTO.conditionDepFlag()) {
            return queryByDepartment(page, cscpUserDTO);
        } else {
            return queryWithoutDepartment(page, cscpUserDTO);
        }
    }

    /**
     * 根据部门查询用户信息
     * 当通过部门查询用户时，此方法首先获取与部门相关的用户其他信息，然后与基础用户信息合并后返回
     *
     * @param page 分页对象，用于分页查询
     * @param cscpUserDTO 用户DTO对象，包含查询条件
     * @return 返回包含用户信息的分页对象
     */
    private IPage<CscpUserDTO> queryByDepartment(IPage page, CscpUserDTO cscpUserDTO) {
        // 查询与部门相关的用户其他信息
        List<CscpUserDTO> cscpUserOtherInfo = cscpUserRepository.otherQueryCscpUserListNoAdmin(cscpUserDTO);
        if (CollectionUtils.isEmpty(cscpUserOtherInfo)) {
            return new Page<>();
        }

        // 将用户其他信息转换为Map，便于后续合并信息
        Map<Long, List<CscpUserDTO>> cscpUserOtherMap = cscpUserOtherInfo.stream().collect(Collectors.groupingBy(CscpUserDTO::getId));

        cscpUserDTO.setUserIds(new ArrayList<>(cscpUserOtherMap.keySet()));

        // 查询基础用户信息

        String sysConfigValueByCode = sysConfigService.getSysConfigValueByCode("in.or.out.net.flag");
        String contextIndex = "no";
        if (StringUtils.isNotBlank(sysConfigValueByCode) && "out".equals(sysConfigValueByCode)) {
            //是否使用全文索引
            contextIndex = "yes";
        }
        IPage<CscpUserDTO> pa = cscpUserRepository.baseQueryCscpUserListNoAdmin(page, cscpUserDTO, contextIndex);
        if (CollectionUtils.isNotEmpty(pa.getRecords())) {
            // 合并用户详细信息
            mergeUserDetails(pa.getRecords(), cscpUserOtherMap);
        }
        return pa;
    }

    /**
     * 不根据部门查询用户信息
     * 此方法直接查询基础用户信息，然后补充查询用户的其他信息并合并后返回
     *
     * @param page 分页对象，用于分页查询
     * @param cscpUserDTO 用户DTO对象，包含查询条件
     * @return 返回包含用户信息的分页对象
     */
    private IPage<CscpUserDTO> queryWithoutDepartment(IPage page, CscpUserDTO cscpUserDTO) {
        String sysConfigValueByCode = sysConfigService.getSysConfigValueByCode("in.or.out.net.flag");
        String contextIndex = "no";
        if (StringUtils.isNotBlank(sysConfigValueByCode) && "out".equals(sysConfigValueByCode)) {
            //是否使用全文索引
            contextIndex = "yes";
        }
        IPage<CscpUserDTO> pa = cscpUserRepository.baseQueryCscpUserListNoAdmin(page, cscpUserDTO, contextIndex);
        if (CollectionUtils.isEmpty(pa.getRecords())) {
            return pa;
        }

        // 收集用户ID列表，用于查询用户其他信息
        List<Long> userIdList = pa.getRecords().stream()
                .map(CscpUserDTO::getId)
                .collect(Collectors.toList());
        cscpUserDTO.setUserIds(userIdList);
        // 查询用户其他信息
        List<CscpUserDTO> cscpUserOtherInfo = cscpUserRepository.otherQueryCscpUserListNoAdmin(cscpUserDTO);
        if (CollectionUtils.isNotEmpty(cscpUserOtherInfo)) {
            Map<Long, List<CscpUserDTO>> cscpUserOtherMap = cscpUserOtherInfo.stream().collect(Collectors.groupingBy(CscpUserDTO::getId));
            mergeUserDetails(pa.getRecords(), cscpUserOtherMap);
        }
        return pa;
    }

    /**
     * 合并用户详细信息
     * 此方法将从其他来源获取的用户信息合并到基础用户信息中，以提供更全面的用户数据
     *
     * @param records 基础用户信息列表
     * @param cscpUserOtherMap 包含用户其他信息的Map
     */
    private void mergeUserDetails(List<CscpUserDTO> records, Map<Long, List<CscpUserDTO>> cscpUserOtherMap) {
        records.forEach(x -> {
            List<CscpUserDTO> cscpUserOrgs = cscpUserOtherMap.get(x.getId());
            if (CollectionUtils.isNotEmpty(cscpUserOrgs)) {
                x.setOrgSort(cscpUserOrgs.get(0).getOrgSort());
                x.setSort(cscpUserOrgs.get(0).getSort());
                x.setDepartmentId(cscpUserOrgs.get(0).getDepartmentId());
                x.setDepartmentCode(cscpUserOrgs.get(0).getDepartmentCode());
                x.setDepartmentName(StringUtils.join(cscpUserOrgs.stream().map(CscpUserDTO::getDepartmentName).collect(Collectors.toSet()), ","));
                x.setCompanyId(cscpUserOrgs.get(0).getCompanyId());
                x.setCompanyIdList(cscpUserOrgs.stream().map(CscpUserDTO::getCompanyId).collect(Collectors.toList()));
                x.setCompanyName(StringUtils.join(cscpUserOrgs.stream().map(CscpUserDTO::getCompanyName).collect(Collectors.toSet()), ","));
                x.setPost(cscpUserOrgs.get(0).getPost());
            }
        });
    }

    /**
     * 获取对象中为空的属性名数组
     * 此方法用于辅助属性复制，仅复制非空值，避免覆盖现有值
     *
     * @param source 源对象，用于检查空属性
     * @return 返回空属性名数组
     */
    public static String[] getNullPropertyNames(Object source) {
        if (source == null) {
            return new String[0];
        }
        synchronized (source.getClass()) {
            BeanWrapper srcWrapper = new BeanWrapperImpl(source);
            PropertyDescriptor[] pds = srcWrapper.getPropertyDescriptors();
            Set<String> nullFields = new HashSet<>();
            for (PropertyDescriptor pd : pds) {
                Object val = srcWrapper.getPropertyValue(pd.getName());
                if (val == null) {
                    nullFields.add(pd.getName());
                }
            }
            return nullFields.toArray(new String[0]);
        }
    }


    private String patternQuote(String realName) {

        if (realName == null) {
            return realName;
        }

        // 替换逻辑：将+替换为\+
        return realName.replace("+", "\\+");
    }

    /**
     * 重置所有用户（系统管理员除外）密码
     *
     * @param cscpUserPasswordUpdate
     * @return
     * @throws Exception
     */
    @Override
    public boolean resetAllPassword(CscpUserPasswordUpdate cscpUserPasswordUpdate) throws Exception {
        Base64Encrypt base64Encrypt = new Base64Encrypt();
        //校验密码复杂度
        String newPassword = decryptPassword(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword()));
        CheckResult checkResult = new CheckResult();

        lengthCheck.check(newPassword, checkResult);
        lowerCaseCheck.check(newPassword, checkResult);
        upperCaseCheck.check(newPassword, checkResult);
        containDigitCheck.check(newPassword, checkResult);
        specialCharCheck.check(newPassword, checkResult);
//        keyBoardCheckNew.check(newPassword, checkResult);

        StringBuffer stringBuffer = new StringBuffer();
        if (checkResult.getErrorReason().size() > 0) {
            for (int i = 0; i < checkResult.getErrorReason().size(); i++) {
                if (stringBuffer.length() > 0) {
                    stringBuffer.append(",");
                }
                stringBuffer.append(checkResult.getErrorReason().get(i));
            }
        }
        if (checkResult.getConfirmCount() < 4) {
            if (stringBuffer.length() > 0) {
                stringBuffer.append(",");
            }
            stringBuffer.append("密码应再包含");
            for (int i = 0; i < checkResult.getConfirmInfo().size(); i++) {
                stringBuffer.append(checkResult.getConfirmInfo().get(i));
                if (i < checkResult.getConfirmInfo().size() - 1) {
                    stringBuffer.append("、");
                }
            }
            stringBuffer.append("等至少");
            stringBuffer.append(4 - checkResult.getConfirmCount());
            stringBuffer.append("项");
        }
        if (stringBuffer.length() > 0) {
//            throw new Exception(stringBuffer.toString());
            log.error(stringBuffer.toString());
            return false;
        }

        // 开始修改密码
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.ne(CscpUser::getLoginName, "admin");
        List<CscpUser> users = this.selectListNoAdd(lambdaQueryWrapper);
        users.stream().forEach(x -> {
            CscpUser cscpUser = new CscpUser();
            cscpUser.setId(x.getId());
            cscpUser.setPassword(passwordEncoder.encode(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword())));

            if (westoneEncryptService.isCipherMachine()) {
                // TODO 计算SM3HMAC
                String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
                cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
            }

            this.updateById(cscpUser);
            this.clearUserCache(x.getLoginName());
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdatePasswordResult updatePassword(CscpUserPasswordUpdate cscpUserPasswordUpdate, boolean checkOld)
            throws Exception {
        Base64Encrypt base64Encrypt = new Base64Encrypt();
        CscpUser cscpUser = null;
        if (NO_VALIDATION.equals(cscpUserPasswordUpdate.getAuthentication())) {
            cscpUser = findByUsername(cscpUserPasswordUpdate.getUserName());
        } else {
            cscpUser = findByUsername(cscpUserPasswordUpdate.getUserName());
            if (cscpUser == null) {
                cscpUser = findByUsername(SecurityUtils.getCurrentUserName());
            }
        }
        if (cscpUser == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }

        //校验密码复杂度
        String newPassword = decryptPassword(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword()));

        String defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        if (Objects.equals(defaultPassword, newPassword)) {
            throw new BusinessException("该密码为初始化密码,请重新修改密码!");
        }
        if (checkOld) {
            if (!passwordEncoder.matches(decryptPassword(base64Encrypt.decrypt(cscpUserPasswordUpdate.getOldPassword())),
                    decryptPassword(cscpUser.getPassword()))) {
                throw new BusinessException(ResultCode.USER_PASSWORD_ERROR);
            }
        }


        CheckResult checkResult = new CheckResult();

        lengthCheck.check(newPassword, checkResult);
        lowerCaseCheck.check(newPassword, checkResult);
        upperCaseCheck.check(newPassword, checkResult);
        containDigitCheck.check(newPassword, checkResult);
        specialCharCheck.check(newPassword, checkResult);

        StringBuffer stringBuffer = new StringBuffer();
        if (checkResult.getErrorReason().size() > 0) {
            for (int i = 0; i < checkResult.getErrorReason().size(); i++) {
                if (stringBuffer.length() > 0) {
                    stringBuffer.append(",");
                }
                stringBuffer.append(checkResult.getErrorReason().get(i));
            }
        }
        if (checkResult.getConfirmCount() < 4) {
            if (stringBuffer.length() > 0) {
                stringBuffer.append(",");
            }
            stringBuffer.append("密码应再包含");
            for (int i = 0; i < checkResult.getConfirmInfo().size(); i++) {
                stringBuffer.append(checkResult.getConfirmInfo().get(i));
                if (i < checkResult.getConfirmInfo().size() - 1) {
                    stringBuffer.append("、");
                }
            }
            stringBuffer.append("等至少");
            stringBuffer.append(4 - checkResult.getConfirmCount());
            stringBuffer.append("项");

        }
        LambdaQueryWrapper<CscpUserPasswordChangeLog> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserPasswordChangeLog::getUserId, cscpUser.getId()).orderByDesc(CscpUserPasswordChangeLog::getTime);

        List<CscpUserPasswordChangeLog> changeLogs = cscpUserPasswordChangeLogRepository.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(changeLogs)) {
            for (int i = 0; i < changeLogs.size(); i++) {
                if (passwordEncoder.matches(newPassword, changeLogs.get(i).getPassword())) {
//                    throw new Exception("密码使用过");
                    stringBuffer.append(" 密码已使用过");
                }
            }
        }

        if (stringBuffer.length() > 0) {
            throw new Exception(stringBuffer.toString());
        }

        cscpUser.setPassword(passwordEncoder.encode(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword())));
        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }
        cscpUserRepository.updateById(cscpUser);

        CscpUserPasswordChangeLog cscpUserPasswordChangeLog = new CscpUserPasswordChangeLog();
        cscpUserPasswordChangeLog.setUserId(cscpUser.getId());
        cscpUserPasswordChangeLog.setTenantId(cscpUser.getTenantId() != null ? cscpUser.getTenantId() : Long.parseLong(ComponentConstant.USER_TENANTID));
        cscpUserPasswordChangeLog.setTime(LocalDateTime.now());
        cscpUserPasswordChangeLog.setPassword(passwordEncoder.encode(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword())));
        cscpUserPasswordChangeLog.setCompanyId(2L);
        cscpUserPasswordChangeLogRepository.insert(cscpUserPasswordChangeLog);

        return new UpdatePasswordResult(true, cscpUser.getId(), cscpUserPasswordUpdate.getNewPassword(), cscpUser.getLoginName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserPassword(CscpUserPasswordUpdate cscpUserPasswordUpdate) throws Exception {

        Base64Encrypt base64Encrypt = new Base64Encrypt();
        CscpUser cscpUser = findByUsername(cscpUserPasswordUpdate.getUserName());
        if (cscpUser == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }

        //校验密码复杂度
        String newPassword = decryptPassword(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword()));
        String defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        if (Objects.equals(defaultPassword, newPassword)) {
            throw new BusinessException("该密码为初始化密码,请重新修改密码!");
        }

        CheckResult checkResult = new CheckResult();

        lengthCheck.check(newPassword, checkResult);
        lowerCaseCheck.check(newPassword, checkResult);
        upperCaseCheck.check(newPassword, checkResult);
        containDigitCheck.check(newPassword, checkResult);
        specialCharCheck.check(newPassword, checkResult);

        StringBuffer stringBuffer = new StringBuffer();
        if (checkResult.getErrorReason().size() > 0) {
            for (int i = 0; i < checkResult.getErrorReason().size(); i++) {
                if (stringBuffer.length() > 0) {
                    stringBuffer.append(",");
                }
                stringBuffer.append(checkResult.getErrorReason().get(i));
            }
        }
        if (checkResult.getConfirmCount() < 4) {
            if (stringBuffer.length() > 0) {
                stringBuffer.append(",");
            }
            stringBuffer.append("密码应再包含");
            for (int i = 0; i < checkResult.getConfirmInfo().size(); i++) {
                stringBuffer.append(checkResult.getConfirmInfo().get(i));
                if (i < checkResult.getConfirmInfo().size() - 1) {
                    stringBuffer.append("、");
                }
            }
            stringBuffer.append("等至少");
            stringBuffer.append(4 - checkResult.getConfirmCount());
            stringBuffer.append("项");

        }

        if (stringBuffer.length() > 0) {
            throw new BusinessException(stringBuffer.toString());
        }

        LambdaQueryWrapper<CscpUserPasswordChangeLog> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserPasswordChangeLog::getUserId, cscpUser.getId()).orderByDesc(CscpUserPasswordChangeLog::getTime);

        cscpUser.setPassword(passwordEncoder.encode(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword())));
        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }
        cscpUserRepository.updateById(cscpUser);

        CscpUserPasswordChangeLog cscpUserPasswordChangeLog = new CscpUserPasswordChangeLog();
        cscpUserPasswordChangeLog.setUserId(cscpUser.getId());
        cscpUserPasswordChangeLog.setTenantId(cscpUser.getTenantId() != null ? cscpUser.getTenantId() : Long.parseLong(ComponentConstant.USER_TENANTID));
        cscpUserPasswordChangeLog.setTime(LocalDateTime.now());
        cscpUserPasswordChangeLog.setPassword(passwordEncoder.encode(base64Encrypt.decrypt(cscpUserPasswordUpdate.getNewPassword())));
        cscpUserPasswordChangeLog.setCompanyId(2L);
        cscpUserPasswordChangeLogRepository.insert(cscpUserPasswordChangeLog);

        // 维护系统操作日志
        SystemLogOperation systemLogOperation = new SystemLogOperation();
        systemLogOperation.setUri(RequestUtil.getURI());
        systemLogOperation.setParams(JsonUtils.objectToJson(cscpUserPasswordUpdate));
        systemLogOperation.setIp(InetAddress.getLocalHost().getHostAddress());
        systemLogOperation.setMethod("/api/system/updateUserPassword");
        systemLogOperation.setMainBody(cscpUser.getLoginName());
        systemLogOperation.setObjectBody("修改用户密码");
        systemLogOperation.setSystemType(1);
        systemLogOperation.setCreateBy(cscpUser.getId());
        systemLogOperation.setCreateName(cscpUser.getLoginName());
        systemLogOperation.setOperationSource("协同办公平台");
        systemLogOperation.setStatus("UPDATE" + CscpLogOperationDTO.SUCCESS);
        systemLogOperation.setOperationType(0);
        systemLogOperation.setOperationResult("成功");
//        systemLogOperationMapper.insert(systemLogOperation);

        if (westoneEncryptService.isCipherMachine()) {
            SystemLogOperateQueue.getInstance().addBlockingQueueData(new SystemLogOperateStock(systemLogOperation, westoneEncryptService, systemLogOperationMapper));
        } else {
            systemLogOperationMapper.insert(systemLogOperation);
        }

        return true;
    }

    @Override
    public boolean existByUsername(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return false;
        }

        CscpUser cscpUser = this.findByUsername(userName);
        return !Objects.isNull(cscpUser);
    }

    public boolean existByRealUsername(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return false;
        }
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper<>();
        if (com.ctsi.hndx.utils.StringUtils.isMobile(userName)) {
            String encMobile = westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(userName) : KeyCenterUtils.encrypt(userName);
            queryWrapper.eq(CscpUser::getMobile, encMobile);
        } else {
            queryWrapper.eq(CscpUser::getLoginName, userName);
        }
        Integer count = cscpUserRepository.selectCountNoAdd(queryWrapper);
        return count != null && count > 0;
    }

    public CscpUser findByUsername(String username) {
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper<>();
        String encMobile = westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(username) : KeyCenterUtils.encrypt(username);
        queryWrapper.eq(CscpUser::getLoginName, username)
                .or()
                .eq(CscpUser::getMobile, encMobile)
                .last("limit 1");
        CscpUser users = cscpUserRepository.selectOneNoAdd(queryWrapper);
        return users;
    }

    /**
     * 根据登录账号返回当前登录人的信息
     *
     * @param
     * @return
     */
    @Override
//@Cacheable(value = "userCache-findByCurrentUserName", key = "#loginName", unless = "#result == null")
    public Optional<CscpUserDTO> findByCurrentUserName(String loginName) {
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUser::getLoginName, loginName);
        List<CscpUser> list = cscpUserRepository.selectList(queryWrapper);
        if (list.size() > 1) {
            throw new DuplicateUserNameException("多个用户同名，请跟管理员确认");
        }
        CscpUser cscpUser = list.get(0);
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        BeanUtils.copyProperties(cscpUser, cscpUserDTO);
        boolean isDeFaultLogin = false;
        if (list.size() == 1) {
            // 此用户只属于一个组织机构
            List<CscpUserOrgDTO> cscpUserOrgDTOList = cscpUserOrgService.qryUserOrgByUserId(cscpUser.getId());
            if (cscpUserOrgDTOList.size() == 1) {
                if (cscpUserOrgDTOList.get(0).getCompanyId() == null) {
                    throw new BusinessException("CscpUserOrg表单位id为空！");
                }
                cscpUserDTO.setCompanyId(cscpUserOrgDTOList.get(0).getCompanyId());
                CscpOrg cscpOrgCom = cscpOrgService.getById(cscpUserOrgDTOList.get(0).getCompanyId());

                if (cscpOrgCom != null) {
                    cscpUserDTO.setCrmTenantType(cscpOrgCom.getCrmTenantType());
                    cscpUserDTO.setCompanyName(cscpOrgCom.getOrgName());
                    // TODO 查询机构信息 设置是否水印 2023-03-24 added lizuolang
                    cscpUserDTO.setHasWatermark(cscpOrgCom.getHasWatermark());
                    // TODO 查询机构信息 设置正文编辑是否分屏 2023-04-19 added lizuolang
                    cscpUserDTO.setSplitview(cscpOrgCom.getSplitview());
                    // TODO 查询机构信息 设置单位共享云盘空间大小，单位GB，默认2G 2023-05-05 added lizuolang
                    cscpUserDTO.setCloudDiskSpaceSize(cscpOrgCom.getCloudDiskSpaceSize());
                }

                cscpUserDTO.setDepartmentId(cscpUserOrgDTOList.get(0).getOrgId());
                cscpUserDTO.setDepartmentName(
                        cscpOrgService.getById(cscpUserOrgDTOList.get(0).getOrgId()).getOrgName());
                cscpUserDTO.setUserType(UserType.PERSON_USER);

            } else if (cscpUserOrgDTOList.size() > 1) {
                //当此用户属于多个组织机构时
                for (CscpUserOrgDTO cscpUserOrgDTO : cscpUserOrgDTOList) {
                    if (cscpUserOrgDTO.getDefaultDepartment() == CscpUserOrg.DEFAULTDEPARMENT.LOGIN.getCode()) {
                        cscpUserDTO.setCompanyId(cscpUserOrgDTO.getCompanyId());
                        CscpOrg cscpOrgCom = cscpOrgService.getById(cscpUserOrgDTO.getCompanyId());

                        if (cscpOrgCom != null) {
                            cscpUserDTO.setCrmTenantType(cscpOrgCom.getCrmTenantType());
                            cscpUserDTO.setCompanyName(cscpOrgCom.getOrgName());
                            // TODO 查询机构信息 设置是否水印 2023-03-24 added lizuolang
                            cscpUserDTO.setHasWatermark(cscpOrgCom.getHasWatermark());
                            // TODO 查询机构信息 设置正文编辑是否分屏 2023-04-19 added lizuolang
                            cscpUserDTO.setSplitview(cscpOrgCom.getSplitview());
                            // TODO 查询机构信息 设置单位共享云盘空间大小，单位GB，默认2G 2023-05-05 added lizuolang
                            cscpUserDTO.setCloudDiskSpaceSize(cscpOrgCom.getCloudDiskSpaceSize());
                        }
                        cscpUserDTO.setDepartmentId(cscpUserOrgDTO.getOrgId());
                        cscpUserDTO.setDepartmentName(
                                cscpOrgService.getById(cscpUserOrgDTO.getOrgId()).getOrgName());
                        cscpUserDTO.setUserType(UserType.PERSON_USER);

                        isDeFaultLogin = true;
                        break;
                    }
                }
                if (!isDeFaultLogin) {
                    throw new BusinessException(ResultCode.USER_NO_DEFAULT_LOGIN_ERROR);
                }
            } else {
                //当没有组织机构时，只能出现两种情况，平台的基础管理员暂时默认admin和租户管理员暂时默认一个
                if (!("admin".equals(loginName) || "aqsjgly".equals(loginName) || "aqbmgly".equals(loginName) || "wrdcadmin".equals(loginName))) {
                    LambdaQueryWrapper<TSysTenant> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(TSysTenant::getTenantLoginName, loginName);
                    TSysTenant tSysTenant = tSysTenantMapper.selectOne(lambdaQueryWrapper);
                    if (tSysTenant != null) {
                        //表示租户管理员
                        cscpUserDTO.setTenantId(tSysTenant.getId());
                        cscpUserDTO.setUserType(UserType.TENANT_USER);
                    } else {
                        //抛出异常,没有给用户甚至组织机构
                        throw new BusinessException("没有给用户设置组织机构，请联系管理员设置组织机构");
                    }
                } else {
                    cscpUserDTO.setUserType(UserType.SYSTEM_USER);
                }


            }
        }

        return Optional.of(cscpUserDTO);
    }

    @Override
//@CacheEvict(value = {"userCache-findByCurrentUserName", "userCache-getUserByUsernameOrMobile"}, key = "#loginName")
    public void clearUserCache(String loginName) {
        log.info("清除用户信息{}缓存:", loginName);
    }

    @Override
//@CacheEvict(value = {"userCache-getUserByUsernameOrMobile"}, key = "#loginName")
    public void clearUserCacheUsernameOrMobile(String loginName) {
        log.info("清除用户信息{}缓存:", loginName);
    }


    @Override
    public int passwordNeedChange(long userId, String password) {
        String defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        if (StringUtils.isNotBlank(defaultPassword) && defaultPassword.equals(password)) {
            return 1;
        }


//        List<CscpUserPasswordChangeLog> changeLogs = cscpUserPasswordChangeLogRepository.selectByExample(cscpUserPasswordChangeLogExample);
//        if(CollectionUtils.isNotEmpty(changeLogs)){
//            if(changeLogs.get(0).getTime().plusMonths(expireMonth).compareTo(ZonedDateTime.now())<0){
//                return 2;
//            }
//
//        }else{
//            //未找到修改密码记录，为老用户，为其创建一条修改记录
////            CscpUserPasswordChangeLog cscpUserPasswordChangeLog = new CscpUserPasswordChangeLog();
////            cscpUserPasswordChangeLog.setUserId(userId);
////            cscpUserPasswordChangeLog.setPassword(passwordEncoder.encode(password));
////            cscpUserPasswordChangeLog.setTime(ZonedDateTime.now());
////            cscpUserPasswordChangeLogRepository.insert(cscpUserPasswordChangeLog);
//        }
        return 0;
    }


    @Override
    public void updateUserDetailForLogin(long id) {
        CscpUser tud = cscpUserRepository.selectById(id);
        boolean isnew = false;
        if (tud == null) {
            tud = new CscpUser();
            tud.setCreateTime(LocalDateTime.now());
            tud.setId(id);
            isnew = true;
        }
        tud.setLastLogin(LocalDateTime.now());
        if (tud.getCreateTime() == null) {
            tud.setCreateTime(LocalDateTime.now());
        }
        tud.setNameHandle();
        if (isnew) {
            cscpUserRepository.insert(tud);
        } else {
            cscpUserRepository.updateById(tud);
        }
    }

    @Override
    public Integer getUserSorted(CscpUserDTO cscpUserDTO) {
        int userSorted;
        try {
            userSorted = cscpUserRepository.getUserMaxSorted();
        } catch (NullPointerException e) {
            log.error("数据库用户表 order_by 字段全部为空！");
            throw new BusinessException(ResultCode.USER_TABLE_SORTED_IS_NULL);
        }

        return userSorted + 1;
    }

    @Override
    public boolean existByMobile(String mobile) {

        LambdaQueryWrapper<CscpUser> userLam = new LambdaQueryWrapper();
        userLam.eq(CscpUser::getMobileStart, westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(mobile) : KeyCenterUtils.division(mobile)).last(SysConstant.LIMIT_ONE);
        return cscpUserRepository.selectCountNoAdd(userLam).intValue() > 0;

    }

    /**
     * 检查 strId 是否已存在
     *
     * @param strId 用户唯一标识
     * @return true=存在，false=不存在
     */
    @Override
    public boolean existByStrId(String strId) {
        if (ObjectUtil.isEmpty(strId)) {
            return false; // 如果 strId 为空，直接返回不存在
        }

        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpUser::getStrId, strId)
                .last(SysConstant.LIMIT_ONE); // 只查一条

        return cscpUserRepository.selectCountNoAdd(queryWrapper).intValue() > 0;
    }

    public CscpUser selectUserByMobile(String mobile) {
        CscpUser user = new CscpUser();
        LambdaQueryWrapper<CscpUser> userLam = new LambdaQueryWrapper();
        userLam.eq(CscpUser::getMobileStart, westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(mobile) : KeyCenterUtils.division(mobile)).last(SysConstant.LIMIT_ONE);
        List<CscpUser> cscpUsers = cscpUserRepository.selectListNoAdd(userLam);
        if (!cscpUsers.isEmpty()) {
            user = cscpUsers.get(0);
        }
        return user;

    }

    public List<CscpUser> selectUsersByMobile(String mobile) {
        LambdaQueryWrapper<CscpUser> userLam = new LambdaQueryWrapper();
        userLam.eq(CscpUser::getMobileStart, westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(mobile) : KeyCenterUtils.division(mobile)).last(" limit 2 ");
        return cscpUserRepository.selectListNoAdd(userLam);

    }

    /**
     * 根据手机号查询用户信息
     *
     * @param mobile
     * @return
     */
    @Override
    public CscpUser getUserdata(String mobile) {
        Assert.notNull(mobile, "手机号码不能为空");
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(CscpUser::getMobile, this.division(mobile));
        String encMobile = westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(mobile) : KeyCenterUtils.encrypt(mobile);
        lambdaQueryWrapper.eq(CscpUser::getMobile, encMobile);
        CscpUser cscpUser = cscpUserRepository.selectOne(lambdaQueryWrapper);
        if (null == cscpUser) {
            lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.like(CscpUser::getBackupMobile, westoneEncryptService.isCipherMachine() ? encMobile : mobile);
            List<CscpUser> cscpUserList = cscpUserRepository.selectListNoAdd(lambdaQueryWrapper);
            if (null != cscpUserList && cscpUserList.size() > 0) {
                cscpUser = cscpUserList.get(0);
            }
        }
        return cscpUser;
    }

    /**
     * 根据手机号查询手机号是否在系统已重复。包括备用手机号
     *
     * @param mobiles 电话号码字符串用,分割
     * @param userId  用户Id
     * @return
     */
    @Override
    public List<String> existMobileByMobileList(String mobiles, Long userId) {
        Assert.notNull(mobiles, "手机号码不能为空");
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper();
        List<String> mobileList = Arrays.asList(mobiles.split(","));
        List<String> keyMobileList = new ArrayList<>();
        if (null != userId && 0 != userId) {
            lambdaQueryWrapper.ne(CscpUser::getId, userId);
        }
        for (String mobile : mobileList) {
            String encMobile = westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(mobile) : KeyCenterUtils.encrypt(mobile);
            keyMobileList.add(encMobile);
        }
        lambdaQueryWrapper.and(wrapper -> {
            wrapper.in(CscpUser::getMobile, keyMobileList);
            for (String mobile : mobileList) {
                wrapper.or().like(CscpUser::getBackupMobile, westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(mobile) : mobile);
            }
        });

        List<CscpUser> cscpUserList = cscpUserRepository.selectListNoAdd(lambdaQueryWrapper);
        if (null == cscpUserList || cscpUserList.size() <= 0) {
            return null;
        } else {
            // 使用流和map方法获取所有用户手机号码并存储在List<String>中
            Set<String> phoneSet = new HashSet<>();
            for (CscpUser cscpUser : cscpUserList) {
                for (String mobile : mobileList) {
                    if (mobile.equals(cscpUser.getMobile())) {
                        phoneSet.add(mobile);
                    }
                    if (null != cscpUser.getBackupMobile() && StringUtils.isNotEmpty(cscpUser.getBackupMobile()) && cscpUser.getBackupMobile().contains(mobile)) {
                        phoneSet.add(mobile);
                    }
                }
            }
            return new ArrayList<String>(phoneSet);
        }
    }

    @Override
    public CscpUser selectUserByStrId(String strId) {
        if (StrUtil.isEmpty(strId)) {
            return null;
        }
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpUser::getStrId, strId)
        ;
        List<CscpUser> cscpUsers = cscpUserRepository.selectListNoAdd(lambdaQueryWrapper);

        return cscpUsers.isEmpty() ? null : cscpUsers.get(0);
    }

    @Override
    public Map<Boolean, String> queryUserUpdatePassword(String loginName) {
        Map<Boolean, String> map = new HashMap<>(16);
        // 获取用户信息
        CscpUser cscpUser = this.selectOneNoAdd(Wrappers.<CscpUser>lambdaQuery()
                .eq(CscpUser::getLoginName, StringUtils.trim(loginName)));
        if (cscpUser == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
        LambdaQueryWrapper<CscpUser> userLqw = Wrappers.lambdaQuery();
        userLqw.eq(CscpUser::getStatus, UserConstant.USER_ACTIVE_STATUS);
        userLqw.eq(CscpUser::getLoginName, StringUtils.trim(loginName));
        CscpUser statusUser = this.selectOneNoAdd(userLqw);
        if (statusUser == null) {
            throw new BusinessException(ResultCode.USER_ACCOUNT_FORBIDDEN);
        }

        // 判断用户密码是否是默认密码
        String password = cscpUser.getPassword();
        if (passwordEncoder.matches(sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD), password)) {
            map.put(true, "该密码为初始化密码,请重新修改密码!");
            return map;
        }
        // 不校验是否默认密码和过期时间
        map.put(false, "");
        return map;
        //// 获取用户修改密码最新数据
        //List<CscpUserPasswordChangeLog> passwordChangeLogList = cscpUserPasswordChangeLogRepository.selectList(Wrappers.<CscpUserPasswordChangeLog>lambdaQuery()
        //        .eq(CscpUserPasswordChangeLog::getUserId, cscpUser.getId()).orderByDesc(CscpUserPasswordChangeLog::getTime).last("limit 1"));
        //LocalDateTime createTime;
        //if (CollectionUtils.isEmpty(passwordChangeLogList)) {
        //    // 获取当前创建时间
        //    createTime = cscpUser.getCreateTime();
        //} else {
        //    CscpUserPasswordChangeLog passwordChangeLog = passwordChangeLogList.get(0);
        //    createTime = passwordChangeLog.getTime();
        //}
        //// 获取密码修改周期 单位为天
        //JSONObject jsonObject = JSON.parseObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.SECURITY_AUTHENTICATION));
        //long passwordUpdatePeriod = jsonObject.getLongValue("passwordUpdatePeriod");
        //long differDay = Duration.between(createTime, LocalDateTime.now()).toDays();
        //if (differDay > passwordUpdatePeriod) {
        //    map.put(true, String.format("账号密码已超过%s天未修改，请修改密码!",passwordUpdatePeriod));
        //} else {
        //    map.put(false, "");
        //}
        //return map;
    }


    @Override
    public PageResult<QueryUserLockDTO> queryUserLock(QueryUserLockVO vo, BasePageForm basePageForm) {
        if (StringUtils.isNotBlank(vo.getRealName())) {
            vo.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(vo.getRealName()) : this.division(vo.getRealName()));
        }
        if (StringUtils.isNotBlank(vo.getMobile())) {
            vo.setMobile(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(vo.getMobile()) : this.division(vo.getMobile()));
        }
        // 获取用户锁定列表
        IPage<QueryUserLockDTO> userLockIPage = cscpUserRepository.queryUserLock(PageHelperUtil.getMPlusPageByBasePage(basePageForm), vo, SecurityUtils.getCurrentUserId());
        List<QueryUserLockDTO> userLockDTOList = userLockIPage.getRecords();
        userLockDTOList.forEach(user -> {
            user.setRealName(DesensitizeUtil.desensitizedName(user.getRealName()));
            user.setMobile(DesensitizeUtil.desensitizedPhoneNumber(user.getMobile()));
        });
        return new PageResult<>(userLockDTOList, userLockIPage.getTotal(), basePageForm.getPageSize());
    }

    @Override
    public PageResult<QueryUserExamineDTO> queryUserExamine(QueryUserLockVO vo, BasePageForm basePageForm) {
        if (StringUtils.isNotBlank(vo.getRealName())) {
            vo.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(vo.getRealName()) : this.division(vo.getRealName()));
        }
        if (StringUtils.isNotBlank(vo.getMobile())) {
            vo.setMobile(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(vo.getMobile()) : this.division(vo.getMobile()));
        }
        // 获取用户审核列表
        IPage<QueryUserExamineDTO> userExamineIPage = cscpUserRepository.queryUserExamine(PageHelperUtil.getMPlusPageByBasePage(basePageForm), vo);
        List<QueryUserExamineDTO> userExamineDTOList = userExamineIPage.getRecords();
        userExamineDTOList.forEach(userExamine -> {
            userExamine.setUserSexName(Objects.equals(userExamine.getUserSex(), "0") ? "男" : "女");
            userExamine.setRealName(DesensitizeUtil.desensitizedName(userExamine.getRealName()));
        });
        return new PageResult<>(userExamineDTOList, userExamineIPage.getTotal(), basePageForm.getPageSize());
    }

    @Override
    public boolean updateUserExamine(UpdateUserExamineVO vo) {
        return this.update(Wrappers.<CscpUser>lambdaUpdate()
                .in(CscpUser::getId, vo.getUserIdList())
                .set(CscpUser::getExamineStatus, vo.getExamineStatus()));
    }

    @Override
    public boolean updateUserSecurityClassification(UpdateUserSecurityClassificationVO vo) {
        return this.update(Wrappers.<CscpUser>lambdaUpdate()
                .in(CscpUser::getId, vo.getUserId())
                .set(CscpUser::getSecurityClassificationCode, vo.getSecurityClassificationCode())
                .set(CscpUser::getSecurityClassificationCodeName, vo.getSecurityClassificationCodeName()));
    }

    @Override
    public PageResult<QuerySystemLogDTO> querySystemLog(QuerySystemLogVO vo, BasePageForm basePageForm) throws ParseException {
        IPage iPage = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        List<String> dateList = getDateList();
        // 获取日志类型
        long userId = SecurityUtils.getCurrentUserId();
        int systemType = 1;
        if (userId == 2L) {
            // admin只查询系统日志
            systemType = 0;
        }
        if (vo.getLogType() == 0) {
            // 查询近半年的日志
            LambdaQueryWrapper<SystemLogOperation> lambdaQuery = Wrappers.<SystemLogOperation>lambdaQuery();
            if (StringUtils.isNotBlank(vo.getStartTime()) && StringUtils.isNotBlank(vo.getEndTime())) {
                lambdaQuery.ge(SystemLogOperation::getCreateTime, vo.getStartTime());
                lambdaQuery.le(SystemLogOperation::getCreateTime, vo.getEndTime());
            } else {
                lambdaQuery.ge(SystemLogOperation::getCreateTime, dateList.get(0) + " 00:00:00");
                lambdaQuery.le(SystemLogOperation::getCreateTime, dateList.get(dateList.size() - 1) + " 23:59:59");
            }
            // 如果是安全审计只查询管理员行为日志
            if (userId == 3L) {
                lambdaQuery.in(SystemLogOperation::getCreateBy, Arrays.asList(1, 2, 3));
            }
            // 如果是安全保密只查询普通用户行为日志不查询管理员
            if (userId == 1L) {
                lambdaQuery.notIn(SystemLogOperation::getCreateBy, Arrays.asList(1, 2, 3));
            }
            lambdaQuery.eq(SystemLogOperation::getSystemType, systemType);
            lambdaQuery.like(StringUtils.isNotBlank(vo.getIp()), SystemLogOperation::getIp, vo.getIp());
            lambdaQuery.like(StringUtils.isNotBlank(vo.getMainBody()), SystemLogOperation::getMainBody, vo.getMainBody());
            lambdaQuery.like(StringUtils.isNotBlank(vo.getObjectBody()), SystemLogOperation::getObjectBody, vo.getObjectBody());
            lambdaQuery.eq(vo.getOperationType() != null, SystemLogOperation::getOperationType, vo.getOperationType());
            lambdaQuery.orderByDesc(SystemLogOperation::getCreateTime);
            IPage<SystemLogOperation> page = systemLogOperationMapper.selectPageNoAdd(iPage, lambdaQuery);
            if (westoneEncryptService.isCipherMachine()) {
                page.getRecords().stream().forEach(x -> {
                    String userName = x.getCreateName();
                    String optResult = x.getOperationResult();
                    String mainBody = x.getMainBody();
                    String objectBody = x.getObjectBody();
                    String optTime = x.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"));
                    String hmacContentSrc = UserAESUtil.md5WithSalt(userName + mainBody + objectBody + x.getSystemType() + optResult + optTime);
                    if (StringUtils.isBlank(x.getHmacOperateContent())) {
                        String hmacContent = westoneEncryptService.calculateSM3HMAC(hmacContentSrc);
                        LambdaUpdateWrapper<SystemLogOperation> lambdaUpdate = Wrappers.lambdaUpdate();
                        lambdaUpdate.eq(SystemLogOperation::getId, x.getId()).set(SystemLogOperation::getHmacOperateContent, hmacContent);
                        systemLogOperationMapper.update(null, lambdaUpdate);
                        x.setDataIsIntegrity(0);
                    } else {
                        boolean isIntegrity = westoneEncryptService.compareSM3HMAC(hmacContentSrc, x.getHmacOperateContent());
                        if (isIntegrity) {
                            x.setDataIsIntegrity(0);
                        } else {
                            x.setDataIsIntegrity(1);
                        }
                    }
                });
            }
            Map<Long, String> errorMap = getErrorMap(page, null, 1);
            return new PageResult<>(convertQuerySystemLogList(page.getRecords(), errorMap), page.getTotal(), basePageForm.getPageSize());
        } else {
            // 查询半年前的日志
            LambdaQueryWrapper<SystemLogOperationHistory> lambdaQuery = Wrappers.<SystemLogOperationHistory>lambdaQuery();
            if (StringUtils.isNotBlank(vo.getStartTime()) && StringUtils.isNotBlank(vo.getEndTime())) {
                lambdaQuery.ge(SystemLogOperationHistory::getCreateTime, vo.getStartTime());
                lambdaQuery.le(SystemLogOperationHistory::getCreateTime, vo.getEndTime());
            } else {
                lambdaQuery.lt(SystemLogOperationHistory::getCreateTime, dateList.get(0) + " 00:00:00");
            }
            // 如果是安全审计只查询管理员行为日志
            if (userId == 3L) {
                lambdaQuery.in(SystemLogOperationHistory::getCreateBy, Arrays.asList(1, 2, 3));
            }

            if (userId == 1L) {
                lambdaQuery.notIn(SystemLogOperationHistory::getCreateBy, Arrays.asList(1, 2, 3));
            }
            lambdaQuery.eq(SystemLogOperationHistory::getSystemType, systemType);
            lambdaQuery.like(StringUtils.isNotBlank(vo.getIp()), SystemLogOperationHistory::getIp, vo.getIp());
            lambdaQuery.like(StringUtils.isNotBlank(vo.getMainBody()), SystemLogOperationHistory::getMainBody, vo.getMainBody());
            lambdaQuery.like(StringUtils.isNotBlank(vo.getObjectBody()), SystemLogOperationHistory::getObjectBody, vo.getObjectBody());
            lambdaQuery.eq(vo.getOperationType() != null, SystemLogOperationHistory::getOperationType, vo.getOperationType());
            lambdaQuery.orderByDesc(SystemLogOperationHistory::getCreateTime);
            IPage<SystemLogOperationHistory> page = systemLogOperationHistoryMapper.selectPageNoAdd(iPage, lambdaQuery);
            if (westoneEncryptService.isCipherMachine()) {
                page.getRecords().stream().forEach(x -> {
                    String userName = x.getCreateName();
                    String optResult = x.getOperationResult();
                    String mainBody = x.getMainBody();
                    String objectBody = x.getObjectBody();
                    String optTime = x.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"));
                    String hmacContentSrc = UserAESUtil.md5WithSalt(userName + mainBody + objectBody + x.getSystemType() + optResult + optTime);
                    if (StringUtils.isBlank(x.getHmacOperateContent())) {
                        String hmacContent = westoneEncryptService.calculateSM3HMAC(hmacContentSrc);
                        LambdaUpdateWrapper<SystemLogOperation> lambdaUpdate = Wrappers.lambdaUpdate();
                        lambdaUpdate.eq(SystemLogOperation::getId, x.getId()).set(SystemLogOperation::getHmacOperateContent, hmacContent);
                        systemLogOperationMapper.update(null, lambdaUpdate);
                        x.setDataIsIntegrity(0);
                    } else {
                        boolean isIntegrity = westoneEncryptService.compareSM3HMAC(hmacContentSrc, x.getHmacOperateContent());
                        if (isIntegrity) {
                            x.setDataIsIntegrity(0);
                        } else {
                            x.setDataIsIntegrity(1);
                        }
                    }
                });
            }
            Map<Long, String> errorMap = getErrorMap(null, page, 2);
            return new PageResult<>(convertQuerySystemLogDtoList(page.getRecords(), errorMap), page.getTotal(), basePageForm.getPageSize());
        }
    }

    private List<QuerySystemLogDTO> convertQuerySystemLogDtoList(List<SystemLogOperationHistory> records, Map<Long, String> errorMap) {
        if (records == null) {
            return null;
        }

        List<QuerySystemLogDTO> list = new ArrayList<QuerySystemLogDTO>(records.size());
        for (SystemLogOperationHistory systemLogOperationHistory : records) {
            QuerySystemLogDTO querySystemLogDTO = userMapping.convertQuerySystemLogDto(systemLogOperationHistory);
            if (ObjectUtil.isNull(querySystemLogDTO.getCscpLogOperationId())) {
                querySystemLogDTO.setError(querySystemLogDTO.getObjectBody());
            } else {
                querySystemLogDTO.setError(errorMap.get(systemLogOperationHistory.getCscpLogOperationId()));
            }
            list.add(querySystemLogDTO);
        }
        return list;
    }

    public List<QuerySystemLogDTO> convertQuerySystemLogList(List<SystemLogOperation> systemLogOperationList, Map<Long, String> errorMap) {
        if (systemLogOperationList == null) {
            return null;
        }

        List<QuerySystemLogDTO> list = new ArrayList<QuerySystemLogDTO>(systemLogOperationList.size());
        for (SystemLogOperation systemLogOperation : systemLogOperationList) {
            QuerySystemLogDTO querySystemLogDTO = userMapping.convertQuerySystemLog(systemLogOperation);
            if (ObjectUtil.isNull(systemLogOperation.getCscpLogOperationId())) {
                querySystemLogDTO.setError(systemLogOperation.getObjectBody());
            } else {
                querySystemLogDTO.setError(errorMap.get(systemLogOperation.getCscpLogOperationId()));
            }
            list.add(querySystemLogDTO);
        }
        return list;
    }

    //关联失败原因
    private Map<Long, String> getErrorMap(IPage<SystemLogOperation> page, IPage<SystemLogOperationHistory> historyPage, int type) {
        Map<Long, String> errorMap = new HashMap<>();
        List<Long> operationIds = new ArrayList<>();
        if (type == 1) {
            if (CollectionUtil.isNotEmpty(page.getRecords())) {
                operationIds = page.getRecords().stream().
                        filter(Objects::nonNull).map(e -> e.getCscpLogOperationId()).distinct().collect(Collectors.toList());
            }
        } else {
            if (CollectionUtil.isNotEmpty(historyPage.getRecords())) {
                operationIds = historyPage.getRecords().stream().
                        filter(Objects::nonNull).map(e -> e.getCscpLogOperationId()).distinct().collect(Collectors.toList());
            }
        }

        if (CollectionUtil.isNotEmpty(operationIds)) {
            LambdaQueryWrapper<CscpLogOperation> logOperationQuery = new LambdaQueryWrapper();
            logOperationQuery.in(CscpLogOperation::getId, operationIds);
            List<CscpLogOperation> cscpLogOperations = cscpLogOperationRepository.selectListNoAdd(logOperationQuery);
            errorMap = cscpLogOperations.stream().filter(e ->
                    ObjectUtil.isNotNull(e.getError())).collect(Collectors.toMap(CscpLogOperation::getId, CscpLogOperation::getError));
        }
        return errorMap;
    }

    @Override
    public List<QueryUserSelectedVO.QueryUserSelectedInfoVO> queryUserSelected(QueryUserSelectedVO vo) {
        List<QueryUserSelectedVO.QueryUserSelectedInfoVO> userSelectedInfoVOList = vo.getQueryUserSelectedInfoVOList();
        if (StringUtils.isBlank(vo.getSecurityName()) || vo.getSecurityName().contains("内部") || vo.getSecurityName().contains("秘密")) {
            return userSelectedInfoVOList;
        }
        List<Long> userIdList = userSelectedInfoVOList.stream().map(QueryUserSelectedVO.QueryUserSelectedInfoVO::getUserId).distinct().collect(Collectors.toList());
        List<CscpUser> cscpUserList = cscpUserRepository.selectList(Wrappers.<CscpUser>lambdaQuery().in(CscpUser::getId, userIdList));
        Map<Long, String> userMap = cscpUserList.stream().collect(Collectors.toMap(CscpUser::getId, CscpUser::getSecurityClassificationCode));
        if (vo.getSecurityName().contains("机密") || vo.getSecurityName().contains("明电")) {
            // 只有重要和核心的类别的人
            userSelectedInfoVOList = userSelectedInfoVOList.stream().filter(user -> userMap.get(user.getUserId()) != null && (Objects.equals(userMap.get(user.getUserId()), "1") || Objects.equals(userMap.get(user.getUserId()), "2"))).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(userSelectedInfoVOList)) {
            throw new BusinessException("选中的人员权限不足请重新选择");
        }
        return userSelectedInfoVOList;
    }

    @Override
    public ResultVO<Boolean> checkUserSecurityLevel(CheckUserSecretLevelVO vo) {
        List<Long> userSelectedInfoVOList = vo.getQueryUserSelectedInfoVOList();
        if (StringUtils.isBlank(vo.getSecurityName()) || vo.getSecurityName().contains("内部") || vo.getSecurityName().contains("秘密")
                || vo.getSecurityName().contains("公开")) {
            return ResultVO.success(Boolean.TRUE);
        }

        //0522 子流程返主流程 ，存在userSelectedInfoVOList为空的情况，直接过
        if (CollectionUtil.isEmpty(userSelectedInfoVOList)) {
            return ResultVO.success(Boolean.TRUE);
        }

        // 获取用户列表
        List<CscpUser> cscpUserList = cscpUserRepository.selectList
                (Wrappers.<CscpUser>lambdaQuery().in(CscpUser::getId, userSelectedInfoVOList));
        Map<Long, String> securityClassificationCodeMap = cscpUserList.stream().collect(Collectors.toMap(CscpUser::getId, CscpUser::getSecurityClassificationCode));

        Map<Long, String> nameMap = cscpUserList.stream().collect(Collectors.toMap(CscpUser::getId, CscpUser::getRealName));

        StringBuffer buffer = new StringBuffer();
        if (vo.getSecurityName().contains("机密")) {
            userSelectedInfoVOList = userSelectedInfoVOList.stream().
                    filter(userId -> securityClassificationCodeMap.get(userId) != null && (Objects.equals(securityClassificationCodeMap.
                            get(userId), "0"))).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(userSelectedInfoVOList)) {
                for (int i = 0; i < userSelectedInfoVOList.size(); i++) {
                    if (i == userSelectedInfoVOList.size() - 1) {
                        buffer.append(nameMap.get(userSelectedInfoVOList.get(i)));
                    } else {
                        buffer.append(nameMap.get(userSelectedInfoVOList.get(i))).append("、");
                    }
                }
                buffer.append("涉密等级不达标，请修改后重新提交！");
                return ResultVO.error(buffer.toString(), Boolean.FALSE);
            }
        }
        return ResultVO.success(Boolean.TRUE);
    }

    /**
     * 获取日期区间列表
     *
     * @return 响应参数
     * @throws ParseException
     */
    private List<String> getDateList() throws ParseException {
        Calendar c = Calendar.getInstance();
        // 获取当前日期近半年
        c.add(Calendar.MONTH, -4);
        String before_six = c.get(Calendar.YEAR) + "-" + c.get(Calendar.MONTH) + "-" + c.get(Calendar.DATE);//六个月前
        List<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");// 格式化为年月
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(sdf.parse(before_six));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), min.get(Calendar.DATE));
        max.setTime(sdf.parse(sdf.format(new Date())));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH) + 1, min.get(Calendar.DATE));
        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }


    @Override
//@Cacheable(value = "userCache-getUserByUsernameOrMobile", key = "#userName", unless = "#result == null")
    public CscpUserDTO getUserByUsernameOrMobile(String userName) {
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        Assert.hasText(userName, "用户名不能为空");
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper();
        // lambdaQueryWrapper.eq(CscpUser::getLoginName, userName).or().eq(CscpUser::getMobile, this.division(userName))
        //         .last(SysConstant.LIMIT_ONE);
        String encMobile = westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(userName) : KeyCenterUtils.encrypt(userName);
        lambdaQueryWrapper.eq(CscpUser::getLoginName, userName).or().eq(CscpUser::getMobile, encMobile)
                .last(SysConstant.LIMIT_ONE);
        CscpUser cscpUser = cscpUserRepository.selectOne(lambdaQueryWrapper);
        if (null == cscpUser) {
            lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.like(CscpUser::getBackupMobile, westoneEncryptService.isCipherMachine() ? encMobile : userName);
            List<CscpUser> cscpUserList = cscpUserRepository.selectListNoAdd(lambdaQueryWrapper);
            if (null != cscpUserList && cscpUserList.size() > 0) {
                cscpUser = cscpUserList.get(0);
            }
        }
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
        if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
            throw new BusinessException(ResultCode.USER_ACCOUNT_FORBIDDEN);
        }

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 数据完整性校验
            String hmacMobile = cscpUser.getHmacMobile();
            String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
            boolean isIntegrity = westoneEncryptService.compareSM3HMAC(src, hmacMobile);
            if (!isIntegrity) {
                throw new BusinessException(ResultCode.DATA_NOT_INTEGRITY);
            }

            // TODO 用户访问权限完整性校验
            Long userId = cscpUser.getId();
            String loginName = cscpUser.getLoginName();
            List<CscpRolesDTO> cscpRolesDTOS = cscpUserRoleService.selectRolesByUserId(userId);
            if (null != cscpRolesDTOS && cscpRolesDTOS.size() > 0) {
                for (CscpRolesDTO cscpRolesDTO : cscpRolesDTOS) {
                    String hmacRoleIdName = cscpRolesDTO.getHmacRoleIdName();
                    String hmacRoleIdNameSrc = userId + loginName + cscpRolesDTO.getId() + cscpRolesDTO.getName();
                    boolean isRoleIntegrity = westoneEncryptService.compareSM3HMAC(hmacRoleIdNameSrc, hmacRoleIdName);
                    if (!isRoleIntegrity) {
                        throw new BusinessException("登录失败，用户访问权限[" + cscpRolesDTO.getName() + "]被篡改");
                    }
                }
            }
        }

        BeanUtils.copyProperties(cscpUser, cscpUserDTO);
        return cscpUserDTO;
    }


    /**
     * 查询不在当前list中的的用户信息
     *
     * @param iPage
     * @param queryCscpUserNotInListDTO
     * @return
     */
    @Override
    public IPage<CscpUserDTO> queryCscpUserNotInListDTO(IPage iPage, QueryCscpUserNotInListDTO queryCscpUserNotInListDTO) {
        return cscpUserRepository.queryCscpUserNotInListDTO(iPage, queryCscpUserNotInListDTO);
    }


    /**
     * 获取当前登录用户的基础信息
     *
     * @param uid
     * @return
     */
    @Override
    public CurrentCscpUserDTO findCurrentUserDto(long uid) {
        CurrentCscpUserDTO userDetailDTO = new CurrentCscpUserDTO();
        CscpUser cscpUser = cscpUserRepository.selectById(uid);
        if (cscpUser != null) {
            if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
                return userDetailDTO;
            }
            BeanUtils.copyProperties(cscpUser, userDetailDTO);
            CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            userDetailDTO.setDepartmentName(cscpUserDetail.getDepartmentName());
            userDetailDTO.setCompanyName(cscpUserDetail.getCompanyName());
            userDetailDTO.setUserType(UserType.getUserType(cscpUserDetail.getUserType()));
            userDetailDTO.setCompanyId(cscpUserDetail.getCompanyId());
            userDetailDTO.setDepartmentId(cscpUserDetail.getDepartmentId());
            userDetailDTO.setTenantId(cscpUserDetail.getTenantId());
            userDetailDTO.setUserName(cscpUserDetail.getUsername());
            if (cscpUser.getSex() != null) {
                if (cscpUser.getSex() == 1) {
                    userDetailDTO.setSex("女");
                } else if (cscpUser.getSex() == 0) {
                    userDetailDTO.setSex("男");
                } else {
                    userDetailDTO.setSex("");
                }
            } else {
                userDetailDTO.setSex("");
            }

            //获取当前登录用户的组织机构信息
            CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(cscpUserDetail.getCompanyId());
            CscpOrgDTO cscpDeparDTO = cscpOrgService.findOne(cscpUserDetail.getDepartmentId());
            cscpOrgDTO.setShowHomeCpj(cscpDeparDTO.getShowHomeCpj());
            userDetailDTO.setCscpOrgDTO(cscpOrgDTO);

            LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpUserOrg::getUserId, cscpUserDetail.getId());
            queryWrapper.eq(CscpUserOrg::getOrgId, cscpUserDetail.getDepartmentId());
            queryWrapper.last("limit 1");
            CscpUserOrg userOrgList = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
            if (userOrgList != null) {
                userDetailDTO.setPost(userOrgList.getPost());
            }

            //获取干部信息
            BizCadreInformation bizCadreInformation = bizCadreInformationMapper.selectOneNoAdd(new LambdaQueryWrapper<BizCadreInformation>().eq(BizCadreInformation::getUserId, uid));
            if (!Objects.isNull(bizCadreInformation)) {
//                userDetailDTO.setSex(bizCadreInformation.getSex());
                userDetailDTO.setCurrentPosition(bizCadreInformation.getCurrentPosition());
                // 参加工作时间不为空才设置工龄
                if (null != bizCadreInformation.getWorkingHours()) {
                    userDetailDTO.setWorkingYears(String.valueOf(LocalDateTime.now().getYear() - bizCadreInformation.getWorkingHours().getYear()));
                }
                if (bizCadreInformation.getCurrentPosition() != null) {
                    String currentPosition = DictionariesCodeUtil.getDictionariesCodeName("currentRank").get(bizCadreInformation.getCurrentPosition());
                    userDetailDTO.setCurrentPosition(currentPosition);
                }

            }
            //获取租户名称
            TSysTenant tSysTenant = tSysTenantMapper.selectById(userDetailDTO.getTenantId());
            if (!Objects.isNull(tSysTenant)) {
                userDetailDTO.setTenantName(tSysTenant.getTenantName());
            }
            //获取部门负责人信息
            String departmentHeadName = cscpUserOrgService.getDepartmentHeadName(cscpUserDetail.getDepartmentId());
            userDetailDTO.setDepartmentHeadName(departmentHeadName);
            //查询是否部门管理员
            List<TDeptManagementAuthorityDTO> tDeptManagementAuthorityDTOList = tDeptManagementAuthorityService.findManageDeptByUserId(uid);
            if (CollectionUtils.isNotEmpty(tDeptManagementAuthorityDTOList)) {
                userDetailDTO.setUserType(UserType.DEPT_USER);
            }
            //回显人员标签
            if (StringUtils.isNotEmpty(cscpUser.getPersonLabel())) {
                userDetailDTO.setPersonLabelList(Arrays.asList(cscpUser.getPersonLabel().split(",")));
            }
            // 用户角色
            userDetailDTO.setRoleCodes(cscpUserDetail.getRoleCodes());
            // 密级
            userDetailDTO.setSecurityClassificationCodeName(cscpUser.getSecurityClassificationCodeName());
            // 设置版主独立管理应用ID集合
            userDetailDTO.setModeratorAppIdList(cscpUserRepository.getModeratorManageAppIds(cscpUserDetail.getId()));

        }
        return userDetailDTO;
    }

    @Override
    public List<CscpUserDTO> queryCscpUserList(String userName) {
        return cscpUserRepository.selectList(
                        new LambdaQueryWrapper<CscpUser>()
                                .like(CscpUser::getLoginName, userName))
                .stream().map(i -> BeanConvertUtils.copyProperties(i, CscpUserDTO.class)).collect(Collectors.toList());
    }

    @Override
    public List<CscpUserDTO> listQueryUserDTO(List<Long> userIdList, boolean isDispaly) {
        return cscpUserRepository.selectListNoAdd(
                        new LambdaQueryWrapper<CscpUser>()
                                .in(CscpUser::getId, userIdList)
                                .eq(isDispaly, CscpUser::getDisplay, UserConstant.USER_ACTIVE_STATUS)
                                .eq(CscpUser::getStatus, UserConstant.USER_ACTIVE_STATUS)
                                .select(CscpUser.class, tableFieldInfo -> !(tableFieldInfo.getField().isAnnotationPresent(MybatisNoSelect.class))))
                .stream().map(i -> BeanConvertUtils.copyProperties(i, CscpUserDTO.class)).collect(Collectors.toList());
    }

    @Override
    public CscpUserDTO selectBranchLeader(Long orgId) {
        if (!this.checkDepartment(orgId)) {
            throw new BusinessException("查询的机构类型必须是部门");
        }
        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(orgId);
        if (Objects.isNull(cscpOrgDTO)) {
            return null;
        }
        Long branchLeaderId = cscpOrgDTO.getBranchLeaderId();
        return this.findByUserId(branchLeaderId);
    }

    @Override
    public List<CscpUserDTO> selectDepartmentHead(Long orgId) {
       /* if (!this.checkDepartment(orgId)) {
            throw new BusinessException("查询的机构类型必须是部门");
        }*/
        CscpUserOrgDTO cscpUserOrgDTO = new CscpUserOrgDTO();
        cscpUserOrgDTO.setDepartmentHead(1);
        cscpUserOrgDTO.setOrgId(orgId);
        List<Long> departmentHeadIdList = cscpUserOrgService.selectUserOrgList(cscpUserOrgDTO).stream().
                map(x -> x.getUserId()).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(departmentHeadIdList)) {
            return new ArrayList<>();
        }
        return this.listQueryUserDTO(departmentHeadIdList, true);
    }

    @Override
    public CscpUserDTO selectBranchLeaderAndDepartmentHead(Long orgId) {
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        CscpUserDTO branchLeaderIdDTO = this.selectBranchLeader(orgId);
        cscpUserDTO.setBranchLeaderDTO(branchLeaderIdDTO);

        List<CscpUserDTO> departmentHeadDTOList = this.selectDepartmentHead(orgId);
        cscpUserDTO.setDepartmentHeadDTOList(departmentHeadDTOList);

        return cscpUserDTO;
    }

    @Override
    public boolean saveDepartmentHead(Long orgId, Long userId) {
        // 判断用户是否被禁用
        CscpUser cscpUser = cscpUserRepository.selectById(userId);
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException("用户不存在");
        }
        if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
            throw new BusinessException("用户已被禁用");
        }
        CscpOrgDTO orgDTO = cscpOrgService.findOne(orgId);
        if (Objects.isNull(orgDTO) || orgDTO.getType() != 3) {
            throw new BusinessException("没有找到该部门，或该组织机构的类型不是部门");
        }

        // 查询该用户机构信息
        CscpUserOrgDTO qryDTO = new CscpUserOrgDTO();
        qryDTO.setOrgId(orgId);
        qryDTO.setUserId(userId);
        CscpUserOrgDTO cscpUserOrgDTO = cscpUserOrgService.selectUserOrgList(qryDTO).get(0);

        if (Objects.isNull(cscpUserOrgDTO)) {
            throw new BusinessException("未找到该用户部门关联信息");
        }

        // 设置成部门领导
        cscpUserOrgDTO.setDepartmentHead(1);
        cscpUserOrgService.update(cscpUserOrgDTO);
        return true;
    }

    @Override
    public boolean deleteDepartmentHead(Long orgId, Long userId) {
        // 判断是否为部门
        CscpOrgDTO orgDTO = cscpOrgService.findOne(orgId);
        if (Objects.isNull(orgDTO) || orgDTO.getType() != 3) {
            throw new BusinessException("没有找到该部门，或该组织机构的类型不是部门");
        }
        // 查询该用户机构信息
        CscpUserOrgDTO dto = new CscpUserOrgDTO();
        dto.setUserId(userId);
        dto.setOrgId(orgId);
        CscpUserOrgDTO cscpUserOrgDTO = cscpUserOrgService.selectUserOrgList(dto).get(0);

        // 设置成部门领导
        cscpUserOrgDTO.setDepartmentHead(0);
        cscpUserOrgService.update(cscpUserOrgDTO);
        return true;
    }

    @Override
    public boolean deleteUserOrg(Long orgId, Long userId) {
        // 需要判断该用户关联了几个部门：如果只有一个，则不允许移除
        LambdaQueryWrapper<CscpUserOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpUserOrg::getUserId, userId);
        Integer count = cscpUserOrgRepository.selectCountNoAdd(lambdaQueryWrapper);
        if (count == 1) {
            throw new BusinessException("该用户只存在一个关联部门，不允许移除");
        }

        CscpUserOrgDTO dto = new CscpUserOrgDTO();
        dto.setUserId(userId);
        dto.setOrgId(orgId);
        cscpUserOrgService.deleteByUserOrg(dto);
        // 推送
        PushUserEvent pushUserEvent = new PushUserEvent();
        List<Long> userIdList = new ArrayList<>(1);
        userIdList.add(userId);
        pushUserEvent.setUserIdList(userIdList);
        eventPublisher.publishEvent(pushUserEvent);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBranchLeaderAndDepartmentHead(CscpOrgDTO cscpOrgDTO) {
        if (!this.checkDepartment(cscpOrgDTO.getId())) {
            throw new BusinessException("新增分管领导或者部门领导的机构类型必须是部门");
        }

        List<Long> departmentHeadIdList = cscpOrgDTO.getDepartmentHeadIdList();

        // 先删除该机构的所有部门领导
        LambdaUpdateWrapper<CscpUserOrg> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(CscpUserOrg::getOrgId, cscpOrgDTO.getId()).set(CscpUserOrg::getDepartmentHead, null);
        cscpUserOrgService.update(null, lambdaUpdateWrapper);

        // 添加部门领导
        if (CollectionUtils.isNotEmpty(departmentHeadIdList)) {
            for (Long departmentHeadId : departmentHeadIdList) {
                // 再新增部门领导
                LambdaUpdateWrapper<CscpUserOrg> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(CscpUserOrg::getOrgId, cscpOrgDTO.getId())
                        .eq(CscpUserOrg::getUserId, departmentHeadId)
                        .set(CscpUserOrg::getDepartmentHead, 1);
                cscpUserOrgService.update(null, updateWrapper);
            }
        }

        // 先删除该机构的分管领导
        LambdaUpdateWrapper<CscpOrg> branchUpdateWrapper = Wrappers.lambdaUpdate();
        branchUpdateWrapper.eq(CscpOrg::getId, cscpOrgDTO.getId()).set(CscpOrg::getBranchLeaderId, null);
        cscpOrgService.update(null, branchUpdateWrapper);
        // 部门添加分管领导
        if (!Objects.isNull(cscpOrgDTO.getBranchLeaderId())) {
            // 然后新增分管领导
            LambdaUpdateWrapper<CscpOrg> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(CscpOrg::getId, cscpOrgDTO.getId())
                    .set(CscpOrg::getBranchLeaderId, cscpOrgDTO.getBranchLeaderId());
            cscpOrgService.update(null, updateWrapper);
        }
        return true;
    }

    /**
     * 判断名称是否相同，如果相同返回一个新的名称 名称后面 _数字
     *
     * @param userName
     * @return
     */
    /*@Override
    public synchronized String getUserNameMaxNumber(String userName) {

        userName = tsfh(userName);

        if (existByUsername(userName)) {
            QueryWrapper<CscpUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.select(CscpUser.class, info -> "login_name".equals(info.getColumn()))
                    .likeRight("login_name", userName + SysConstant.NAME_SPLIT);

            CscpUser cscpUser = cscpUserRepository.selectOne(queryWrapper);
            if (cscpUser != null) {
                String[] parts = cscpUser.getLoginName().split(SysConstant.NAME_SPLIT);
                if (parts.length > 1) {
                    try {
                        int t = Integer.parseInt(parts[1]) + 2;
                        return userName + SysConstant.NAME_SPLIT + t;
                    } catch (NumberFormatException e) {
                        // 处理数字转换失败的情况
                        return userName + SysConstant.NAME_SPLIT + 1;
                    }
                } else {
                    return userName + SysConstant.NAME_SPLIT + 1;
                }
            } else {
                return userName + SysConstant.NAME_SPLIT + 1;
            }
        }
        return userName;
    }*/
    /*@Override
    public String getUserNameMaxNumber(String baseName) {
        synchronized (lock) {
            int suffix = 1;
            String candidate = baseName;

            while (existByUsername(candidate)) {
                suffix++;
                candidate = baseName + "_" + suffix;
            }

            return candidate;
        }
    }*/
    @Override
    public String getUserNameMaxNumber(String baseName) {
        if (!existByRealUsername(baseName)) {
            return baseName;
        }

        String lockKey = "lock:username:" + baseName;
        String key = "username_counter:new:" + baseName;

        // 使用分布式锁确保原子性
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 60, TimeUnit.SECONDS);

        if (Boolean.TRUE.equals(locked)) {
            try {
                // 在锁保护下执行用户名生成逻辑
                if (!existByRealUsername(baseName)) {
                    return baseName;
                }

                // 使用原子递增操作
                Long suffix = redisTemplate.opsForValue().increment(key, 1);

                // 如果是第一次创建计数器，需要从数据库获取真实的最大值
                if (suffix == 1) {
                    long maxFromDB = getMaxSuffixFromDBJia1(baseName);
                    if (maxFromDB > 1) {
                        // 重置计数器为数据库中的最大值
                        redisTemplate.opsForValue().set(key, maxFromDB);
                        suffix = maxFromDB;
                    }
                }

                String candidate = baseName + "_" + suffix;

                // 验证候选用户名是否已存在于数据库中
                if (!existByRealUsername(candidate)) {
                    return candidate;
                } else {
                    // 如果仍然存在，继续递增
                    suffix = redisTemplate.opsForValue().increment(key, 1);
                    return baseName + "_" + suffix;
                }

            } finally {
                redisTemplate.delete(lockKey);
            }
        } else {
            // 获取锁失败，等待一段时间后重试
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return getUserNameMaxNumber(baseName);
        }
    }

    public long getMaxSuffixFromDBJia1(String baseName) {
        Integer max = cscpUserRepository.getMaxSuffixByBaseName(baseName);
        if (max == null) {
            return 1L;
        } else {
            return 1L + max;
        }
    }

    private String tsfh(String userName) {
        if (userName != null) {
            return userName.replaceAll("（", "").replaceAll("）", "")
                    .replaceAll("\\(", "").replaceAll("\\)", "")
                    .replaceAll(" ", "");
        }
        return userName;
    }

    /**
     * 判断是不是部门
     *
     * @param orgId
     * @return
     */
    private boolean checkDepartment(Long orgId) {
        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(orgId);
        if (!DEPARTMENT_TYPE.equals(cscpOrgDTO.getType())) {
            return false;
        }
        return true;
    }

    @Override
    public boolean checkUserStatus(Long userId) {
        CscpUser cscpUser = cscpUserRepository.selectById(userId);
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException("用户不存在");
        }
        if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
            return false;
        }
        return true;
    }

    /**
     * 判断用户表排序是否存在
     *
     * @param sort
     * @return
     */
    @Override
    public boolean checkUserSortExist(Long userId, Integer sort) {
        // 排除排序为9999的记录
        if (sort >= 9999) {
            return false;
        }
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper();
        // 排除自己
        if (Objects.nonNull(userId)) {
            queryWrapper.ne(CscpUser::getId, userId);
        }
        queryWrapper.eq(CscpUser::getTenantId, tenantId);
        queryWrapper.eq(CscpUser::getOrderBy, sort);
        int count = cscpUserRepository.selectCount(queryWrapper);
        return count > 0;
    }

    /**
     * 修改用户表排序: 修改用户表排序，等于该排序的该租户下的所有用户表的排序号全部 +1
     *
     * @param cscpUserDTO
     * @return
     */
    @Override
    public boolean updateUserSort(CscpUserDTO cscpUserDTO) {

        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();

        if (Objects.isNull(cscpUserDTO.getOrderBy())) {
            return true;
        }

        LambdaQueryWrapper<CscpUserOrg> userOrgLqw = Wrappers.lambdaQuery();
        userOrgLqw.eq(CscpUserOrg::getUserId, cscpUserDTO.getId());
        List<CscpUserOrg> userOrgs = cscpUserOrgService.selectListNoAdd(userOrgLqw);
        if (CollectionUtils.isEmpty(userOrgs)) {
            return true;
        }
        List<Long> userOrgIds = userOrgs.stream().map(CscpUserOrg::getUserId).collect(Collectors.toList());
        userOrgLqw.clear();
        userOrgLqw.in(CscpUserOrg::getOrgId, userOrgIds);
        userOrgLqw.ne(CscpUserOrg::getUserId, cscpUserDTO.getId());
        List<CscpUserOrg> userOrgList = cscpUserOrgService.selectListNoAdd(userOrgLqw);
        if (CollectionUtils.isEmpty(userOrgList)) {
            return true;
        }
        List<Long> ids = userOrgList.stream().map(CscpUserOrg::getUserId).collect(Collectors.toList());
        LambdaQueryWrapper<CscpUser> userLqw = Wrappers.lambdaQuery();
        userLqw.in(CscpUser::getId, ids);
        userLqw.orderByAsc(CscpUser::getOrderBy);
        List<CscpUser> userList = cscpUserRepository.selectListNoAdd(userLqw);
        if (CollectionUtils.isEmpty(userList)) {
            return true;
        }
        for (CscpUser cscpUser : userList) {
            Integer newOrderBy = cscpUser.getOrderBy() + 1;
            LambdaUpdateWrapper<CscpUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CscpUser::getOrderBy, newOrderBy);
            updateWrapper.eq(CscpUser::getId, cscpUser.getId());
            cscpUserRepository.update(null, updateWrapper);
        }
//        // 如果用户组织机构中间表的排序号存在
//        if (checkUserSortExist(cscpUserDTO.getId(), cscpUserDTO.getOrderBy())) {
//            //则大于中间表的排序号等于的该排序号的所有排序 + 1
//            LambdaUpdateWrapper<CscpUser> lcuo = new LambdaUpdateWrapper<>();
//            // 排除自己
//            if (Objects.nonNull(cscpUserDTO.getId())) {
//                lcuo.ne(CscpUser::getId, cscpUserDTO.getId());
//            }
//            lcuo.eq(CscpUser::getTenantId, tenantId);
//            lcuo.ge(CscpUser::getOrderBy, cscpUserDTO.getOrderBy());
//            lcuo.lt(CscpUser::getOrderBy, 9999);
//            lcuo.set(CscpUser::getOrderBy, cscpUserDTO.getOrderBy() + 1);
//            cscpUserRepository.update(null, lcuo);
//        }
        return true;
    }

    /**
     * 查询租户下所有用户
     *
     * @param cscpUserDTO
     * @return
     */
    @Override
    public List<CscpUserDTO> selectUserByTenantId(CscpUserDTO cscpUserDTO) {
        if (Objects.isNull(cscpUserDTO.getTenantId())) {
            return new ArrayList<>();
        }
        if (StringUtils.isNotBlank(cscpUserDTO.getRealName())) {
            cscpUserDTO.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(cscpUserDTO.getRealName()) : this.division(cscpUserDTO.getRealName()));
        }
        List<CscpUserDTO> cscpUserDTOList = cscpUserRepository.selectUserByTenantId(cscpUserDTO).stream().map(i -> {
            CscpOrgDTO orgDTO = cscpOrgService.findOne(i.getCompanyId());
            if (Objects.nonNull(orgDTO)) {
                i.setCompanyName(orgDTO.getOrgName());
            }
            return i;
        }).collect(Collectors.toList());
        return cscpUserDTOList;
    }

    /**
     * 查询指定租户下的用户
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<CscpUserDTO> selectTenantUsers(Long tenantId) {
        List<CscpUserDTO> cscpUsers = cscpUserRepository.selectTenantUsers(tenantId);
        return cscpUsers;
    }


    /**
     * 条件查询用户
     *
     * @param cscpUserDTO
     * @return
     */
    @Override
    public List<CscpUserDTO> selectUserList(CscpUserDTO cscpUserDTO) {
        List<CscpUserDTO> cscpUserDTOList = cscpUserRepository.selectUserByTenantId(cscpUserDTO).stream().map(i -> {
            CscpOrgDTO orgDTO = cscpOrgService.findOne(i.getCompanyId());
            if (Objects.nonNull(orgDTO)) {
                i.setCompanyName(orgDTO.getOrgName());
            }
            return i;
        }).collect(Collectors.toList());
        return cscpUserDTOList;
    }

    @Override
    public List<CscpUserExportDTO> selectUserExport(CscpUserDTO cscpUserDTO) {
        return cscpUserRepository.selectUserExport(cscpUserDTO);
    }

    /**
     * 获取本单位未登录的用户
     *
     * @param singInUserIdList
     * @return
     */
    @Override
    public List<CscpUserNumberDTO> notSingInCompanyUser(List<Long> singInUserIdList) {
        return cscpUserRepository.notSingInUser(singInUserIdList);
    }

    /**
     * 获取租户登录人数
     */
    @Override
    public CscpStatisticsSignInTenantDTO statisticsSignInTenantPeople() {
        if (SecurityUtils.isSystemName()) {
            return null;
        }

        // 创建一个数值格式化对象(求占比)
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);


        List<CscpTenantNumberDTO> onLineTenantNumberList = new LinkedList<>();
        List<CscpTenantNumberDTO> notOnLineTenantNumberList = new LinkedList<>();

        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();

        //获取该租户下的所有单位在线人数
        //查询当前租户下面的所有单位
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpOrg>()
                        .eq(CscpOrg::getTenantId, currentCscpUserDetail.getTenantId())
                        .eq(CscpOrg::getType, 2)
                        .orderByAsc(CscpOrg::getOrderBy)
        );

        for (CscpOrg cscpOrg : orgList) {
            //获取单位下的人数
            int notOnlineSize = cscpUserOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpUserOrg>().select(CscpUserOrg::getId).eq(CscpUserOrg::getCompanyId, cscpOrg.getId())).size();
            //从Redis中取出在线人数
            Set<Object> objects = redisUtil.sGet(new StringBuffer(RedisKeyConstant.ONLINE_COMPANY).append(":").append(cscpOrg.getId()).toString());
            Integer onLineSize = objects.stream().map(i -> ((CscpUserNumberDTO) i))
                    .collect(Collectors.groupingBy(CscpUserNumberDTO::getUserId)).keySet().size();
            //构建对象
            CscpTenantNumberDTO cscpTenantNumberDTO = new CscpTenantNumberDTO();
            //机构或者单位名称
            cscpTenantNumberDTO.setOrgName(cscpOrg.getOrgName());
            //实际在线人数
            cscpTenantNumberDTO.setActualOnLineNumber(onLineSize);
            //应在线人数
            cscpTenantNumberDTO.setAnswerOnLineNumber(notOnlineSize);
            //占比
            cscpTenantNumberDTO.setOccupy(numberFormat.format((float) onLineSize / (float) notOnlineSize * 100) + "%");
            //区分在线或者不在线
            if (onLineSize != 0) {
                onLineTenantNumberList.add(cscpTenantNumberDTO);
            } else {
                notOnLineTenantNumberList.add(cscpTenantNumberDTO);
            }

        }


        //获取平级中租户的在线人数
        Long tenantId = tSysTenantMapper.selectOne(new LambdaQueryWrapper<TSysTenant>().select(TSysTenant::getId).eq(TSysTenant::getId, currentCscpUserDetail.getTenantId())).getId();
        List<TSysTenant> tSysTenantList = tSysTenantMapper.selectList(new LambdaQueryWrapper<TSysTenant>().eq(TSysTenant::getParentId, tenantId));
        for (TSysTenant tSysTenant : tSysTenantList) {
            //获取租户下面的所有单位
            List<CscpOrg> companyList = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getId).eq(CscpOrg::getType, 2).eq(CscpOrg::getTenantId, tSysTenant.getId()));
            //获取单位下面所有人的数量
            int notOnlineSize = 0;
            if (!companyList.isEmpty()) {
                notOnlineSize = cscpUserOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpUserOrg>().select(CscpUserOrg::getId).in(CscpUserOrg::getCompanyId, companyList.stream().map(i -> i.getId()).collect(Collectors.toList()))).size();
            }

            //获取当前租户下所有单位的在线总人数
            long onLineSize = 0;
            for (CscpOrg cscpOrg : companyList) {
//                onLineSize += redisUtil.sGetSetSize(new StringBuffer(RedisKeyConstant.ONLINE_COMPANY).append(":").append(String.valueOf(cscpOrg.getId())).toString());
                Set<Object> c2 = redisUtil.sGet(new StringBuffer(RedisKeyConstant.ONLINE_COMPANY).append(":").append(cscpOrg.getId()).toString());
                onLineSize += c2.stream().map(i -> ((CscpUserNumberDTO) i))
                        .collect(Collectors.groupingBy(CscpUserNumberDTO::getUserId)).keySet().size();
            }

            //组装对象
            CscpTenantNumberDTO cscpTenantNumberDTO = new CscpTenantNumberDTO();
            //租户名称
            cscpTenantNumberDTO.setOrgName(tSysTenant.getTenantName());
            //实际在线人数
            cscpTenantNumberDTO.setActualOnLineNumber(onLineSize);
            //应在线人数
            cscpTenantNumberDTO.setAnswerOnLineNumber(notOnlineSize);
            //占比
            cscpTenantNumberDTO.setOccupy(numberFormat.format((float) onLineSize / (float) notOnlineSize * 100) + "%");
            //区分在线或者不在线
            if (onLineSize != 0) {
                onLineTenantNumberList.add(cscpTenantNumberDTO);
            } else {
                notOnLineTenantNumberList.add(cscpTenantNumberDTO);
            }

        }

        CscpStatisticsSignInTenantDTO statisticsSignInTenant = new CscpStatisticsSignInTenantDTO();
        statisticsSignInTenant.setOnLineList(onLineTenantNumberList);
        statisticsSignInTenant.setOnLineCount(onLineTenantNumberList.size());
        statisticsSignInTenant.setNotOnLineList(notOnLineTenantNumberList);
        statisticsSignInTenant.setNotOnLineCount(notOnLineTenantNumberList.size());
        return statisticsSignInTenant;
    }

    @Override
    public NumberOfPeopleOnlineDTO numberOfPeopleOnline() {
        NumberOfPeopleOnlineDTO numberOfPeopleOnline = new NumberOfPeopleOnlineDTO();
        //如果是管理员直接放回
        if (SecurityUtils.isSystemName()) {
            return numberOfPeopleOnline;
        }

        //本单位在线人数
        Set<Object> c1 = redisUtil.sGet(
                new StringBuffer(RedisKeyConstant.ONLINE_COMPANY).append(":").append(SecurityUtils.getCurrentCscpUserDetail().getCompanyId()).toString());
        Integer companyCount = c1.stream().map(i -> ((CscpUserNumberDTO) i))
                .collect(Collectors.groupingBy(CscpUserNumberDTO::getUserId)).keySet().size();

        //本租户下的人数
        List<TSysTenant> tSysTenantList = new LinkedList<>();
        TSysTenant tSysTenant = null;
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        if (com.ctsi.hndx.utils.StringUtils.isNotNull(tenantId)) {
            //获取本租户
            tSysTenant = tSysTenantMapper.selectOne(new LambdaQueryWrapper<TSysTenant>().eq(TSysTenant::getId, tenantId));
            //获取平级中租户的在线人数
            tSysTenantList = tSysTenantMapper.selectList(new LambdaQueryWrapper<TSysTenant>().eq(TSysTenant::getParentId, tSysTenant.getId()));
            tSysTenantList.add(tSysTenant);
        }

        long onLineSize = 0;
        for (TSysTenant sysTenantList : tSysTenantList) {
            //获取租户下面的所有单位
            List<CscpOrg> companyList = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getId).eq(CscpOrg::getType, 2).eq(CscpOrg::getTenantId, sysTenantList.getId()));
            for (CscpOrg cscpOrg : companyList) {
                Set<Object> c2 = redisUtil.sGet(new StringBuffer(RedisKeyConstant.ONLINE_COMPANY).append(":").append(cscpOrg.getId()).toString());
                onLineSize += c2.stream().map(i -> ((CscpUserNumberDTO) i))
                        .collect(Collectors.groupingBy(CscpUserNumberDTO::getUserId)).keySet().size();
            }
        }


        numberOfPeopleOnline.setTenantName(tSysTenant.getTenantName());
        numberOfPeopleOnline.setCompanyOnlineCount(companyCount);
        numberOfPeopleOnline.setTenantOnlineCount(onLineSize);

        return numberOfPeopleOnline;
    }

    /**
     * 根据多个用户id获取用户的手机号码
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> getCscpUserMoileAll(List<Long> userId) {
        List<String> moileList = cscpUserRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUser>()
                        .select(CscpUser::getMobile)
                        .in(CscpUser::getId, userId)
                        .eq(CscpUser::getStatus, UserConstant.USER_ACTIVE_STATUS)
        ).stream().map(i -> i.getMobile()).collect(Collectors.toList());
        return moileList;
    }

    /**
     * 查询用户基本信息
     *
     * @param cscpBaseUserDTO
     * @return
     */
    @Override
    public List<CscpBaseUserDTO> selectBaseUserInfo(CscpBaseUserDTO cscpBaseUserDTO) {
        if (StringUtils.isNotBlank(cscpBaseUserDTO.getUserName())) {
            cscpBaseUserDTO.setUserName(this.division(cscpBaseUserDTO.getUserName()));
        }
        List<CscpBaseUserDTO> baseUserDTOList = cscpUserRepository.selectBaseUserInfo(cscpBaseUserDTO);
        return baseUserDTOList;
    }

    /**
     * 查询用户基本组织机构属性
     *
     * @param cscpBaseUserDTO
     * @return
     */
    @Override
    public List<CscpBaseUserDTO> queryBaseUserInfo(CscpBaseUserDTO cscpBaseUserDTO) {
        if (StringUtils.isNotBlank(cscpBaseUserDTO.getUserName())) {
            cscpBaseUserDTO.setUserName(this.division(cscpBaseUserDTO.getUserName()));
        }
        List<CscpBaseUserDTO> baseUserDTOList = cscpUserRepository.queryBaseUserInfo(cscpBaseUserDTO);
        baseUserDTOList.stream().forEach(i -> {
            i.setCompanyName(cscpOrgService.findOne(i.getCompanyId()).getOrgName());
        });
        return baseUserDTOList;
    }

    /**
     * 删除租户下的所有用户
     *
     * @param tenantId
     * @return
     */
    @Override
    public Boolean deleteUserByTenantId(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new BusinessException("租户ID不允许为空");
        }
        LambdaQueryWrapper<CscpUser> userLambdaQueryWrapper = new LambdaQueryWrapper();
        userLambdaQueryWrapper.eq(CscpUser::getTenantId, tenantId);
        cscpUserRepository.delete(userLambdaQueryWrapper);
        return true;
    }

    /**
     * 查询本单位除指定部门外的所有用户信息
     *
     * @param cscpUserDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CscpUserDTO> queryOtherDepartmentUsers(CscpUserDTO cscpUserDTO, BasePageForm basePageForm) {
        // 查询要排除部门下的所有用户ID
        CscpUserOrgDTO cscpUserOrgDTO = new CscpUserOrgDTO();
        cscpUserOrgDTO.setOrgId(cscpUserDTO.getExcludeDepartmentId());
        List<Long> exUserIdList = cscpUserOrgService.selectUserOrgList(cscpUserOrgDTO).stream()
                .map(i -> i.getUserId()).collect(Collectors.toList());

        QueryCscpUserNotInListDTO dto = QueryCscpUserNotInListDTO.builder()
                .userIdList(exUserIdList)
                .realName(this.division(cscpUserDTO.getRealName()))
                .tenantId(SecurityUtils.getCurrentCscpUserDetail().getTenantId())
                .companyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId())
                .build();
        IPage<CscpUserDTO> userIPage = cscpUserRepository.queryCscpUserNotInListDTO(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), dto);
        return new PageResult<>(userIPage.getRecords(), userIPage.getTotal(), userIPage.getTotal());
    }

    /**
     * 获取本单位在线人数和未在线人数
     *
     * @return
     */
    @Override
    public StatisticsSignInPeopleDTO statisticsSignInCompanyPeople() {
        if (SecurityUtils.isTenantName()) {
            throw new BusinessException("该接口只允许单位管理员和普通用户访问");
        }

        StatisticsSignInPeopleDTO ssipd = new StatisticsSignInPeopleDTO();
        //本单位登录的用户
        Set<Object> onLineCountList = redisUtil.sGet(RedisKeyConstant.ONLINE_COMPANY + ":" + SecurityUtils.getCurrentCscpUserDetail().getCompanyId());

        //计算当前用户登录了多久
        for (Object obj : onLineCountList) {
            if (obj instanceof CscpUserNumberDTO) {
                //登录时长
                ((CscpUserNumberDTO) obj).setLoginDuration(LocalDateTimeUtils.getLoginDuration(((CscpUserNumberDTO) obj).getLoginTime()));
            }
        }

        Map<Long, CscpUserNumberDTO> userNumberMap = onLineCountList.stream().map(i -> ((CscpUserNumberDTO) i))
                .collect(Collectors.groupingBy(CscpUserNumberDTO::getUserId, Collectors.collectingAndThen(Collectors.toList(), list -> {
                    CscpUserNumberDTO c1 = list.get(0);
                    if (list.size() == 2) {
                        CscpUserNumberDTO c2 = list.get(1);
                        c1.setLoginMode(new StringBuffer(c1.getLoginMode()).append("/").append(c2.getLoginMode()).toString());
                        c1.setLoginDuration(new StringBuffer(c1.getLoginDuration()).append("/").append(c2.getLoginDuration()).toString());
                        return c1;
                    }
                    return c1;
                })));

        onLineCountList = userNumberMap.values().stream().collect(Collectors.toSet());


        //本单位未登录的用户
        List<Long> singInUserIdList = onLineCountList.stream().map(i -> ((CscpUserNumberDTO) i).getUserId()).collect(Collectors.toList());
        List<CscpUserNumberDTO> notSingInUser = this.notSingInCompanyUser(singInUserIdList);
        //排序 先根据部门排序在根据用户排序
        notSingInUser.stream().forEach(i -> i.setUserName(i.getUserName()));

//        //未登录用户的出差统计
//        Map<String, Object> pushMap = new HashMap(2);
//        List<Long> collect = notSingInUser.stream().map(i -> i.getUserId()).collect(Collectors.toList());
//        //去除url中的[
//        String regEx = "[\n\\[\\]]";
//        //日期转换
//        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        pushMap.put("userIds", collect.toString().replaceAll(regEx, " "));
//        pushMap.put("localDate", LocalDate.now());
//        String bizLeave = SysConstant.BIZ_LEAVE_API.replace(SysConstant.replaceValue, "bizLeave");
//        //请求获取请假出差统计
//        ResultVO resultVO = RestTemplateRequestJWT.get(new StringBuffer(SpringUtil.getLocalUrlPort()).append(bizLeave).toString(), ResultVO.class, pushMap);
//        //将放回的数据进行JSON处理
//        String resultJson = JSON.toJSONString(resultVO);
//        JSONObject jsonObject = JSON.parseObject(resultJson);
//        JSONArray resultData = JSON.parseArray(jsonObject.getString("resultData"));
//        //遍历返回的用户请假出差统计
//        for (int i = 0; i < resultData.size(); i++) {
//            JSONObject jsonObject1 = resultData.getJSONObject(i);
//            notSingInUser.stream().forEach(y -> {
//                if (jsonObject1.getLong("userId").longValue() == y.getUserId()) {
//                    //计算请假开会出差多少人
//                    Integer type = jsonObject1.getInteger("type");
//                    if (SysConstant.TRAVEL.equals(type)) {
//                        ssipd.setTravelCount(ssipd.getTravelCount() + 1);
//                    } else if (SysConstant.LEAVE.equals(type)) {
//                        ssipd.setLeaveCount(ssipd.getLeaveCount() + 1);
//                    } else if (SysConstant.ATTEND_A_MEETING.equals(type)) {
//                        ssipd.setAttendAMeeting(ssipd.getAttendAMeeting() + 1);
//                    }
//                    y.setUserState(type);
//                }
//            });
//        }

        //返回
        //没有登录的用户信息
        ssipd.setNotOnLineUserList(notSingInUser.stream().map(i -> {
            if (Objects.isNull(i.getUserOrderBy())) {
                i.setUserOrderBy(Integer.MAX_VALUE);
            }
            return i;
            //先按照部门排序在按照用户排序
        }).sorted(Comparator.comparing(CscpUserNumberDTO::getOrgOrderBy).thenComparing(CscpUserNumberDTO::getUserOrderBy)).collect(Collectors.toList()));
        //登录的用户信息
        for (Object o : onLineCountList) {
            //补充getOrgOrderBy或getUserOrderBy 为null的场景
            CscpUserNumberDTO cscpUserNumberDTO = (CscpUserNumberDTO) o;
            if (cscpUserNumberDTO.getUserOrderBy() == null && cscpUserNumberDTO.getOrgOrderBy() != null) {
                cscpUserNumberDTO.setUserOrderBy(cscpUserNumberDTO.getOrgOrderBy());
            } else if (cscpUserNumberDTO.getOrgOrderBy() == null && cscpUserNumberDTO.getUserOrderBy() != null) {
                cscpUserNumberDTO.setOrgOrderBy(cscpUserNumberDTO.getUserOrderBy());
            } else if (cscpUserNumberDTO.getOrgOrderBy() == null && cscpUserNumberDTO.getUserOrderBy() == null) {
                cscpUserNumberDTO.setUserOrderBy(999);
                cscpUserNumberDTO.setOrgOrderBy(999);
            }
        }

        List<CscpUserNumberDTO> collect = onLineCountList.stream().map(i -> ((CscpUserNumberDTO) i)).sorted(Comparator.comparing(CscpUserNumberDTO::getOrgOrderBy).thenComparing(CscpUserNumberDTO::getUserOrderBy)).collect(Collectors.toList());
        ssipd.setOnLineUserList(collect);
        //没有登录用户的数量
        ssipd.setNotOnLineCount(notSingInUser.size());
        //登录用户的数量
        ssipd.setOnLineCount(onLineCountList.size());

        return ssipd;
    }

    /**
     * 批量查询用户基本信息
     *
     * @param userIdList
     * @param isDisplay
     * @return
     */
    @Override
    public List<CscpBaseUserDTO> listQueryBaseUserDTO(List<Long> userIdList, boolean isDisplay) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<CscpBaseUserDTO> cscpUserDTOList = cscpUserRepository.selectList(
                        new LambdaQueryWrapper<CscpUser>()
                                .in(CscpUser::getId, userIdList)
                                .eq(isDisplay, CscpUser::getDisplay, UserConstant.USER_ACTIVE_STATUS)
                                .eq(CscpUser::getStatus, UserConstant.USER_ACTIVE_STATUS))
                .stream().map(i -> {
                    CscpBaseUserDTO cscpBaseUserDTO = new CscpBaseUserDTO();
                    cscpBaseUserDTO.setUserId(i.getId());
                    cscpBaseUserDTO.setUserName(i.getRealName());
                    return cscpBaseUserDTO;
                }).collect(Collectors.toList());
        return cscpUserDTOList;
    }

    /**
     * 解密
     *
     * @param ciphertext 密码的密文
     * @return
     */

    private String decryptPassword(String ciphertext) {
        String password = ciphertext;
        try {
            // TODO 对用户口令再进行一次SM4解密
            if (westoneEncryptService.isCipherMachine()) {
                password = westoneEncryptService.decryptBySM4ECB_WithKeyProtectedBySM2(password);
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(rsaPrikey)) {
                password = new String(
                        RSAUtil.decryptPri(Base64.getDecoder().decode(ciphertext), Base64.getDecoder().decode(rsaPrikey)), "UTF-8");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return password;
    }

    public static class UpdatePasswordResult {
        boolean result;
        long userId;
        String password;
        String userName;

        public UpdatePasswordResult(boolean result, long userId, String password, String userName) {
            this.result = result;
            this.userId = userId;
            this.password = password;
            this.userName = userName;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public boolean isResult() {
            return result;
        }

        public void setResult(boolean result) {
            this.result = result;
        }

        public long getUserId() {
            return userId;
        }

        public void setUserId(long userId) {
            this.userId = userId;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    //分割字符串加密
    public String division(String realName) {
        String realNames = "";
        if (StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames;
    }

    /**
     * 修改用户表排序: 修改用户表排序，等于该排序的该租户下的所有用户表的排序号全部 +1
     *
     * @param cscpUserDTO
     * @return
     */
    @Override
    public boolean crmUpdateUserSort(CscpUserDTO cscpUserDTO, TSysTenant tSysTenant) {

        Long tenantId = tSysTenant.getId();
        if (Objects.isNull(cscpUserDTO.getOrderBy())) {
            return true;
        }
        // 如果用户组织机构中间表的排序号存在
        if (crmCheckUserSortExist(cscpUserDTO.getId(), cscpUserDTO.getOrderBy(), tenantId)) {
            //则大于中间表的排序号等于的该排序号的所有排序 + 1
            LambdaQueryWrapper<CscpUser> lcuo = new LambdaQueryWrapper();
            // 排除自己
            if (Objects.nonNull(cscpUserDTO.getId())) {
                lcuo.ne(CscpUser::getId, cscpUserDTO.getId());
            }
            lcuo.eq(CscpUser::getTenantId, tenantId);
            lcuo.ge(CscpUser::getOrderBy, cscpUserDTO.getOrderBy());
            lcuo.lt(CscpUser::getOrderBy, 9999);
            cscpUserRepository.selectList(lcuo).stream().map(i -> {
                i.setOrderBy(i.getOrderBy() + 1);
                return cscpUserRepository.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }

    /**
     * CRM判断用户表排序是否存在
     *
     * @param sort
     * @return
     */
    @Override
    public boolean crmCheckUserSortExist(Long userId, Integer sort, Long tenantId) {
        // 排除排序为9999的记录
        if (sort >= 9999) {
            return false;
        }
//        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        LambdaQueryWrapper<CscpUser> queryWrapper = new LambdaQueryWrapper();
        // 排除自己
        if (Objects.nonNull(userId)) {
            queryWrapper.ne(CscpUser::getId, userId);
        }
        queryWrapper.eq(CscpUser::getTenantId, tenantId);
        queryWrapper.eq(CscpUser::getOrderBy, sort);
        int count = cscpUserRepository.selectCount(queryWrapper);
        return count > 0;
    }

    @Override
    @Transactional
    public CscpUserDTO crmInsert(CscpUserDTO cscpUserDTO, TSysTenant tSysTenant, Long orgId) {
        //如果用户未设置密码，则使用默认密码
        String defaultPassword = cscpUserDTO.getPassword();
        if (org.apache.commons.lang.StringUtils.isBlank(defaultPassword)) {
            defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        }
        cscpUserDTO.setPassword(passwordEncoder.encode(defaultPassword));
        if (!SysConstant.DELAULT_MOBILE.equals(cscpUserDTO.getMobile())) {
            // 先判断手机号码或者登录名是否重复
            boolean existByMobile = existByMobile(cscpUserDTO.getMobile());
            if (existByMobile) {
                throw new BusinessException(ResultCode.USER_MOBILE_EXISTED);
            }
        }

        boolean existByUsername = existByUsername(cscpUserDTO.getLoginName());
        if (existByUsername) {
            throw new BusinessException(ResultCode.USER_HAS_EXISTED);
        }
        CscpUser cscpUser = new CscpUser();
        //名字
        RealNameSplitType realNameSplitType = StringSplitUtils.realNameSplit(cscpUserDTO.getRealName());
        // //姓
        // cscpUser.setRealNameStart(realNameSplitType.getRealNameStart());
        // //名
        // cscpUser.setRealNameEnd(realNameSplitType.getRealNameEnd());
        cscpUser.setRealNameStart(cscpUserDTO.getRealName());
        cscpUser.setRealNameEnd(cscpUserDTO.getRealName());

        cscpUser.setMobileStart(cscpUserDTO.getMobile());
        cscpUser.setMobileMiddle(cscpUserDTO.getMobile());
        cscpUser.setMobileMiddle(cscpUserDTO.getMobile());
        cscpUser.setMobileEnd(cscpUserDTO.getMobile());
        cscpUser.setMobile(cscpUserDTO.getMobile()); // 重新加密设值

        cscpUser.setTenantId(tSysTenant.getId());
        cscpUser.setCreateBy(tSysTenant.getTenantLoginId());
        // //电话开头
        // MobileSplitType mobileSplitType = StringSplitUtils.mobileSplit(cscpUserDTO.getMobile());
        // cscpUser.setMobileStart(mobileSplitType.getMobileStart());
        // //电话中间
        // cscpUser.setMobileMiddle(mobileSplitType.getMobileMiddle());
        // //电话结尾
        // cscpUser.setMobileEnd(mobileSplitType.getMobileEnd());

        BeanUtils.copyProperties(cscpUserDTO, cscpUser);

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }

        List<CscpOrgNameIdListDTO> orgIdList = cscpUserDTO.getOrgNameList();
        if (CollectionUtils.isNotEmpty(orgIdList) && orgIdList.size() > 1 && Objects.isNull(cscpUserDTO.getDefaultDepart())) {
            throw new BusinessException(ResultCode.USER_NO_DEFAULT_LOGIN_ERROR);
        }
        /*if (cscpUser.getOrderBy() == null) {
            cscpUser.setOrderBy(this.getUserSorted(cscpUserDTO));
        }*/

        // 修改用户表排序号,大于该排序的该租户下的所有用户表的排序号全部 + 1
        this.crmUpdateUserSort(cscpUserDTO, tSysTenant);

        // 修改组织机构排序，大于该排序的所有组织机构的排序号全部 + 1
        cscpUserOrgService.updateUserOrgSort(cscpUserDTO);

        // 设置是否显示为true, 是否统计默认为false
        if (Objects.isNull(cscpUser.getDisplay())) {
            cscpUser.setDisplay(true);
        }
        if (Objects.isNull(cscpUser.getStatistics())) {
            cscpUser.setStatistics(false);
        }
        // 设置默认办公电话
        if (StringUtils.isBlank(cscpUser.getOfficePhone())) {
            cscpUser.setOfficePhone(SysConstant.DELAULT_MOBILE);
        }

        // 保存用户信息
        cscpUser.setNameHandle();
        cscpUserDTO.setNameHandle();
        cscpUserRepository.insert(cscpUser);

        // 保存用户角色
        cscpUserRoleService.crmSaveUserRoles(cscpUser.getId(), cscpUserDTO.getRoleIds(), tSysTenant, orgId);

        // 保存App用户角色
        List<Long> appRoleIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cscpUserDTO.getAppRoleIds())) {
            appRoleIdList = cscpUserDTO.getAppRoleIds();
        }
        if (!appRoleIdList.contains(Long.parseLong(SystemRole.APP_GENERAL_ROLE.getId()))) {
            appRoleIdList.add(Long.parseLong(SystemRole.APP_GENERAL_ROLE.getId()));
        }
        cscpUserDTO.setAppRoleIds(appRoleIdList);
        itAppUserRoleService.insertRolesToUser(appRoleIdList, cscpUser.getId());

        //保存用户机构
        if (CollectionUtils.isEmpty(cscpUserDTO.getOrgIdList())
                && CollectionUtils.isEmpty(cscpUserDTO.getOrgNameList())) {
            log.error("机构ID不能为空！");
            throw new BusinessException("机构ID不能为空！");
        }
        cscpUserOrgService.crmSaveUserOrgRel(cscpUser.getId(), cscpUserDTO);


        //===================同步通讯录开始=============================
        if (Objects.isNull(cscpUserDTO.getAddType())) {
            CreateTAddressBookDTO build = CreateTAddressBookDTO.builder()
                    .defaultPhone(cscpUserDTO.getMobile())
                    .realName(cscpUserDTO.getRealName())
                    .sort(cscpUserDTO.getSort())
                    .whetherShow(cscpUserDTO.getWhetherShow())
                    .AddType(1)
                    .telephone(cscpUserDTO.getOfficePhone())
                    .secretaryName(cscpUserDTO.getSecretaryName())
                    .secretaryPhone(cscpUserDTO.getSecretaryPhone())
//                    .jobTitle(cscpUserDTO.getPost())
                    .userId(cscpUser.getId()).build();
            if (cscpUserDTO.getOrgNameList() != null && cscpUserDTO.getOrgNameList().size() > 0) {
                build.setJobTitle(cscpUserDTO.getOrgNameList().get(0).getPost());
            }
            try {
                tAddressBookService.create(build);
            } catch (Exception e) {
                log.error("CscpUserDataChangeMonitorService handleFullTextInsert error : {}", e.getMessage());
            }
        }
        //===================同步通讯录结束=============================

        // ===================同步用户到统一身份认证系统===================
        asyncPushUserToWestoneUas(cscpUser);
        // ===================同步用户到统一身份认证系统===================

        BeanUtils.copyProperties(cscpUser, cscpUserDTO);
        return cscpUserDTO;
    }

    /**
     * 发送短信验证码
     *
     * @param smsVerificationCode
     * @return
     */
    @Override
    public Boolean sendSmsVerificationCode(SmsVerificationCodeDTO smsVerificationCode) {
        if (Objects.isNull(smsVerificationCode) || !com.ctsi.hndx.utils.StringUtils.isNotEmpty(smsVerificationCode.getPhone())) {
            throw new BusinessException("手机号不能为空!");
        }

        //判断这个手机号是否存在库里
        boolean existByMobile = this.existByMobile(smsVerificationCode.getPhone());
        if (!existByMobile) {
            throw new BusinessException(ResultCode.PHONE_NOT_EXIST);
        }

        //手机号码
        String phone = SysConfigConstant.SMS_CODE + smsVerificationCode.getPhone();

        //判断手机号是否已经发送过短信了，每个手机号只能60秒发一次
        if (!Objects.isNull(redisUtil.get(phone)) && redisUtil.getExpire(phone) > validTime - sendSmsTime) {
            throw new BusinessException("短信发送操作频繁!");
        }

        String redisCountKey = phone + "_COUNTS";
        if (redisUtil.hasKey(redisCountKey)) {
            if ((Integer) redisUtil.get(redisCountKey) > 50) {
                throw new BusinessException("今日已经发送达到50条!");
            } else {
                Integer count = 0;
                count = count + (Integer) redisUtil.get(redisCountKey) + 1;
                redisUtil.set(redisCountKey, count, getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);
            }
        } else {
            redisUtil.set(redisCountKey, 1, getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);
        }

        //生成验证码
        String code = String.valueOf((int) ((Math.random() * 9 + 1) * Math.pow(10, 5)));
        //发送短信
        try {
            SmsSendUtil.sendSms(smsVerificationCode.getPhone(), code, SmsSendEnum.USER_CAPTCHA);
        } catch (Throwable e) {
            throw new BusinessException("短信发送失败!");
        }

        //把手机号存到redis中,有效时间为validTime
        redisUtil.set(phone, code, validTime, TimeUnit.SECONDS);

        return true;
    }

    /**
     * 验证手机验证码是否正确
     *
     * @param smsVerificationCode
     * @return
     */
    @Override
    public Boolean checkVerificationCode(SmsVerificationCodeDTO smsVerificationCode) {
        if (Objects.isNull(smsVerificationCode)) {
            throw new BusinessException("请输入验证码");
        }

        //根据手机号取对应的验证码,并进行判断
        Object o = redisUtil.get(SysConfigConstant.SMS_CODE + smsVerificationCode.getPhone());
        if (String.valueOf(o).equals(smsVerificationCode.getCode())) {
            return false;
        }

        return true;
    }

    @Override
    public void updateAppVersionName(Long id, String versionName) {
        cscpUserRepository.updateAppVersionName(id, versionName);
    }

    public IPage<CscpUserDTO> getWorkUserList(Long groupId, String realName, BasePageForm basePageForm) {
        return cscpUserRepository.getWorkUserList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), groupId, realName);
    }

    @Override
    public PageResult<CscpUserDTO> selectCommCompanyUserList(Long id, String realName, List<Long> userIds, BasePageForm basePageForm) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(realName)) {
            realName = this.division(realName);
        }
        IPage<CscpUserDTO> cscpUserIPage =
                cscpUserRepository.pageQueryUserList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), id, realName, userIds);
        List<CscpUserDTO> data = cscpUserIPage.getRecords();
        return new PageResult<>(data, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }

    @Override
    public CscpUserDTO selectUserIsDisable(Long id) {
        return cscpUserRepository.selectUserIsDisable(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int fixDuplicateLoginNames() {
        // 获取所有重复的登录名和用户
        List<CscpUser> allUsers = cscpUserRepository.selectList(
                Wrappers.lambdaQuery(CscpUser.class)
                        .select(CscpUser::getLoginName, CscpUser::getId, CscpUser::getCreateTime)
                        .orderByAsc(CscpUser::getCreateTime)
        );

        Map<String, List<CscpUser>> loginNameToUsers = allUsers.stream()
                .collect(Collectors.groupingBy(CscpUser::getLoginName));

        List<CscpUser> usersToUpdateList = new ArrayList<>();

        for (Map.Entry<String, List<CscpUser>> entry : loginNameToUsers.entrySet()) {
            List<CscpUser> users = entry.getValue();
            if (users.size() > 1) {
                for (int i = 1; i < users.size(); i++) {
                    CscpUser user = users.get(i);
                    String newLoginName = generateNewLoginName(entry.getKey(), i, loginNameToUsers.keySet());
                    user.setLoginName(newLoginName);
                    usersToUpdateList.add(user);
                }
            }
        }

        if (usersToUpdateList.isEmpty()) {
            log.info("没有重复的登录名，不需要更新数据！");
            return 0;
        }

        // 批量更新
        boolean result = this.updateBatchById(usersToUpdateList);
        // 返回实际更新的条数
        return result ? usersToUpdateList.size() : 0;
    }

    @Override
    public int dealAdminForOrgs(Long id) {
        int count = 0;

        // 查询type=2的单位
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper<CscpOrg>()
                .eq(CscpOrg::getType, 2).eq(CscpOrg::getDeleted, 0);
        if (id != null) {
            lambdaQueryWrapper.eq(CscpOrg::getId, id);
        }
        lambdaQueryWrapper.select(CscpOrg::getId, CscpOrg::getOrgName, CscpOrg::getTenantId);
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(lambdaQueryWrapper);

        for (CscpOrg org : orgList) {
            try {
                Long orgId = org.getId();
                // 查询该单位下的用户机构关系
                List<CscpUserOrg> userOrgList = cscpUserOrgRepository.queryListCscpUserOrg(orgId);
                boolean hasAdmin = false;

                if (userOrgList != null && !userOrgList.isEmpty()) {
                    List<Long> userIds = userOrgList.stream()
                            .map(CscpUserOrg::getUserId)
                            .filter(Objects::nonNull).collect(Collectors.toList());

                    if (!userIds.isEmpty()) {
                        // 批量查询用户角色
                        List<CscpRoles> allRoles = cscpUserRoleRepository.queryRoleByUserIds(userIds);

                        Set<String> roleIds = allRoles.stream()
                                .map(cscpRoles -> cscpRoles.getId().toString())
                                .collect(Collectors.toSet());

                        if (roleIds.contains(SystemRole.COMPANY_ROLE.getId())) {
                            hasAdmin = true;
                        }
                    }
                }

                // 如果不存在admin，则调用saveUserOrgRel新增管理员
                if (!hasAdmin) {
                    CscpOrgDTO cscpOrgDTO = new CscpOrgDTO();
                    cscpOrgDTO.setId(orgId); // 设置orgId
                    cscpOrgDTO.setOrgName(org.getOrgName()); // 设置orgName
                    cscpOrgDTO.setTenantId(org.getTenantId());
                    companySaveStrategyService.saveCompanyAdmin(cscpOrgDTO);
                    count++;
                }
            } catch (Exception e) {
                log.error("新增单位管理员异常，单位ID: {}", org.getId(), e);
            }
        }

        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateUpdateSqlAndDownload(Long companyId, HttpServletResponse response) throws IOException {
        if (companyId == null) {
            companyId = 0L;
        }
        long startTime = System.currentTimeMillis();
        log.info("开始为机构 {} 生成用户身份证更新 SQL...", companyId);
        CscpOrg cscpOrg = cscpOrgRepository.selectById(companyId);
        if (cscpOrg == null) {
            throw new BusinessException("未找到单位id对应的机构");
        }

        if (StringUtils.isBlank(cscpOrg.getOrgCode())) {
            throw new BusinessException("机构编码不能为空，请检查机构编码是否正确");
        }

        List<Long> orgIdList = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>()
                        .select(CscpOrg::getId).likeRight(CscpOrg::getOrgCode, cscpOrg.getOrgCode()))
                .stream().map(i -> i.getId()).collect(Collectors.toList());

        log.info("成功查询到 {} 条目标机构的详细信息。", orgIdList.size());
        // 查询指定机构下的所有目标用户信息
        List<CscpUser> targetUsers = cscpUserRepository.selectUsersByOrgIdList(orgIdList);

        if (CollectionUtils.isEmpty(targetUsers)) {
            log.info("机构 {} 下没有用户需要处理或未能查询到用户详细信息。", companyId);
            writeInfoResponse(response, "机构 " + companyId + " 下没有用户需要处理或未能查询到用户详细信息。");
            return;
        }
        log.info("成功查询到 {} 条目标用户的详细信息。", targetUsers.size());

        targetUsers.forEach(user -> {
            // 解密 realName
            if (StrUtil.isNotBlank(user.getRealName())) {
                try {
                    String decodedRealName = new String(Base64.getDecoder().decode(user.getRealName()), StandardCharsets.UTF_8);
                    user.setRealName(decodedRealName);
                } catch (IllegalArgumentException e) {
                    log.error("用户 ID {} 的 realName Base64 解码失败: {}", user.getId(), user.getRealName(), e);
                    user.setRealName("");
                } catch (Exception e) {
                    log.error("用户 ID {} 的 realName 解码过程中发生未知错误: {}", user.getId(), user.getRealName(), e);
                    user.setRealName("");
                }
            }
            // 解密 mobile
            if (StrUtil.isNotBlank(user.getMobile())) {
                try {
                    String decodedMobile = new String(Base64.getDecoder().decode(user.getMobile()), StandardCharsets.UTF_8);
                    user.setMobile(decodedMobile);
                } catch (IllegalArgumentException e) {
                    log.error("用户 ID {} 的 mobile Base64 解码失败: {}", user.getId(), user.getMobile(), e);
                    user.setMobile("");
                } catch (Exception e) {
                    log.error("用户 ID {} 的 mobile 解码过程中发生未知错误: {}", user.getId(), user.getMobile(), e);
                    user.setMobile("");
                }
            }
        });

        // 使用 mobile -> CscpUser 的映射 (过滤掉 mobile 为空或 null 的用户)
        Map<String, CscpUser> userMapByMobile = targetUsers.stream()
                .filter(user -> StrUtil.isNotBlank(user.getMobile()))
                .collect(Collectors.toMap(CscpUser::getMobile, Function.identity(), (existing, replacement) -> {
                    log.warn("发现重复的 mobile: {}, userId1: {}, userId2: {}. 更新时将使用其中一个。",
                            existing.getMobile(), existing.getId(), replacement.getId());
                    return existing;
                }));

        // 使用 realName -> List<CscpUser> 的映射 (过滤掉 realName 为空或 null 的用户)
        Map<String, List<CscpUser>> userMapByRealName = targetUsers.stream()
                .filter(user -> StrUtil.isNotBlank(user.getRealName()))
                .collect(Collectors.groupingBy(CscpUser::getRealName));


        log.info("内存查找结构构建完成： userMapByMobile={}, userMapByNameStart={}",
                userMapByMobile.size(), userMapByRealName.size());

        // 分批次查询，每次10万条
        int batchSize = 100000;
        int totalRecords = bizHrsUserInfoMapper.selectCount(null); // 获取总记录数
        int totalBatches = (int) Math.ceil((double) totalRecords / batchSize);
        log.info("总记录数: {}, 分批次查询，批次大小: {}, 总批次: {}", totalRecords, batchSize, totalBatches);


        // 存储最终需要更新的 userId 和对应的 idCardNo
        Map<Long, String> updatesToApply = new HashMap<>();
        // 存储 strUserId 对应的计数
        Map<String, Long> strUserIdCountMap = new HashMap<>();

        // 1. 先统计所有 strUserId 的计数
        QueryWrapper<BizHrsUserInfo> countQueryWrapper = new QueryWrapper<>();
        countQueryWrapper.select("str_user_id")
                .isNotNull("str_user_id")
                .ne("str_user_id", "");
        List<BizHrsUserInfo> allUserIds = bizHrsUserInfoMapper.selectListNoAdd(countQueryWrapper);
        strUserIdCountMap = allUserIds.stream()
                .filter(bizUser -> StrUtil.isNotBlank(bizUser.getStrUserId()))
                .collect(Collectors.groupingBy(BizHrsUserInfo::getStrUserId, Collectors.counting()));
        log.info("统计 strUserId 计数完成，共有 {} 个唯一的 strUserId", strUserIdCountMap.size());

        // 2. 分批次查询和处理
        for (int batch = 0; batch < totalBatches; batch++) {
            log.info("开始处理第 {} 批次，共 {} 批次", batch + 1, totalBatches);

            QueryWrapper<BizHrsUserInfo> bizQueryWrapper = new QueryWrapper<>();
            bizQueryWrapper.select("id_card_no", "str_mobile", "str_user_id")
                    .last("LIMIT " + batchSize + " OFFSET " + (batch * batchSize));
            List<BizHrsUserInfo> bizHrsUserInfos = bizHrsUserInfoMapper.selectListNoAdd(bizQueryWrapper);

            if (CollectionUtils.isEmpty(bizHrsUserInfos)) {
                log.info("第 {} 批次没有数据需要处理。", batch + 1);
                continue;
            }
            log.info("第 {} 批次查询到 {} 条数据", batch + 1, bizHrsUserInfos.size());

            // 3. 在内存中匹配 BIZ_HRS_USER_INFO 和 CscpUser 数据
            long processedCount = 0;
            for (BizHrsUserInfo bizUser : bizHrsUserInfos) {
                processedCount++;
                String idCardNo = bizUser.getIdCardNo();
                String strMobile = bizUser.getStrMobile();
                String strUserId = bizUser.getStrUserId();

                if (StrUtil.isBlank(idCardNo)) {
                    continue;
                }
                idCardNo = idCardNo.trim();
                CscpUser targetUser = null;

                // 优先使用 Mobile 匹配
                if (StrUtil.isNotBlank(strMobile)) {
                    targetUser = userMapByMobile.get(strMobile);
                    if (targetUser != null) {
                        updatesToApply.put(targetUser.getId(), idCardNo);
                        continue;
                    }
                }

                // 如果 Mobile 未匹配到，或者 Mobile 为空，尝试使用 strUserId 匹配
                if (targetUser == null && StrUtil.isNotBlank(strUserId)) {
                    // 检查 strUserId 在 BizHrsUserInfo 表中是否唯一
                    Long count = strUserIdCountMap.get(strUserId);
                    if (count != null && count == 1) {
                        // 只有 strUserId 唯一时，才尝试匹配 realName
                        List<CscpUser> matchingUsers = userMapByRealName.get(strUserId);
                        // 确保 realName 完全相同，且在 CscpUser 表中也唯一
                        if (matchingUsers != null && matchingUsers.size() == 1) {
                            targetUser = matchingUsers.get(0);
                        }
                    }
                }

                // 如果找到了唯一的目标用户，记录待更新信息
                if (targetUser != null) {
                    updatesToApply.put(targetUser.getId(), idCardNo);
                }
                log.info("处理第 {} 批次记录: idCardNo={}, strMobile={}, strUserId={}, 目标用户 ID: {}",
                        batch + 1, idCardNo, strMobile, strUserId, targetUser != null ? targetUser.getId() : "未找到");
            }
        }

        log.info("BIZ_HRS_USER_INFO 表处理完成. 找到 {} 条需要更新的记录。", updatesToApply.size());

        // 4. 最后检查 updatesToApply 中是否有重复的 strUserId 导致的冲突
        Map<String, List<Long>> idCardNoToUserIds = new HashMap<>();
        for (Map.Entry<Long, String> entry : updatesToApply.entrySet()) {
            Long userId = entry.getKey();
            String idCardNo = entry.getValue();
            idCardNoToUserIds.computeIfAbsent(idCardNo, k -> new ArrayList<>()).add(userId);
        }

        // 移除有多个 userId 映射到同一 idCardNo 的记录
        for (Map.Entry<String, List<Long>> entry : idCardNoToUserIds.entrySet()) {
            if (entry.getValue().size() > 1) {
                log.warn("发现 idCardNo={} 映射到多个 userId: {}, 移除这些记录", entry.getKey(), entry.getValue());
                entry.getValue().forEach(updatesToApply::remove);
            }
        }

        log.info("最终需要更新的记录数: {}", updatesToApply.size());

        // 生成 SQL 语句
        List<String> updateSqlStatements = new ArrayList<>();
        if (updatesToApply.isEmpty()) {
            log.info("没有找到任何需要更新的用户信息。");
            writeInfoResponse(response, "没有找到任何需要更新的用户信息。");
            return;
        }

        Sm4Encrypt sm4Encrypt = new Sm4Encrypt();
        for (Map.Entry<Long, String> entry : updatesToApply.entrySet()) {
            Long userId = entry.getKey();
            String idCardNo = entry.getValue();
            String safeIdCardNo = escapeSql(idCardNo);
            safeIdCardNo = sm4Encrypt.encrypt(safeIdCardNo);
            String sql = String.format("UPDATE cscp_user SET id_card_no = '%s' WHERE id = %d;commit;",
                    safeIdCardNo, userId);
            updateSqlStatements.add(sql);
        }

        // 设置响应头，实现文件下载
        response.setContentType("text/plain;charset=UTF-8");
        String fileName = "update_id_card_no_sql_" + companyId + ".sql";
        response.setHeader("Content-Disposition", "attachment; filename=\"" +
                URLEncoder.encode(fileName, "UTF-8").replace("+", "%20") + "\"");

        // 将 SQL 语句写入响应输出流
        try (PrintWriter writer = response.getWriter()) {
            for (String sql : updateSqlStatements) {
                writer.write(sql);
                writer.write(System.lineSeparator());
            }
            writer.flush();
        } catch (IOException e) {
            log.error("写入 SQL 文件到响应流失败：", e);
            throw e;
        }

        // 同时将结果保存到本地 TXT 文件
        try {
            String localFileName = "update_id_card_sql.txt";
            File localFile = new File(localFileName);

            try (FileOutputStream fos = new FileOutputStream(localFile);
                 OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
                 BufferedWriter bw = new BufferedWriter(osw)) {

                for (String sql : updateSqlStatements) {
                    bw.write(sql);
                    bw.newLine();
                }
                bw.flush();
                log.info("已成功将结果保存到本地文件：{}", localFile.getAbsolutePath());
            }
        } catch (IOException e) {
            log.error("保存结果到本地 TXT 文件失败：", e);
        }

        long endTime = System.currentTimeMillis();
        log.info("为机构 {} 生成用户身份证更新 SQL 完成，耗时 {} 秒", companyId, (endTime - startTime) / 1000.0);
    }

    @Override
    public PageResult<CscpUserDTO> selectDivisionUserById(CscpUserDTO cscpUserDTO, BasePageForm page) {
        try {
            // 区划机构管理 - 顶层
            List<CscpOrgDTO> cscpOrgList = cscpOrgService.selectDivisionOrgById_new(null, null);

            if (CollectionUtils.isEmpty(cscpOrgList)) {
                return new PageResult<>(Collections.emptyList(), 0L, 0L);
            }

            cscpUserDTO.setRootOrgCodePath(cscpOrgList.stream().map(CscpOrgDTO::getOrgCodePath).collect(Collectors.toList()));

            // 查询用户列表
            if (StringUtils.isNotBlank(cscpUserDTO.getRealName())) {
                cscpUserDTO.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(cscpUserDTO.getRealName()) : this.division(cscpUserDTO.getRealName()));
            }
            if (StringUtils.isNotBlank(cscpUserDTO.getIdCardNo())) {
                cscpUserDTO.setIdCardNo(westoneEncryptService.isCipherMachine() ?
                        westoneEncryptService.encryptBySM4ECB_WithKeyProtectedBySM2(cscpUserDTO.getIdCardNo())
                        : KeyCenterUtils.encrypt(cscpUserDTO.getIdCardNo()));
            }
            // 转义特殊字符
            String escapedName = patternQuote(cscpUserDTO.getRealName());
            cscpUserDTO.setRealName(escapedName);
            cscpUserDTO.setMobile(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(cscpUserDTO.getMobile()) : this.division(cscpUserDTO.getMobile()));
            long t2 = System.currentTimeMillis();
            IPage<CscpUserDTO> cscpUserIPage = cscpUserRepository.criteriaQueryCscpUserListNoAdmin(
                    PageHelperUtil.getMPlusPageByBasePage(page), cscpUserDTO);
            log.info("分页查询用户列表sql执行时间" + (System.currentTimeMillis() - t2));

            if (cscpUserIPage == null || CollectionUtils.isEmpty(cscpUserIPage.getRecords())) {
                return new PageResult<>(Collections.emptyList(), 0L, 0L);
            }

            // 数据脱敏处理
            List<CscpUserDTO> desensitizedUsers = cscpUserIPage.getRecords().stream()
                    .peek(user -> {
                        if (user != null) {
                            user.setRealName(DesensitizeUtil.desensitizedName(user.getRealName()));
                            user.setMobile(DesensitizeUtil.desensitizedPhoneNumber(user.getMobile()));
                        }
                    })
                    .collect(Collectors.toList());

            return new PageResult<>(desensitizedUsers, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
        } catch (Exception e) {
            log.error("selectDivisionUserById error:{}", e.toString());
            return new PageResult<>(Collections.emptyList(), 0L, 0L);
        }
    }

    @Override
    public PageResult<CscpUserDTO> findByOrgIdNew(CscpUserDTO cscpUserDTO, BasePageForm page) {
        if (SecurityUtils.isGeneralName() && CollUtil.isEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())) {
            cscpUserDTO.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        }
        if (SecurityUtils.isTenantName()) {
            cscpUserDTO.setTenantId(SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        }
        if (StringUtils.isNotEmpty(cscpUserDTO.getIdCardNo())) {
            cscpUserDTO.setIdCardNo(
                    westoneEncryptService.isCipherMachine()
                            ? westoneEncryptService.encryptBySM4ECB_WithKeyProtectedBySM2(
                            cscpUserDTO.getIdCardNo()) : KeyCenterUtils.encrypt(cscpUserDTO.getIdCardNo()));
        }
        cscpUserDTO.setRealName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(cscpUserDTO.getRealName()) : this.division(cscpUserDTO.getRealName()));
        cscpUserDTO.setMobile(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(cscpUserDTO.getMobile()) : this.division(cscpUserDTO.getMobile()));

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        if (currentCompanyId != null && CollUtil.isEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())) {
            CscpOrg byId = cscpOrgService.getById(currentCompanyId);
            LambdaQueryWrapper<CscpOrg> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotEmpty(byId.getOrgCodePath())) {
                orgLambdaQueryWrapper.like(CscpOrg::getOrgCodePath, byId.getOrgCodePath());
            } else {
                orgLambdaQueryWrapper.like(CscpOrg::getOrgCode, byId.getOrgCode());
            }
            List<CscpOrg> cscpOrgs = cscpOrgService.selectListNoAdd(orgLambdaQueryWrapper);
            List<Long> idList = cscpOrgs.stream().map(CscpOrg::getId).collect(Collectors.toList());
            cscpUserDTO.setCompanyIdList(idList);
        }
        // 查询是否部门管理员，设置管理部门
        List<Long> deptIDList = null;
        if (cscpUserDTO.getUserType() != null && null != cscpUserDTO.getUserType().name() && StringUtils.equals(UserType.DEPT_USER.name(), cscpUserDTO.getUserType().name())) {
            List<TDeptManagementAuthorityDTO> tDeptManagementAuthorityDTOList = tDeptManagementAuthorityService.findManageDeptByUserId(SecurityUtils.getCurrentUserId());
            deptIDList = tDeptManagementAuthorityDTOList.stream().map(i -> i.getDeptId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deptIDList)) {
                return new PageResult<>(new ArrayList<>(), 0, 0);
            }
        }
        // 普通用户不能查看
        if (SecurityUtils.isNormalName() && CollUtil.isEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())) {
            if (CollectionUtils.isEmpty(deptIDList)) {
                return new PageResult<>(new ArrayList<>(), 0, 0);
            }
        }

        IPage<CscpUserDTO> cscpUserIPage = null;
        // 转义特殊字符
        String escapedName = patternQuote(cscpUserDTO.getRealName());
        cscpUserDTO.setRealName(escapedName);
        if (cscpUserDTO.getCompanyId() == null || CollUtil.isNotEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())) {
            cscpUserIPage = criteriaQueryCscpUserListNoAdmin(
                    PageHelperUtil.getMPlusPageByBasePage(page), cscpUserDTO);
        } else {
            cscpUserIPage = cscpUserRepository.queryCscpUserList(
                    PageHelperUtil.getMPlusPageByBasePage(page), cscpUserDTO, deptIDList);
        }
        List<CompletableFuture<CscpUserDTO>> futures = cscpUserIPage.getRecords().stream()
                .map(x -> CompletableFuture.supplyAsync(() -> {
                    x.setExamineStatusName(UserExamineStatusEnum.obtainType(x.getExamineStatus()));

                    x.setRealName(DesensitizeUtil.desensitizedName(x.getRealName()));
                    x.setMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getMobile()));

                    return x;
                })).collect(Collectors.toList());

        List<CscpUserDTO> cscpUserDTOList = futures.stream().map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
        return new PageResult<>(cscpUserDTOList, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }

    @Override
    public PageResult<CscpUserDTO> queryUserByTreeIdAndRoleId(Long orgId, Long roleId, BasePageForm basePageForm) {
        IPage<CscpUserDTO> data = cscpUserRepository.queryUserByTreeIdAndRoleIdPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), orgId, roleId);

        return new PageResult<CscpUserDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    @Override
    public Boolean sendSmsVerificationCodeWw(SmsVerificationCodeDTO smsVerificationCode) {

        if (Objects.isNull(smsVerificationCode) || !com.ctsi.hndx.utils.StringUtils.isNotEmpty(smsVerificationCode.getPhone())) {
            throw new BusinessException("手机号不能为空!");
        }

        //判断这个手机号是否存在库里
        boolean existByMobile = this.existByMobile(smsVerificationCode.getPhone());
        if (!existByMobile) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        //手机号码
        String phone = SysConfigConstant.SMS_CODE + smsVerificationCode.getPhone();

        //判断手机号是否已经发送过短信了，每个手机号只能60秒发一次
//        if (!Objects.isNull(redisUtil.get(phone)) && redisUtil.getExpire(phone) > validTime - sendSmsTime) {
//            throw new BusinessException("短信操作频繁!");
//        }

        //生成验证码
        String code = String.valueOf((int) ((Math.random() * 9 + 1) * Math.pow(10, 5)));
        //发送短信
        try {
            SmsSendUtil.sendSms(smsVerificationCode.getPhone(), code, SmsSendEnum.USER_CAPTCHA);
        } catch (Throwable e) {
            throw new BusinessException("短信发送失败!");
        }

        //把手机号存到redis中,有效时间为validTime
        redisUtil.set(phone, code, validTime, TimeUnit.SECONDS);

        return true;


    }

    @Override
//@Cacheable(value = "userCache-findByCurrentUserName", key = "#loginName", unless = "#result == null")
    public Optional<CscpUserDTO> findByCurrentUserName2(String loginName) {
        if (StringUtils.isBlank(loginName)) {
            throw new IllegalArgumentException("登录名不能为空");
        }
        CscpUserDTO cscpUserDTO = RequestUserContextUtil.getLoginContext();

        if (cscpUserDTO == null) {
            throw new BusinessException("用户未找到");
        }
        List<CscpUserOrgDTO> cscpUserOrgDTOList = cscpUserOrgService.qryUserOrgByUserId(cscpUserDTO.getId());

        if (cscpUserOrgDTOList.size() == 1) {
            CscpUserOrgDTO dto = cscpUserOrgDTOList.get(0);
            if (dto.getCompanyId() == null || dto.getOrgId() == null) {
                throw new BusinessException("单位ID或组织机构ID为空！");
            }

            CscpOrg companyOrg = cscpOrgService.getById(dto.getCompanyId());
            if (companyOrg == null) {
                throw new BusinessException("单位信息不存在");
            }

            CscpOrg departmentOrg = cscpOrgService.getById(dto.getOrgId());
            if (departmentOrg == null) {
                throw new BusinessException("部门信息不存在");
            }

            cscpUserDTO.setCompanyId(companyOrg.getId());
            cscpUserDTO.setCrmTenantType(companyOrg.getCrmTenantType());
            cscpUserDTO.setCompanyName(companyOrg.getOrgName());
            cscpUserDTO.setHasWatermark(companyOrg.getHasWatermark());
            cscpUserDTO.setSplitview(companyOrg.getSplitview());
            cscpUserDTO.setCloudDiskSpaceSize(companyOrg.getCloudDiskSpaceSize());

            cscpUserDTO.setDepartmentId(departmentOrg.getId());
            cscpUserDTO.setDepartmentName(departmentOrg.getOrgName());
            cscpUserDTO.setUserType(UserType.PERSON_USER);

        } else if (cscpUserOrgDTOList.size() > 1) {
            boolean isDeFaultLogin = false;
            for (CscpUserOrgDTO dto : cscpUserOrgDTOList) {
                if (dto.getDefaultDepartment() == CscpUserOrg.DEFAULTDEPARMENT.LOGIN.getCode()) {
                    if (dto.getCompanyId() == null || dto.getOrgId() == null) {
                        throw new BusinessException("默认单位ID或组织机构ID为空！");
                    }

                    CscpOrg companyOrg = cscpOrgService.getById(dto.getCompanyId());
                    if (companyOrg == null) {
                        throw new BusinessException("默认单位信息不存在");
                    }

                    CscpOrg departmentOrg = cscpOrgService.getById(dto.getOrgId());
                    if (departmentOrg == null) {
                        throw new BusinessException("默认部门信息不存在");
                    }

                    cscpUserDTO.setCompanyId(companyOrg.getId());
                    cscpUserDTO.setCrmTenantType(companyOrg.getCrmTenantType());
                    cscpUserDTO.setCompanyName(companyOrg.getOrgName());
                    cscpUserDTO.setHasWatermark(companyOrg.getHasWatermark());
                    cscpUserDTO.setSplitview(companyOrg.getSplitview());
                    cscpUserDTO.setCloudDiskSpaceSize(companyOrg.getCloudDiskSpaceSize());

                    cscpUserDTO.setDepartmentId(departmentOrg.getId());
                    cscpUserDTO.setDepartmentName(departmentOrg.getOrgName());
                    cscpUserDTO.setUserType(UserType.PERSON_USER);

                    isDeFaultLogin = true;
                    break;
                }
            }
            if (!isDeFaultLogin) {
                throw new BusinessException(ResultCode.USER_NO_DEFAULT_LOGIN_ERROR);
            }
        } else {
            if (!SYSTEM_USERS.contains(loginName)) {
                LambdaQueryWrapper<TSysTenant> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(TSysTenant::getTenantLoginName, loginName);
                TSysTenant tSysTenant = tSysTenantMapper.selectOne(lambdaQueryWrapper);
                if (tSysTenant != null) {
                    cscpUserDTO.setTenantId(tSysTenant.getId());
                    cscpUserDTO.setUserType(UserType.TENANT_USER);
                } else {
                    throw new BusinessException("没有给用户设置组织机构，请联系管理员设置组织机构");
                }
            } else {
                cscpUserDTO.setUserType(UserType.SYSTEM_USER);
            }
        }

        return Optional.of(cscpUserDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, String> checkUserOrgByOrgId(CscpUserOrgAlterDTO record) {
        String msg = "操作成功";

        if (CollectionUtils.isEmpty(record.getUserList())) {
            throw new BusinessException("参数错误, 用户不能为空");
        }
        List<Long> userIds = record.getUserList().stream().map(CscpUserDTO::getId).collect(Collectors.toList());

        if ("remove".equals(record.getFlag())) {
            // 移除
            LambdaQueryWrapper<CscpUserOrg> updateUserOrgLqw = Wrappers.lambdaQuery(CscpUserOrg.class)
                    .in(CscpUserOrg::getUserId, userIds)
                    .eq(CscpUserOrg::getOrgId, record.getOldOrgId());
            cscpUserOrgRepository.delete(updateUserOrgLqw);
        } else if (null != record.getNewOrgId() && "alter".equals(record.getFlag())) {
            // 变更
            CscpOrg cscpOrg = cscpOrgRepository.selectById(record.getNewOrgId());
            if (Objects.isNull(cscpOrg)) {
                throw new BusinessException("机构不存在");
            }
            Long orgId = cscpOrg.getId();
            for (CscpUserDTO cscpUserDTO : record.getUserList()) {
                CscpUserOrgDTO cscpUserOrgDTO = new CscpUserOrgDTO();
                cscpUserOrgDTO.setCompanyId(cscpOrg.getCompanyId());
                cscpUserOrgDTO.setCompanyName(cscpOrg.getOrgName());
                if (!Objects.isNull(cscpUserDTO.getPost())) {
                    cscpUserOrgDTO.setPost(cscpUserDTO.getPost());
                }
                if (!Objects.isNull(cscpUserDTO.getRank())) {
                    cscpUserOrgDTO.setRank(cscpUserDTO.getRank());
                }
                cscpUserOrgDTO.setUserId(cscpUserDTO.getId());
                cscpUserOrgDTO.setOrgId(orgId);
                cscpUserOrgDTO.setOrgName(cscpOrg.getOrgName());
                cscpUserOrgDTO.setOrgAbbreviation(cscpOrg.getOrgAbbreviation());
                cscpUserOrgDTO.setPathCode(cscpOrg.getPathCode());
                cscpUserOrgDTO.setDefaultDepartment(1);
                cscpUserOrgDTO.setOrderBy(cscpUserDTO.getOrderBy());
                cscpUserOrgService.insert(cscpUserOrgDTO);
            }
        }
        return Pair.of(true, msg);
    }

    @Override
    public IPage<CscpUserDTO> queryCscpUserByRole(IPage iPage, QueryCscpUserNotInListDTO build) {
        return cscpUserRepository.queryCscpUserByRole(iPage, build);
    }

    @Override
    public PageResult<CscpUserDTO> queryUserByTreeIdAndRoleCode(Long orgId, List<String> roleCodes, BasePageForm basePageForm) {
        IPage<CscpUserDTO> data = cscpUserRepository.queryUserByTreeIdAndRoleCodePage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), orgId, roleCodes);

        return new PageResult<>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    // SQL 特殊字符转义
    private String escapeSql(String value) {
        if (value == null) {
            return "";
        }
        // 主要转义单引号
        return value.replace("'", "''");
    }


    // 辅助方法：写入普通文本信息到响应
    private void writeInfoResponse(HttpServletResponse response, String message) throws IOException {
        response.setContentType("text/plain;charset=UTF-8");
        try (PrintWriter writer = response.getWriter()) {
            writer.write(message);
            writer.flush();
        } catch (IOException e) {
            log.error("写入信息到响应流失败: {}", message, e);
            throw e;
        }
    }


    private String generateNewLoginName(String baseLoginName, int suffix, Set<String> existingNames) {
        String newLoginName = baseLoginName + "_" + suffix;
        while (existingNames.contains(newLoginName)) {
            suffix++;
            newLoginName = baseLoginName + "_" + suffix;
        }
        return newLoginName;
    }


    /**
     * 设置redis缓存的key有效期一天，每天凌晨失效
     *
     * @param currentDate
     * @return
     */
    public static Integer getRemainSecondsOneDay(Date currentDate) {
        //使用plusDays加传入的时间加1天，将时分秒设置成0
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        //使用ChronoUnit.SECONDS.between方法，传入两个LocalDateTime对象即可得到相差的秒数
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }

    /**
     * 校验身份证号是否重复
     *
     * @param cscpUserDTO
     * @return
     */
    public boolean existIdCardNoRepeat(CscpUserDTO cscpUserDTO) {
        if (null == cscpUserDTO.getIdCardNo()) {
            return false;
        }
        String idCardNo = cscpUserDTO.getIdCardNo();
        idCardNo = westoneEncryptService.isCipherMachine() ?
                westoneEncryptService.encryptBySM4ECB_WithKeyProtectedBySM2(idCardNo) : KeyCenterUtils.encrypt(idCardNo);
        String delaultIdCardNo = westoneEncryptService.isCipherMachine() ?
                westoneEncryptService.encryptBySM4ECB_WithKeyProtectedBySM2(SysConstant.DELAULT_ID_CARD_NO) : KeyCenterUtils.encrypt(SysConstant.DELAULT_ID_CARD_NO);
        LambdaQueryWrapper<CscpUser> queryWrapper = Wrappers.lambdaQuery();
        if (delaultIdCardNo.equals(idCardNo)) {
            return true;
        }
        // 修改时，判断非当前用户是否存在相同身份证
        if (null != cscpUserDTO.getId()) {
            queryWrapper.ne(CscpUser::getId, cscpUserDTO.getId());
        }
        queryWrapper.eq(CscpUser::getIdCardNo, idCardNo);
        queryWrapper.eq(CscpUser::getIdCardNo, delaultIdCardNo);
        int returnRow = cscpUserRepository.selectCount(queryWrapper);
        return returnRow > 0;
    }


    @Override
    public Integer selectUserListCount(CscpUserDTO dto) {
        return cscpUserRepository.selectUserListCount(dto);
    }

    @Override
    public List<CscpUserExportDTO> selectUserListByPage(CscpUserDTO dto, Integer pageNo, Integer pageSize) {
        return cscpUserRepository.selectUserListByPage(dto, pageNo, pageSize);
    }
}
