package com.ctsi.ssdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.enums.RoleTypeEnum;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.systenant.entity.dto.TSysTenantDTO;
import com.ctsi.hndx.systenant.service.ITSysTenantService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.admin.domain.TAppPermissionModule;
import com.ctsi.ssdc.admin.domain.TAppRole;
import com.ctsi.ssdc.admin.domain.dto.TAppPermissionModuleDTO;
import com.ctsi.ssdc.admin.repository.TAppPermissionModuleRepository;
import com.ctsi.ssdc.admin.service.ITAppPermissionModuleService;
import com.ctsi.ssdc.admin.service.ITAppRolePermissionService;
import com.ctsi.ssdc.admin.service.ITAppRoleService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */

@Slf4j
@Service
public class TAppPermissionModuleServiceImpl extends SysBaseServiceImpl<TAppPermissionModuleRepository, TAppPermissionModule> implements ITAppPermissionModuleService {

    @Autowired
    private TAppPermissionModuleRepository tAppPermissionModuleMapper;

    @Autowired
    private ITAppRoleService itAppRoleService;

    @Autowired
    private ITAppRolePermissionService itAppRolePermissionService;

    @Autowired
    private ITSysTenantService itSysTenantService;

    private static final Long ROOT_TENANT_ID = 0L;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TAppPermissionModuleDTO> queryListPage(TAppPermissionModuleDTO entityDTO, BasePageForm basePageForm) {
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper<>();

        // 获取当前用户信息
        CscpUserDetail currentUser = SecurityUtils.getCurrentCscpUserDetail();

        // 根据角色设置查询条件
        if (SecurityUtils.isSystemName()) {
            queryWrapper.eq(TAppPermissionModule::getCreateBy, currentUser.getId());
        } else if (SecurityUtils.isRegionAdmin()) {
            // 区划管理员只能查看通用模块(admin创建的和本区划创建的)
            queryWrapper.and(wrapper -> wrapper
                    .and(w1 -> w1.eq(TAppPermissionModule::getCommonFlag, 1)
                            .eq(TAppPermissionModule::getCreateBy,2L))
                    .or(w2 -> w2 .eq(TAppPermissionModule::getCreateBy, currentUser.getId()))
            );
        } else if (SecurityUtils.isUnitAdmin()) {
            // 单位管理员只能查看通用模块(admin、区划管理员和本单位创建的)
            List<Long> orgIdPathList = new ArrayList<>();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(currentUser.getOrgIdPath())) {
                orgIdPathList = Arrays.stream(currentUser.getOrgIdPath().split("\\|"))
                        .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
            }

            List<Long> finalOrgIdPathList = orgIdPathList;
            queryWrapper.and(wrapper ->
                            wrapper.eq(TAppPermissionModule::getCommonFlag, 1)
                                    .and(w ->
                                            w.in(!finalOrgIdPathList.isEmpty(), TAppPermissionModule::getDepartmentId, finalOrgIdPathList)
                                            .or().eq(TAppPermissionModule::getCreateBy, 2L)
                                    )
                    ).or().eq(TAppPermissionModule::getCompanyId, currentUser.getCompanyId()
            );
        } else {
            // 其他用户无权限查看
            return new PageResult<>(new ArrayList<>(), 0L, basePageForm.getCurrentPage());
        }

        // 添加基础查询条件
        if (StrUtil.isNotBlank(entityDTO.getCode())) {
            queryWrapper.eq(TAppPermissionModule::getCode, entityDTO.getCode());
        }
        if (StrUtil.isNotBlank(entityDTO.getName())) {
            queryWrapper.eq(TAppPermissionModule::getName, entityDTO.getName());
        }

        // 按排序字段升序
        queryWrapper.orderByAsc(TAppPermissionModule::getRoleType)
                .orderByAsc(TAppPermissionModule::getSort)
                .orderByDesc(TAppPermissionModule::getCreateTime);

        // 执行分页查询
        IPage<TAppPermissionModule> pageData = tAppPermissionModuleMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        // 转换并返回结果
        IPage<TAppPermissionModuleDTO> data = pageData.convert(entity ->
            BeanConvertUtils.copyProperties(entity, TAppPermissionModuleDTO.class));

        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public PageResult<TAppPermissionModuleDTO> queryList(TAppPermissionModuleDTO entityDTO, BasePageForm basePageForm) {
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entityDTO.getCode())) {
            queryWrapper.eq(TAppPermissionModule::getCode, entityDTO.getCode());
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entityDTO.getName())) {
            queryWrapper.eq(TAppPermissionModule::getName, entityDTO.getName());
        }
        queryWrapper.orderByAsc(TAppPermissionModule::getSort);
        IPage<TAppPermissionModule> pageData = tAppPermissionModuleMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        //返回
        IPage<TAppPermissionModuleDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TAppPermissionModuleDTO.class));

        return new PageResult<TAppPermissionModuleDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TAppPermissionModuleDTO findOne(Long id) {
        TAppPermissionModule  tAppPermissionModule =  tAppPermissionModuleMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tAppPermissionModule,TAppPermissionModuleDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TAppPermissionModuleDTO create(TAppPermissionModuleDTO entityDTO) {
        // 判断code码是否存在
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TAppPermissionModule::getCode, entityDTO.getCode());
        queryWrapper.or().eq(TAppPermissionModule::getName, entityDTO.getName());
        List<TAppPermissionModule> appPermissionModuleList = tAppPermissionModuleMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(appPermissionModuleList)) {
            throw new BusinessException("该code码或模块名称已存在");
        }

        TAppPermissionModule tAppPermissionModule =  BeanConvertUtils.copyProperties(entityDTO,TAppPermissionModule.class);
        // 根据登录用户信息设置 roleType
        if (SecurityUtils.isSystemName()) {
            tAppPermissionModule.setRoleType(1); // admin
        } else if (SecurityUtils.isTenantName()) {
            tAppPermissionModule.setRoleType(3); // 租户管理员
        } else if (SecurityUtils.isRegionAdmin()) {
            tAppPermissionModule.setRoleType(4); // 区划管理员
        } else if (SecurityUtils.isUnitAdmin()) {
            tAppPermissionModule.setRoleType(5); // 单位管理员
        } else {
            tAppPermissionModule.setRoleType(6); // 普通用户
        }
        this.updateSort(entityDTO);
        save(tAppPermissionModule);
        return  BeanConvertUtils.copyProperties(tAppPermissionModule,TAppPermissionModuleDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TAppPermissionModuleDTO entity) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        int result = tAppPermissionModuleMapper.delete(new LambdaQueryWrapper<TAppPermissionModule>()
                .eq(TAppPermissionModule::getId, entity.getId()).eq(TAppPermissionModule::getCreateBy, currentUserId));
        if (result == 0) {
            throw new BusinessException("只能删除自己创建的模块");
        }
        this.updateSort(entity);
        TAppPermissionModule tAppPermissionModule = BeanConvertUtils.copyProperties(entity,TAppPermissionModule.class);
        return tAppPermissionModuleMapper.updateById(tAppPermissionModule);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        int result = tAppPermissionModuleMapper.delete(new LambdaQueryWrapper<TAppPermissionModule>()
                .eq(TAppPermissionModule::getId, id).eq(TAppPermissionModule::getCreateBy, currentUserId));
        if (result == 0) {
            throw new BusinessException("只能删除自己创建的权限模块");
        }
        return result;
    }


    /**
     * 验证是否存在
     *
     * @param TAppPermissionModuleId
     * @return
     */
    @Override
    public boolean existByTAppPermissionModuleId(Long TAppPermissionModuleId) {
        if (TAppPermissionModuleId != null) {
            LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TAppPermissionModule::getId, TAppPermissionModuleId);
            List<TAppPermissionModule> result = tAppPermissionModuleMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional
    public Boolean insertBatch(List<TAppPermissionModuleDTO> dataList) {
        List<TAppPermissionModule> result = ListCopyUtil.copy(dataList, TAppPermissionModule.class);
        return saveBatch(result);
    }

    @Override
    public TAppPermissionModuleDTO queryTAppPermissionModuleByCode(String code) {
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TAppPermissionModule::getCode, code);
        TAppPermissionModule tAppPermissionModule = tAppPermissionModuleMapper.selectOne(queryWrapper);
        return BeanConvertUtils.copyProperties(tAppPermissionModule,TAppPermissionModuleDTO.class);
    }

    @Override
    public List<TAppPermissionModuleDTO> queryTAppPermissionModuleByCodeList(List<String> codeList) {
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TAppPermissionModule::getCode, codeList);
        queryWrapper.orderByAsc(TAppPermissionModule::getSort);
        List<TAppPermissionModule> tAppPermissionModuleList = tAppPermissionModuleMapper.selectList(queryWrapper);
        return ListCopyUtil.copy(tAppPermissionModuleList, TAppPermissionModuleDTO.class);
    }

    /**
     * 查询该租户所拥有的移动端APP功能模块
     * @param tenantId
     * @return
     */
    @Override
    public List<TAppPermissionModuleDTO> queryAppPermissionModuleListByTenantId(Long tenantId) {
        TSysTenantDTO tenantDTO = itSysTenantService.findOne(tenantId);
        if (Objects.isNull(tenantDTO)) {
            return new ArrayList<>();
        }

        // 如果该租户设置了不受APP端权限控制，则查询所有功能模块
        if (Objects.nonNull(tenantDTO.getAppAuthority()) && !tenantDTO.getAppAuthority()) {
            LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
            List<TAppPermissionModule> tAppPermissionModules = tAppPermissionModuleMapper.selectList(queryWrapper);
            return ListCopyUtil.copy(tAppPermissionModules, TAppPermissionModuleDTO.class);

        }

        String appPermissionCode = tenantDTO.getAppPermissionCodes();

        // 如果该租户下App功能模块为空，则查询其父级租户下的App功能模块
        if (org.apache.commons.lang3.StringUtils.isBlank(appPermissionCode)) {
            int i = 0;
            while (StringUtils.isEmpty(tenantDTO.getAppPermissionCodes())) {
                if (Objects.isNull(tenantDTO.getParentId()) || ROOT_TENANT_ID.equals(tenantDTO.getParentId())) {
                    throw new BusinessException("该租户及其父租户下没有找到App功能模块");
                }
                Long parentId = tenantDTO.getParentId();
                tenantDTO = itSysTenantService.findOne(parentId);
                // 如果该租户的父级设置了不受APP端权限控制，则查询所有功能模块
                if (Objects.nonNull(tenantDTO.getAppAuthority()) && !tenantDTO.getAppAuthority()) {
                    LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
                    List<TAppPermissionModule> tAppPermissionModules = tAppPermissionModuleMapper.selectList(queryWrapper);
                    return ListCopyUtil.copy(tAppPermissionModules, TAppPermissionModuleDTO.class);
                }
                appPermissionCode = tenantDTO.getAppPermissionCodes();
                i ++;
                if (i > 5) {
                    throw new BusinessException("该租户及其父租户下没有找到App功能模块");
                }
            }
        }

        List<String> appPermissionCodeList = Arrays.asList(appPermissionCode.split(",")).stream().collect(Collectors.toList());
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TAppPermissionModule::getCode, appPermissionCodeList);
        List<TAppPermissionModule> appPermissionModuleList = tAppPermissionModuleMapper.selectList(queryWrapper);
        return ListCopyUtil.copy(appPermissionModuleList, TAppPermissionModuleDTO.class);
    }

    /**
     * 列表查询专用，模糊查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public PageResult<TAppPermissionModuleDTO> queryListLike(TAppPermissionModuleDTO entityDTO, BasePageForm basePageForm) {
        CscpUserDetail currentUser = SecurityUtils.getCurrentCscpUserDetail();
        LambdaQueryWrapper<TAppPermissionModule> queryWrapper = new LambdaQueryWrapper();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entityDTO.getCode())) {
            queryWrapper.like(TAppPermissionModule::getCode, entityDTO.getCode());
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entityDTO.getName())) {
            queryWrapper.like(TAppPermissionModule::getName, entityDTO.getName());
        }
        // 根据角色设置查询条件
        if (SecurityUtils.isSystemName()) {
            queryWrapper.eq(TAppPermissionModule::getCreateBy, currentUser.getId());
        } else if (SecurityUtils.isRegionAdmin()) {
            // 区划管理员只能查看通用模块(admin创建的和本区划创建的)
            queryWrapper.and(wrapper -> wrapper
                    .and(w1 -> w1.eq(TAppPermissionModule::getCommonFlag, 1)
                            .eq(TAppPermissionModule::getCreateBy,2L))
                    .or(w2 -> w2 .eq(TAppPermissionModule::getCompanyId, currentUser.getCompanyId()))
            );
        } else if (SecurityUtils.isUnitAdmin()) {
            // 单位管理员只能查看通用模块(admin、区划管理员和本单位创建的)
            List<Long> orgIdPathList = new ArrayList<>();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(currentUser.getOrgIdPath())) {
                orgIdPathList = Arrays.stream(currentUser.getOrgIdPath().split("\\|"))
                        .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
            }

            List<Long> finalOrgIdPathList = orgIdPathList;
            queryWrapper.and(wrapper ->
                            wrapper.eq(TAppPermissionModule::getCommonFlag, 1)
                                    .and(w ->
                                            w.in(!finalOrgIdPathList.isEmpty(), TAppPermissionModule::getDepartmentId, finalOrgIdPathList)
                                            .or().eq(TAppPermissionModule::getCreateBy, 2L)
                                    )
                            ).or().eq(TAppPermissionModule::getCompanyId, currentUser.getCompanyId()
            );
        }

        queryWrapper.orderByAsc(TAppPermissionModule::getRoleType)
                .orderByAsc(TAppPermissionModule::getSort)
                .orderByDesc(TAppPermissionModule::getCreateTime);
        IPage<TAppPermissionModule> pageData = tAppPermissionModuleMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        //返回
        IPage<TAppPermissionModuleDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TAppPermissionModuleDTO.class));

        return new PageResult<TAppPermissionModuleDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 排序号自增
     * @param entity
     * @return
     */
    @Override
    public Boolean updateSort(TAppPermissionModuleDTO entity){
        LambdaQueryWrapper<TAppPermissionModule> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TAppPermissionModule::getSort,entity.getSort());
        int count = tAppPermissionModuleMapper.selectCount(lambdaQueryWrapper);
        if (count > 0){
            tAppPermissionModuleMapper.updataSort(
                    SortEnum.builder()
                            .sort(entity.getSort())
                            .id(entity.getId())
                            .tableName("t_app_permission_module")
                            .sortName("sort")
                            .additionOrsubtraction("+")
                            .build());
        }
        return true;
    }
}
