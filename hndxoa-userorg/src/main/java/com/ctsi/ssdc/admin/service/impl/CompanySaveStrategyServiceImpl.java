package com.ctsi.ssdc.admin.service.impl;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.enums.OrgTypeEnum;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.hndx.systenant.mapper.TSysTenantMapper;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CompanySaveStrategyServiceImpl extends BaseOrgSaveStrategyServiceImpl {

    @Autowired
    private CscpOrgService cscpOrgService;
    @Autowired
    private CscpOrgRepository cscpOrgRepository;
    @Autowired
    private CscpUserService cscpUserService;
    @Autowired
    private TSysTenantMapper tSysTenantMapper;

    @Override
    public Boolean verifyOrgParam(CscpOrgDTO cscpOrgDTO) {
        // 只能租户管理员来创建单位，或者是admin新建租户的时候admin自动生成的，条件是有租户id

       /* if (!SecurityUtils.isTenantName() &&
                !(SecurityUtils.isSystemName() && cscpOrgDTO.getTenantId() != null)) {
            throw new BusinessException("只能由租户管理员创建单位");
        }*/

        long parentId = cscpOrgDTO.getParentId();
        // 如果插入的单位为顶级
        if (ROOT_ORG_ID.equals(parentId)) {
//            if (!OrgTypeEnum.ORG_TYPE_2.getCode().equals(cscpOrgDTO.getType())) {
//                throw new BusinessException("顶级机构只能是单位");
//            }
        } else {
            // 如果插入的单位不是顶级
            CscpOrg parentOrg = cscpOrgRepository.selectById(parentId);
            Set<Integer> set = new HashSet<>();
            set.add(OrgTypeEnum.ORG_TYPE_2.getCode());
            set.add(OrgTypeEnum.ORG_TYPE_3.getCode());
//            if (Objects.isNull(parentOrg) || set.contains(parentOrg.getType())) {
//                throw new BusinessException("单位的上级不能为空且只能是虚拟机构");
//            }
        }
        return true;
    }


    @Override
    public Map<String, String> getOrgCodeAndPathCode(CscpOrgDTO cscpOrgDTO) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
        queryWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);

        // 如果插入的单位为顶级
        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            if (CollectionUtils.isEmpty(orgList)) {
                String curOrgCode = ROOT_ORG_CODE_DEETAULT;
                String curOrgCodePath = ROOT_ORG_CODE_DEETAULT;
                String curOrgIdPath = "";
                return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
            }
            String curOrgCode = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgList.size() + 1),
                    ORG_CODE_LEN);
            String curOrgCodePath = curOrgCode;
            String curOrgIdPath = "";
            return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
        }

        // 如果插入的单位不是顶级
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
//        if (StringUtils.isBlank(parentOrg.getCode()) && StringUtils.isBlank(parentOrg.getPathCode())) {
//            throw new BusinessException("单位上级节点的单位编码和单位完整编码不能为空");
//        }
        String parentOrgCodePath = parentOrg.getOrgCodePath();
        String parentOrgIdPath = parentOrg.getOrgIdPath();
        String parentOrgPathCode = parentOrg.getPathCode();

        String curOrgCode = parentOrgPathCode;
        String curOrgCodePath = parentOrgCodePath + "|" + cscpOrgDTO.getOrgCode();
        String curOrgIdPath = StringUtils.isEmpty(parentOrgIdPath) ? parentOrg.getId().toString()
                : parentOrgIdPath + "|" + parentOrg.getId();
        return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
    }

    @Override
    public Map<String, String> getRegionCode(CscpOrgDTO cscpOrgDTO) {
        return this.getRegionCode_new(cscpOrgDTO);
    }


    @SuppressWarnings("all")
    public Map<String, String> getRegionCode_new(CscpOrgDTO cscpOrgDTO) {
        //获取上级机构信息
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        // 获取同级中最后一个机构信息
        CscpOrg org = cscpOrgService.getMaxCodeCscpOrg(cscpOrgDTO);
        // 如果是顶级单位
        if (cscpOrgDTO.getParentId().equals(0L)) {
            String orgCode = ROOT_ORG_CODE_DEETAULT;
            if (org != null) orgCode = org.getOrgCode();
            String strOrgCode = org.getOrgCode().trim();
            BigInteger bigInteger = new BigInteger(strOrgCode);
            BigInteger resultCode = bigInteger.add(BigInteger.ONE);
            String orgCodeStr = resultCode.toString();
            String maxNumber = orgCodeStr.substring(orgCodeStr.length() - 4);
            maxNumber = Integer.valueOf(maxNumber).toString();
            String curOrgCodePath = orgCodeStr;
            String curOrgIdPath = "";
            return this.regionMap("", orgCodeStr, maxNumber, curOrgCodePath, curOrgIdPath, "");
        }
        String orgCode = "";
        String strMaxNumber = "";
        if(org == null){
            orgCode = parentOrg.getOrgCode() + "0001";
            strMaxNumber = "1";
        }else{
            String strOrgCode = org.getOrgCode().trim();
            BigInteger bigInteger = new BigInteger(strOrgCode);
            BigInteger resultCode = bigInteger.add(BigInteger.ONE);
            String orgCodeStr = resultCode.toString();
            String maxNumber = orgCodeStr.substring(orgCodeStr.length() - 4);
            orgCode = orgCodeStr;
            strMaxNumber = String.valueOf(maxNumber);
        }
        String curOrgCodePath = parentOrg.getOrgCodePath() + "|" + orgCode;
        String curOrgIdPath = StringUtils.isEmpty(parentOrg.getOrgIdPath()) ? parentOrg.getId().toString()
                : parentOrg.getOrgIdPath() + "|" + parentOrg.getId();
        String orderBy = parentOrg.getOrderBy() != null ? (100000 + parentOrg.getOrderBy()) + "" : "";
        return this.regionMap(parentOrg.getRegionCode(), orgCode,strMaxNumber, curOrgCodePath, curOrgIdPath, orderBy);
    }

    @Override
    public CscpOrgDTO getCompanyId(CscpOrgDTO cscpOrgDTO) {
        // 单位的主键ID就是单位id
        if (!Objects.isNull(cscpOrgDTO.getId())) {
            cscpOrgDTO.setCompanyId(cscpOrgDTO.getId());
        } else {
            Long id = SnowflakeIdUtil.getSnowFlakeLongId();
            cscpOrgDTO.setId(id);
            cscpOrgDTO.setCompanyId(id);
        }
        return cscpOrgDTO;
    }

    @Override
    public boolean updateOrgSort(CscpOrgDTO cscpOrgDTO) {
        // 先查询当前租户下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(CscpOrg::getTenantId, tenantId);
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
//        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_2.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCountNoAdd(lambdaQueryWrapper);

        // 如果存在，则把该租户下的大于等于该排序的所有单位排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
//            queryWrapper.eq(CscpOrg::getTenantId, tenantId);
//            queryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_2.getCode());
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            cscpOrgRepository.selectListNoAdd(queryWrapper).stream().map(i -> {
                i.setOrderBy(i.getOrderBy() + 1);
                return cscpOrgRepository.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }

    @Override
    public boolean crmUpdateOrgSort(CscpOrgDTO cscpOrgDTO) {
        Long tenantId = null;
        String crmtype = cscpOrgDTO.getCrmTenantType();
        if(StringUtils.isEmpty(cscpOrgDTO.getGroupNumber())){
            tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        }else{
            LambdaQueryWrapper<TSysTenant> queryWrapperUser = new LambdaQueryWrapper<>();
            if("3".equals(crmtype)){
                crmtype = "2";
                queryWrapperUser.eq(TSysTenant::getCrmTenantType, crmtype);
            }else{
                queryWrapperUser.eq(TSysTenant::getCrmTenantType, cscpOrgDTO.getCrmTenantType());
            }
            TSysTenant tSysTenant = tSysTenantMapper.selectOneNoAdd(queryWrapperUser);
            tenantId =  tSysTenant.getId();
        }

        // 先查询当前租户下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpOrg::getTenantId, tenantId);
        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_2.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCount(lambdaQueryWrapper);

        // 如果存在，则把该租户下的大于等于该排序的所有单位排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getTenantId, tenantId);
            queryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_2.getCode());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            cscpOrgRepository.selectList(queryWrapper).stream().map(i -> {
                i.setOrderBy(i.getOrderBy() + 1);
                return cscpOrgRepository.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }

    /**
     * 单位新增用户
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    @Transactional
    public Boolean saveUserOrgRel(CscpOrgDTO cscpOrgDTO) {
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        String orgName = cscpOrgDTO.getOrgName();
        String name = PinyinUtil.getFirstLetter(orgName, "");
        String newName = cscpUserService.getUserNameMaxNumber(name + "admin");
        cscpUserDTO.setLoginName(newName);
        cscpUserDTO.setRealName(orgName + "管理员");
        cscpUserDTO.setMobile(SysConstant.DELAULT_MOBILE);
        if (cscpOrgDTO.getTenantId() != null) {
            cscpUserDTO.setTenantId(cscpOrgDTO.getTenantId());
        }
        List<Long> roleIds = new ArrayList();
        roleIds.add(Long.valueOf(SystemRole.COMPANY_ROLE.getId()));
        cscpUserDTO.setRoleIds(roleIds);

        List<Long> orgIds = new ArrayList<>();
        orgIds.add(cscpOrgDTO.getId());
        cscpUserDTO.setOrgIdList(orgIds);
        cscpUserDTO.setCrmTenantType(cscpOrgDTO.getCrmTenantType());
        cscpUserDTO.setGroupNumber(cscpOrgDTO.getGroupNumber());
        cscpUserDTO.setOrderBy(CscpUserDTO.DEFAULT_USER_SORT);
        cscpUserDTO.setStatistics(false);
        cscpUserDTO.setDisplay(true);
        cscpUserDTO.setStatus(1);
        cscpUserDTO.setExamineStatus(1);
        cscpUserDTO.setDefaultDepart(cscpOrgDTO.getCompanyId());
        cscpUserDTO.setPushAppCode(cscpOrgDTO.getPushAppCode());
        cscpUserService.insert(cscpUserDTO);
        return true;
    }

    @Override
    @Transactional
    public Boolean crmSaveUserOrgRel(CscpOrgDTO cscpOrgDTO,TSysTenant tSysTenant) {
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        String orgName = cscpOrgDTO.getOrgName();
        String name = PinyinUtil.getFirstLetter(orgName, "");
        String newName = cscpOrgDTO.getGroupNumber();
        cscpUserDTO.setLoginName(newName);
        cscpUserDTO.setRealName(orgName + "管理员");
        cscpUserDTO.setMobile(SysConstant.DELAULT_MOBILE);
        if (cscpOrgDTO.getTenantId() != null) {
            cscpUserDTO.setTenantId(cscpOrgDTO.getTenantId());
        }
        List<Long> roleIds = new ArrayList();
        roleIds.add(Long.valueOf(SystemRole.COMPANY_ROLE.getId()));
        cscpUserDTO.setRoleIds(roleIds);

        List<Long> orgIds = new ArrayList<>();
        orgIds.add(cscpOrgDTO.getId());
        cscpUserDTO.setOrgIdList(orgIds);
        cscpUserDTO.setCrmTenantType(cscpOrgDTO.getCrmTenantType());
        cscpUserDTO.setGroupNumber(cscpOrgDTO.getGroupNumber());
        cscpUserDTO.setOrderBy(CscpUserDTO.DEFAULT_USER_SORT);
        cscpUserDTO.setStatistics(false);
        cscpUserDTO.setDisplay(true);
        cscpUserDTO.setStatus(1);
        cscpUserDTO.setExamineStatus(1);
        cscpUserDTO.setPassword("Xtbg12345#");
        cscpUserService.crmInsert(cscpUserDTO,tSysTenant,cscpOrgDTO.getId());
        return true;
    }
}














