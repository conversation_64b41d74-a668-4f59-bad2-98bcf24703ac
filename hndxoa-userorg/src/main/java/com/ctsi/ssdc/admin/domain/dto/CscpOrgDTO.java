package com.ctsi.ssdc.admin.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tree.TreePO;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Generator
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "机构DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CscpOrgDTO extends BaseDtoEntity implements TreePO {

    private static final long serialVersionUID = 3709242178301712793L;


    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织机构名称")
    @ExcelProperty("组织机构名称")
    private String orgName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "组织机构的描述")
    @ExcelProperty("组织机构的描述")
    private String description;

    /**
     * 父id
     */
    @ApiModelProperty(value = "组织机构的父id")
    @ExcelProperty("组织机构的父id")
    private Long parentId;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    @ExcelProperty("顺序")
    private Integer orderBy;

    @ApiModelProperty(value = "用户机构中间表排序号")
    @ExcelIgnore
    private Integer userOrgSort;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码,不需要前端传值", readOnly = true)
    @ExcelProperty("机构编码")
    private String code;

    /**
     * 机构完整编码
     */
    @ApiModelProperty(value = "机构完整编码，不需要前端传值", readOnly = true)
    @ExcelProperty("机构完整编码")
    private String pathCode;

    /**
     * 机构级别
     */
    @ApiModelProperty(value = "机构级别，不需要前端传值", readOnly = true)
    @ExcelProperty("机构级别")
    private Integer level;

    /**
     * 机构类型
     * ORG_TYPE_1(1,"虚拟机构"),
     * ORG_TYPE_2(2,"单位"),
     * ORG_TYPE_3(3,"部门");
     */
    @ApiModelProperty(value = "机构类型,1表示虚拟机构，2表示单位，3表示部门",
            allowableValues = "range[1,3]")
    @ExcelProperty("机构类型")
    private Integer type;

    @ApiModelProperty("单位类型(0:党委办 1：政府办)")
    private Integer unitType;

    /**
     * 地区id
     */
    @ApiModelProperty(value = "地区id")
    @ExcelProperty("地区id")
    private String lanId;
    /**
     * 地区名称
     */
    @ApiModelProperty(value = "地区名称")
    @ExcelProperty("地区名称")
    private String lan;

    @ApiModelProperty(value = "单位ID")
    @ExcelProperty("单位ID")
    private Long companyId;

    @ApiModelProperty(value = "业务时是否发送短信，1表示默认发送，0表示不发送")
    @ExcelProperty("业务时是否发送短信")
    private int hasSms;

    @ApiModelProperty(value = "业务时是否加水印，1表示默认加水印，0表示无水印")
    @ExcelProperty("业务时是否加水印")
    private Integer hasWatermark;

    @ApiModelProperty(value = "正文编辑是否分屏，1表示分屏，0表示不分屏")
    @ExcelProperty("正文编辑是否分屏")
    private Integer splitview;

    /**
     * 单位共享云盘空间大小，单位GB，默认2G
     */
    @ApiModelProperty(value = "单位共享云盘空间大小，单位GB，默认2G")
    @ExcelProperty("单位共享云盘空间大小，单位GB，默认2G")
    private Integer cloudDiskSpaceSize;

    @ApiModelProperty(value = "机构简称")
    @ExcelProperty("机构简称")
    private String orgAbbreviation;

    @ApiModelProperty(value = "是否考核")
    @ExcelProperty("是否考核")
    private Boolean checkUp;


    /**
     * 是否分管领导(1个部门只能有1个)
     * 1表示为分管领导
     */
    @ApiModelProperty("分管领导ID")
    @ExcelIgnore
    private Long branchLeaderId;

    @ApiModelProperty("租户ID")
    @ExcelIgnore
    private Long tenantId;

    @ApiModelProperty("下级是否还有数据(0:下级无机构数据 1：下级有机构数据)")
    private Integer isValuable;

    @ApiModelProperty("单位级别")
    @ExcelIgnore
    private String unitLevel;

    @ApiModelProperty("主管部门")
    @ExcelIgnore
    private String authorityDepartment;

    @ApiModelProperty("详细类型")
    @ExcelIgnore
    private String detailType;

    /**
     * 是否为部门领导(1个部门可以设置多个)
     * 1表示为部门领导
     */
    @ApiModelProperty("部门领导ID集合")
    @ExcelIgnore
    private List<Long> departmentHeadIdList;

    @ApiModelProperty(value = "分管领导信息")
    @ExcelIgnore
    private CscpUserDTO branchLeaderDTO;

    @ApiModelProperty(value = "部门领导信息")
    @ExcelIgnore
    private List<CscpUserDTO> departmentHeadDTOList;

    @ApiModelProperty("职务")
    @ExcelIgnore
    private String post;

    @ApiModelProperty("职级")
    @ExcelIgnore
    private String rank;

    @ApiModelProperty("是否为部门领导")
    @ExcelIgnore
    private Integer departmentHead;

    @ApiModelProperty("实际考核人数")
    private Integer assessmentPeopleCount;

    @Override
    public String getTitle() {
        return this.orgName;
    }

    @ApiModelProperty(value = "群组号")
    @ExcelProperty("群组号")
    private String groupNumber;

    /**
     * CRM受理的客户类型（单位类型：1：标准型，2：项目型，3：试用型）
     */
    @ApiModelProperty(value = "客户类型")
    @ExcelProperty("客户类型")
    private String crmTenantType;


    /**
     * 客户经理姓名
     */
    @ApiModelProperty(value = "客户经理姓名")
    @ExcelProperty("客户经理姓名")
    private String nameOfAccountManager;

    /**
     * 客户经理电话
     */
    @ApiModelProperty(value = "客户经理电话")
    @ExcelProperty("客户经理电话")
    private String customerManagerTelephone;

    /**
     * 客户管理员姓名
     */
    @ApiModelProperty(value = "客户管理员姓名")
    @ExcelProperty("客户管理员姓名")
    private String customerAdministratorName;

    /**
     * 客户管理员联系电话
     */
    @ApiModelProperty(value = "客户管理员联系电话")
    @ExcelProperty("客户管理员联系电话")
    private String customerAdministratorTelephone;

    /**
     * 账号数
     */
    @ApiModelProperty(value = "账号数")
    @ExcelProperty("账号数")
    private Integer numberOfAccounts;

    /**
     * 项目合同期限
     */
    @ApiModelProperty(value = "项目合同期限")
    @ExcelProperty("项目合同期限")
    private String projectContractPeriod;

    /**
     * 项目总金额
     */
    @ApiModelProperty(value = "项目总金额")
    @ExcelProperty("项目总金额")
    private String totalProjectAmount;

    /**
     * 操作标识
     */
    @TableField(exist = false)
    private String oPFlag;

    @ApiModelProperty("部门人数")
    private Integer personCount;

    /**
     * sms的name
     */
    @ApiModelProperty(value = "sms的name")
    private String smsName;

    /**
     * sms的key
     */
    @ApiModelProperty(value = "sms的key")
    private String smsKey;

    /**
     * 模型别称
     */
    @ApiModelProperty(value = "模型别称")
    private String modelDataType;

    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private String modelName;

    /**
     * 模型别称
     */
    @ApiModelProperty(value = "模型别称")
    private List<String> modelDataTypeList;

    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private List<String> modelNameList;

    /**
     * 公文OID
     */
    @ApiModelProperty(value = "公文OID")
    private String oid;

    /**
     * 档案标识-统一信用代码
     */
    @ApiModelProperty(value = "档案标识-统一信用代码")
    private String creditCode;

    /**
     * 档案标识-代字编码
     */
    @ApiModelProperty(value = "档案标识-代字编码")
    private String dzCode;

    /**
     * 档案标识-组织机构编码
     */
    @ApiModelProperty(value = "档案标识-组织机构编码")
    private String organizationCode;

    /**
     * 档案标识-全宗号
     */
    @ApiModelProperty(value = "档案标识-全宗号")
    private String dossierNumber;

    @ApiModelProperty(value = "请示批件默认编号值")
    private String managementDefaultCode;

    @ApiModelProperty(value = "收文批件默认编号值")
    private String managementReceiveCode;

    @ApiModelProperty(value = "保密级别code")
    private String securityLevelCode;

    @ApiModelProperty(value = "保密级别code")
    private String securityLevelCodeName;

    @ApiModelProperty(value = "控制首页：个人中心的处事呈批件是否显示，0（默认）：不显示，1：显示")
    private String showHomeCpj;

    @ApiModelProperty(value = "机构唯一编码")
    private String soleCode;

    @ApiModelProperty(value = "行政区域编码")
    private String regionCode;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @ApiModelProperty(value = "最大编号")
    private Integer maxNumber;

    @ApiModelProperty(value = "商信机构ID")
    private String strId;

    @ApiModelProperty(value = "商信父级机构ID")
    private String strParentId;

    @ApiModelProperty(value = "商信机构信任号")
    private String strTrustNo;

    @ApiModelProperty(value = "商信父级机构信任号")
    private String strParentTrustNo;

    @ApiModelProperty(value = "卫士通统一身份认证系统组织机构ID")
    private String westoneOrgId;

    @ApiModelProperty(value = "卫士通统一身份认证系统上级组织机构ID")
    private String westoneOrgParentId;

    @ApiModelProperty(value = "跳转对应的服务ID")
    private Long serviceId;

    @ApiModelProperty("组织机构编码HMAC值，完整性校验")
    private String hmacOrgCode;

    @ApiModelProperty("组织机构编码是否完整，0-完整，1-不完整")
    private Integer dataIsIntegrity;

    @ApiModelProperty("存储从根节点到当前节点的完整层级code路径（如：A|B|C）")
    private String orgCodePath;

    @ApiModelProperty("存储从根节点到父节点的完整层级id路径（如：1|2）-- id特殊一点如果要存到当前节点，在维护的时候往往还需更新一次，影响性能")
    private String orgIdPath;

    @ApiModelProperty("orderByPath")
    private String orderByPath;

    @ApiModelProperty("上级机构名称")
    private String parentName;

    @ApiModelProperty("别名")
    private String aliasName;

    @ApiModelProperty("授权推送应用")
    private String pushAppCode;

    @ApiModelProperty("新增的推送应用")
    private List<String> addAppCodes;

    @ApiModelProperty("更新的推送应用")
    private List<String> updateAppCodes;

    @ApiModelProperty("被取消授权的推送应用")
    private List<String> removeAppCodes;

    @ApiModelProperty("龟儿子")
    private List<CscpOrgDTO> children;

    private Integer orgLevel;

    private Integer defaultDepartment;

    @ApiModelProperty("机构类型(0:省,1:市,2:县,3:乡镇)")
    private String orgLevelType;

    @ApiModelProperty("机构分类[00:默认;01:学校;02:医院]")
    private String orgClassify;

    @ApiModelProperty("值班电话")
    private String dutyPhone;
    @ApiModelProperty("传真电话")
    private String faxPhone;

    @ApiModelProperty("顶级至上级机构")
    private List<CscpOrgDTO> parentNodes;

    @ApiModelProperty("直属下级机构")
    private List<CscpOrgDTO> childNodes;

    private ResultDTO resultDTO;

    private Integer deleted;

    public void validate(CscpOrgService cscpOrgService) {
        String orgName = this.getOrgName();
        Long parentId = this.getParentId();
        Long id = this.getId();

        if (StringUtils.isEmpty(orgName) || parentId == null) {
            return;
        }

        List<CscpOrg> list = cscpOrgService.selectOrgByParentId(parentId);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (CscpOrg cscpOrg : list) {
            if ((cscpOrg.getDeleted() == null || cscpOrg.getDeleted() == 0)
                    && Objects.equals(orgName, cscpOrg.getOrgName())
                    && !Objects.equals(cscpOrg.getId(), id)) {
                throw new BusinessException("同一父级机构下已存在名称相同的机构：" + orgName);
            }
        }
    }

    public void buildAddAppCodes(String oldPushAppCode) {
        List<String> olds;
        List<String> news;
        if (StringUtils.isNotEmpty(oldPushAppCode)) {
            olds = Arrays.asList(oldPushAppCode.split(","));
        } else {
            olds = new ArrayList<>();
        }
        if (StringUtils.isNotEmpty(pushAppCode)) {
            news = Arrays.asList(pushAppCode.split(","));
        } else {
            news = new ArrayList<>();
        }
        addAppCodes = news.stream().filter(appCode -> !olds.contains(appCode)).collect(Collectors.toList());
        removeAppCodes = olds.stream().filter(appCode -> !news.contains(appCode)).collect(Collectors.toList());
        updateAppCodes = olds.stream().filter(news::contains).collect(Collectors.toList());
    }

    public void baseValid() {
        if (StringUtils.isNotEmpty(this.dutyPhone)) {
            try {
                String[] phones = this.dutyPhone.split(",");
                for (String phone : phones) {
                    if (!com.ctsi.hndx.utils.StringUtils.isPhone(phone)) {
                        throw new BusinessException("无效的值班电话:" + phone);
                    }
                }
            } catch (BusinessException e) {
                throw new BusinessException(e.toString());
            }
        }
        if (StringUtils.isNotEmpty(this.faxPhone)) {
            try {
                String[] phones = this.faxPhone.split(",");
                for (String phone : phones) {
                    if (!com.ctsi.hndx.utils.StringUtils.isPhone(phone)) {
                        throw new BusinessException("无效的传真电话:" + phone);
                    }
                }
            } catch (BusinessException e) {
                throw new BusinessException(e.toString());
            }
        }
    }
}
