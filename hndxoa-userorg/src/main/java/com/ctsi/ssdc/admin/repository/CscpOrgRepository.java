package com.ctsi.ssdc.admin.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.addrbook.entity.dto.TLabelOrgDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.database.annotation.InjectByDataBaseType;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Generator
 */
@InjectByDataBaseType(includes= {EnumDatabaseName.ORACLE})
public interface CscpOrgRepository extends MybatisBaseMapper<CscpOrg> {

    List<CscpOrg> selectorgList(Long id);

    @InterceptorIgnore(tenantLine = "true")
    List<TLabelOrgDTO> getChildOrgListNoAddTxl(@Param("orgId") Long orgId , @Param("labelIds") List<Long> labelIds);

    @InterceptorIgnore(tenantLine = "true")
    void updatePathCode(@Param("pathCode") String pathCode,@Param("newPathCode") String newPathCode,@Param("len") int len);

    Integer countUserByCompany(Long companyId);

    CscpOrg selectParentOrg(@Param("orgId") Long orgId);

    Integer countUserByOrg(Long orgId);
    Integer countUserByParentId(Long orgId);

    @InterceptorIgnore(tenantLine = "true")
    Integer countParentId(Long parentId,@Param("resultList")List<Long> resultList);

    @InterceptorIgnore(tenantLine = "true")
    List<Long> getLabelList(Long labelId);

    @InterceptorIgnore(tenantLine = "true")
    CscpOrg selectOrgByUserId(String strId);

    /**
     * 根据单位id查询所有部门
     */
    @InterceptorIgnore(tenantLine = "true")
    List<Long> selectAllDeptsByParentDeptId(@Param("parentDeptId") Long parentDeptId);

    @InterceptorIgnore(tenantLine = "true")
    void updateUnitTypeById(@Param("id") Long id);

    /**
     * 忽略删除标识,直接获取最大编码
     * @param parentId
     */
    @InterceptorIgnore(tenantLine = "true")
    CscpOrg getOneByParentIdMaxNumberDesc(@Param("parentId") Long parentId);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> getAllOrgCodeByParentId(@Param("parentId") Long parentId);

    /**
     * 根据单位id查询所有上级单位
     * @param orgId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectSelf2RootOrg(@Param("orgId") Long orgId);

    @InterceptorIgnore(tenantLine = "true")
    CscpOrg selectOrgLengthOne();

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgCityStateList(@Param("id") Long id);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgCityByOrgCode(@Param("orgCode") String orgCode);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgChildrenMaxNumber(@Param("codeList") Set<String> codeList);

    @InterceptorIgnore(tenantLine = "true")
    void updateRootPath();

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectAllWithChildPaths(@Param("codes") Set<String> codes);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgStartWith(@Param("id") Long id);

    @InterceptorIgnore(tenantLine = "true")
    CscpOrg getOrgIgnoreDeleted(@Param("id") Long orgId);
    /**
     * 查询内设机构所属单位id和信用代码
     */
    CscpOrg getCompanyInfoByDepartmentId(@Param("departmentId") Long departmentId);

    List<CscpOrg> selectOrgByCodePath(@Param("orgCodePath") String orgCodePath);

    /**
     * 查询子机构列表及其是否有下级机构状态
     * @param parentId 父机构ID
     * @return 子机构列表
     */
    List<CscpOrgDTO> selectSubOrgListWithChildrenStatus(@Param("parentId") Long parentId);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgByCodePath(@Param("id") Long id);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrgDTO> getAllOrgsFlat();
    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrgDTO> getFlatOrgList(@Param("id") Long id);


    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgChildrenByParentId(@Param("id") Long id);

    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpOrg> selectDeletedList(IPage<CscpOrg> page, @Param("orgName")String orgName, @Param("orgCode")String orgCode);


    @InterceptorIgnore(tenantLine = "true")
    boolean deleteRealById(@Param("id") Long id);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectChildrenByCondition(@Param("orgId") Long orgId, @Param("orgNameList") List<String> orgNameList);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectAllChild(@Param("partitionedIds")  List<List<Long>> partitionedIds);

    @InterceptorIgnore(tenantLine = "true")
    void updateAppCodesById(@Param("orgId") Long orgId, @Param("appCodes") String appCodes);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgUnitStartWithById(@Param("ids") List<Long> ids);

    @InterceptorIgnore(tenantLine = "true")
    void updateLocateAppCodeBatch(@Param("ids") List<Long> ids, @Param("appCode") String appCode);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpOrg> selectOrgHasDeleted(@Param("ids") List<Long> ids);
}
