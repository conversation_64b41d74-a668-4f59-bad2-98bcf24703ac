package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.addrbook.service.ITAddressBookOrgService;
import com.ctsi.hndx.enums.OrgTypeEnum;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.hndx.systenant.mapper.TSysTenantMapper;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.consts.ComponentConstant;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.cscpOrgUpdateCodeDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.service.OrgSaveStrategyService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


public abstract class BaseOrgSaveStrategyServiceImpl implements OrgSaveStrategyService {

    protected static final Long ROOT_ORG_ID = 0L; //最顶层机构默认父级ID

    protected static final Integer ORG_CODE_LEN = 4; //虚拟机构和单位的编码长度

    protected static final Integer ORG_CODE_EXTEND_LEN = 4; //部门编码扩张长度

    protected static final String ROOT_ORG_CODE = "0001"; //最顶层机构编码
    protected static final String ROOT_ORG_CODE_DEETAULT = "4300000000000000"; //最顶层机构默认编码

    protected static final String ROOT_ORG_PATH_CODE = "0001"; //最顶层机构完整编码

    protected static final String ORG_CODE_SUFFIX = "0001"; //新建下级部门，部门编码新增两位"0001"

    protected static final Integer ROOT_ORG_LEVEL = 1; //最顶层机构级别

    protected static final Integer FIRST_ORG_ORDER_BY = 1; //第一个机构排序号

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private ITAddressBookOrgService itAddressBookOrgService;

    @Autowired
    private TSysTenantMapper tSysTenantMapper;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    /**
     * 获取级别
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    public Integer getOrgLevel(CscpOrgDTO cscpOrgDTO) {
        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            return ROOT_ORG_LEVEL;
        }
        CscpOrg parentCscpOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        return parentCscpOrg.getLevel() + 1;
    }

    /**
     * 获取排序
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    public Integer getOrderBy(CscpOrgDTO cscpOrgDTO) {
        Long parentId = cscpOrgDTO.getParentId();
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpOrg::getParentId, parentId);
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(cscpOrgList)) {
            return FIRST_ORG_ORDER_BY;
        }
        // 获取最后一个虚拟单位的排序 + 1
        return cscpOrgList.get(cscpOrgList.size() - 1).getOrderBy() + 1;
    }

    /**
     * 修改组织机构排序：大于该机构排序号的所有排序号都 + 1
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    public boolean updateOrgSort(CscpOrgDTO cscpOrgDTO) {
        // 先查询当前单位下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
//        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCount(lambdaQueryWrapper);

        // 如果存在，则把该单位下的大于等于该排序的所有部门排序号 + 1
        if (count > 0) {
            LambdaUpdateWrapper<CscpOrg> queryWrapper = new LambdaUpdateWrapper();
//            queryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
//            queryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.set(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy() + 1);
            cscpOrgRepository.update(null, queryWrapper);
        }
        return true;
    }

    @Override
    public boolean crmUpdateOrgSort(CscpOrgDTO cscpOrgDTO) {
        // 先查询当前单位下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
//        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCount(lambdaQueryWrapper);

        // 如果存在，则把该单位下的大于等于该排序的所有部门排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
//            queryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
//            queryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
            lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            cscpOrgRepository.selectList(queryWrapper).stream().map(i -> {
                i.setOrderBy(i.getOrderBy() + 1);
                return cscpOrgRepository.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }

    protected Map<String, String> assembledMap(String curOrgCode, String curOrgCodePath, String curOrgIdPath) {
        Map<String, String> map = new HashMap<>();
        map.put("orgCode", curOrgCode);
        map.put("orgCodePath", curOrgCodePath);
        map.put("orgIdPath", curOrgIdPath);
        return map;
    }

    protected Map<String, String> regionMap(String regionCode, String orgCode, String maxNumber, String curOrgCodePath, String curOrgIdPath, String orderBy) {
        Map<String, String> map = new HashMap<>();
        map.put("regionCode", regionCode);
        map.put("orgCode", orgCode);
        map.put("maxNumber", maxNumber);
        map.put("orgCodePath", curOrgCodePath);
        map.put("orgIdPath", curOrgIdPath);
        map.put("orderBy", orderBy);
        return map;
    }

    /**
     * 保存用户机构关系
     *
     * @param cscpOrgDTO
     * @return
     */
    public Boolean saveUserOrgRel(CscpOrgDTO cscpOrgDTO) {
        return true;
    }

    public Boolean crmSaveUserOrgRel(CscpOrgDTO cscpOrgDTO,TSysTenant tSysTenant) {
        return true;
    }

    /**
     * 保存组织机构模板
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * todo 缓存机构
     * @CachePut(value = "orgCache-treeNodes", key = "#result.id", unless = "#result == null")
     * @Caching(
     *             put = @CachePut(value = "orgCache-treeNodes", key = "#result.id", unless = "#result == null"),
     *             evict = @CacheEvict(value = "orgCache-allTree", key = "'fullTree'")
     *     )
     */
    public CscpOrgDTO saveOrg(CscpOrgDTO cscpOrgDTO) {
        // 验证入参
        this.verifyOrgParam(cscpOrgDTO);

        // 获取编码和完整编码
        Map<String, String> map = this.getOrgCodeAndPathCode(cscpOrgDTO);
        Map<String, String> rMap = this.getRegionCode(cscpOrgDTO);
//        cscpOrgDTO.setCode(map.get("orgCode"));
        cscpOrgDTO.setCode("");
        cscpOrgDTO.setPathCode("");
        cscpOrgDTO.setOrgCodePath(rMap.get("orgCodePath"));
        cscpOrgDTO.setOrgIdPath(rMap.get("orgIdPath"));
        // 设置ID
        cscpOrgDTO.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        // 获取单位ID(可能会获取主键ID)
        CscpOrgDTO dto = this.getCompanyId(cscpOrgDTO);
        BeanUtils.copyProperties(dto, cscpOrgDTO);

        // 获取级别
        cscpOrgDTO.setLevel(this.getOrgLevel(cscpOrgDTO));

        // 获取排序(换成前端传)
        if (Objects.isNull(cscpOrgDTO.getOrderBy())) {
            cscpOrgDTO.setOrderBy(this.getOrderBy(cscpOrgDTO));
        }
        cscpOrgDTO.setOrderByPath(rMap.get("orderBy") + (100000 + cscpOrgDTO.getOrderBy()));

        // 修改排序号：大于该机构排序号的所有排序号都 + 1
        this.updateOrgSort(cscpOrgDTO);

        // 保存到组织机构表
        CscpOrg cscpOrg = new CscpOrg();
        BeanUtils.copyProperties(cscpOrgDTO, cscpOrg);
//        this.setPathCode(cscpOrgDTO);

        if(cscpOrgDTO.getModelDataTypeList()!=null && cscpOrgDTO.getModelDataTypeList().size()>0){
            String modelDataType = cscpOrgDTO.getModelDataTypeList().stream().collect(Collectors.joining(","));
            cscpOrg.setModelDataType(modelDataType);
        }

        if(cscpOrgDTO.getModelNameList()!=null && cscpOrgDTO.getModelNameList().size()>0){
            String modelName = cscpOrgDTO.getModelNameList().stream().collect(Collectors.joining(","));
            cscpOrg.setModelName(modelName);
        }

        cscpOrg.setTenantId(Long.parseLong(ComponentConstant.USER_TENANTID));

        cscpOrg.setSoleCode(SnowflakeIdUtil.getSnowFlakeLongId().toString());//生成唯一标识
        cscpOrg.setRegionCode(rMap.get("regionCode"));
        cscpOrg.setOrgCode(rMap.get("orgCode"));

        if (westoneEncryptService.isCipherMachine()) {
            if (null != cscpOrg.getOrgCode() && !"".equals(cscpOrg.getOrgCode())) {
                // TODO 计算SM3HMAC
                cscpOrg.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(cscpOrg.getOrgName() + cscpOrg.getOrgCode()));
            }
        }

        cscpOrg.setMaxNumber(Integer.parseInt(rMap.get("maxNumber")));
        Long id = cscpOrg.getId();
        if (id == null || id == 0){
            cscpOrg.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        }
        // 如果商信没有之用主键id补充
        if (StringUtils.isEmpty(cscpOrg.getStrId())){
            cscpOrg.setStrId(String.valueOf(cscpOrgDTO.getId()));
            cscpOrg.setStrParentId(String.valueOf(cscpOrgDTO.getParentId()));
            cscpOrg.setStrTrustNo(String.valueOf(cscpOrgDTO.getStrTrustNo()));   ;
            cscpOrg.setStrParentTrustNo(String.valueOf(cscpOrgDTO.getStrParentTrustNo()));   ;

        }


        cscpOrgRepository.insert(cscpOrg);

        BeanUtils.copyProperties(cscpOrg, cscpOrgDTO);

        // 保存用户机构关系
        this.saveUserOrgRel(cscpOrgDTO);

        return cscpOrgDTO;
    }

    private void setPathCode(CscpOrgDTO cscpOrgDTO) {
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpOrg::getPathCode, cscpOrgDTO.getPathCode());
        int count = cscpOrgRepository.selectCountNoAdd(lambdaQueryWrapper);

        // 如果存在，则把大于等于该排序的所有单位编码+1
        if (count > 0) {
            CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
            //获取小于当前机构排序号的数量
            LambdaQueryWrapper<CscpOrg> queryCountWrapper = Wrappers.lambdaQuery();
            queryCountWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryCountWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            final Integer[] orgCount = {cscpOrgRepository.selectCountNoAdd(queryCountWrapper)};
            orgCount[0] = orgCount[0] + 1;

            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            List<CscpOrg> cscpOrg = cscpOrgRepository.selectListNoAdd(queryWrapper);
            List<cscpOrgUpdateCodeDTO> list = new ArrayList<>();
            List<cscpOrgUpdateCodeDTO> finalList = list;
            cscpOrg.forEach(item -> {
                cscpOrgUpdateCodeDTO dto = new cscpOrgUpdateCodeDTO();
                String curOrgPathCode = "";
                if (parentOrg == null) {
                    curOrgPathCode = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgCount[0] + 1),
                            ORG_CODE_LEN);
                } else {
                    curOrgPathCode = parentOrg.getPathCode() + com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgCount[0] + 1),
                            ORG_CODE_LEN);
                }
                if (null == item.getPathCode()) {
                    return;
                }
                int len = item.getPathCode().length()+1;
                dto.setPathCode(item.getPathCode());
                dto.setNewPathCode(curOrgPathCode);
                dto.setLen(len);
                dto.setSort(orgCount[0]);
                finalList.add(dto);
//                cscpOrgRepository.updatePathCode(item.getPathCode(), curOrgPathCode,len);
                orgCount[0]++;
            });
            if(list!=null && list.size()>0){
                list = list.stream().sorted(Comparator.comparing(cscpOrgUpdateCodeDTO::getSort).reversed()).collect(Collectors.toList());
                list.forEach(item->{
                    cscpOrgRepository.updatePathCode(item.getPathCode(), item.getNewPathCode(),item.getLen());
                    //机构编码同步到用户机构中间表
                    cscpUserOrgRepository.updateByPathCode(item.getPathCode(), item.getNewPathCode(),item.getLen());
                });
            }
        }
    }

    /**
     * 领航保存组织机构模板
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CscpOrgDTO crmSaveOrg(CscpOrgDTO cscpOrgDTO,TSysTenant tSysTenant) {
        // 获取编码和完整编码
        Map<String, String> map = this.getOrgCodeAndPathCode(cscpOrgDTO);
        cscpOrgDTO.setCode("");
        cscpOrgDTO.setPathCode(map.get("orgPathCode"));
        cscpOrgDTO.setOrgIdPath(map.get("orgIdPath"));
        cscpOrgDTO.setOrgCodePath(map.get("orgCodePath"));


        // 获取单位ID(可能会获取主键ID)
        CscpOrgDTO dto = this.getCompanyId(cscpOrgDTO);
        BeanUtils.copyProperties(dto, cscpOrgDTO);

        // 获取级别
        cscpOrgDTO.setLevel(this.getOrgLevel(cscpOrgDTO));

        // 获取排序(换成前端传)
        if (Objects.isNull(cscpOrgDTO.getOrderBy())) {
            cscpOrgDTO.setOrderBy(this.getOrderBy(cscpOrgDTO));
        }

        // 修改排序号：大于该机构排序号的所有排序号都 + 1
        this.crmUpdateOrgSort(cscpOrgDTO);

        // 保存到组织机构表
        CscpOrg cscpOrg = new CscpOrg();
        BeanUtils.copyProperties(cscpOrgDTO, cscpOrg);
        cscpOrg.setTenantId(tSysTenant.getId());
        cscpOrg.setCreateBy(tSysTenant.getTenantLoginId());

        if (westoneEncryptService.isCipherMachine()) {
            if (null != cscpOrg.getOrgCode() && !"".equals(cscpOrg.getOrgCode())) {
                // TODO 计算SM3HMAC
                cscpOrg.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(cscpOrg.getOrgName() + cscpOrg.getOrgCode()));
            }
        }

//        cscpOrg.setCreateName(  );
        cscpOrgRepository.insert(cscpOrg);
        BeanUtils.copyProperties(cscpOrg, cscpOrgDTO);

        // 保存用户机构关系
        this.crmSaveUserOrgRel(cscpOrgDTO,tSysTenant);

        //生成本单位默认标签
        if (OrgTypeEnum.ORG_TYPE_2.getCode().equals(cscpOrgDTO.getType())) {
            //租户管理员新增标签生成本单位标签，允许该单位下的所有用户查看这个标签
            itAddressBookOrgService.crmGenerateDefaultLabel(cscpOrg.getId(), cscpOrg.getOrgName(),tSysTenant);
        } else if (OrgTypeEnum.ORG_TYPE_3.getCode().equals(cscpOrgDTO.getType())) {
            //部门和本单位标签绑定
            itAddressBookOrgService.generateOrgLabel(cscpOrg.getId(), cscpOrg.getOrgName(), cscpOrgDTO.getParentId());
        }

        return cscpOrgDTO;
    }

    @Override
    public CscpOrgDTO saveCompanyAdmin(CscpOrgDTO cscpOrgDTO) {
        this.saveUserOrgRel(cscpOrgDTO);
        return cscpOrgDTO;
    }
}
