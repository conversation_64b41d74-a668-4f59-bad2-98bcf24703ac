package com.ctsi.ssdc.admin.domain;


import com.baomidou.mybatisplus.annotation.*;
import com.ctsi.hndx.annotations.MybatisNoSelect;
import com.ctsi.hndx.mybatisplus.typerhander.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Generator
 */
@Data
@TableName(autoResultMap = true)
@ApiModel(value = "CscpUser", description = "用户表")
public class CscpUser implements Serializable {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cscp_user
     *
     * @mbg.generated Mon Apr 23 08:56:34 CST 2018
     */
    private static final long serialVersionUID = 1L;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_user.id
     *
     * @mbg.generated Mon Apr 23 08:56:34 CST 2018
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_user.username
     *
     * @mbg.generated Mon Apr 23 08:56:34 CST 2018
     */
    @ApiModelProperty(value = "登录用户名")
    @TableField(value = "login_name")
    private String loginName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_user.password
     *
     * @mbg.generated Mon Apr 23 08:56:34 CST 2018
     */
    // TODO 对密码进行SM4加密存储 2025-02-25
    @TableField(typeHandler = WestoneStrFullValueFromPlaintextDesHandler.class)
    @MybatisNoSelect
    private String password;


    /**
     * 濮?
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "real_name", typeHandler = WestoneStrFullFromBase64ValueDesHandler.class)
    private String realName;


    /**
     * 为兼容加密模糊查询，对姓名进行拆分，如果姓名为两位，拆分成两位，如果姓名为3为的，拆分1为和2,3为，4位个拆分一般
     * 例如  李四   拆分为  realNamestart  为李    realNameEnd  为四
     * 李四五  拆分为  realNamestart  为李    realNameEnd  为四五
     * 李四五六  拆分为  realNamestart  为李四    realNameEnd  为五六
     */
    @ApiModelProperty(value = "姓名的开头")
    @TableField(typeHandler = WestoneStrDivisionFromBase64ValueDesHandler.class)
    @MybatisNoSelect
    private String realNameStart;


    /**
     * 名
     */
    @ApiModelProperty(value = "名")
    @TableField(typeHandler = WestoneStrDivisionFromBase64ValueDesHandler.class)
    @MybatisNoSelect
    private String realNameEnd;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @TableField(typeHandler = WestoneNumberFullValueFromBase64DesHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private String mobile;


    /**
     * 电话开头4位
     */
    @ApiModelProperty(value = "电话中间")
    @TableField(typeHandler = WestoneNumberDivisionFromBase64DesHandler.class, updateStrategy = FieldStrategy.IGNORED)
    @MybatisNoSelect
    private String mobileStart;


    /**
     * 电话中间4位
     */
    @ApiModelProperty(value = "电话中间")
    @TableField(typeHandler = WestoneNumberDivisionFromBase64DesHandler.class, updateStrategy = FieldStrategy.IGNORED)
    @MybatisNoSelect

    private String mobileMiddle;


    /**
     * 电话结尾4位
     */
    @ApiModelProperty(value = "电话结尾")
    @TableField(typeHandler = WestoneNumberDivisionFromBase64DesHandler.class, updateStrategy = FieldStrategy.IGNORED)
    @MybatisNoSelect

    private String mobileEnd;


    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField(typeHandler = WestoneStrFullValueFromPlaintextDesHandler.class)
    private String email;

    /**
     * 最后一次登陆时间
     */
    @ApiModelProperty(value = "最后一次登陆时间")
    private LocalDateTime lastLogin;


    @TableField(fill = FieldFill.UPDATE, select = false)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.UPDATE, select = false)
    private Long updateBy;

    @TableField(fill = FieldFill.UPDATE, select = false)
    private String updateName;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private String createName;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    @TableLogic()
    @TableField(fill = FieldFill.INSERT, select = false)
    private Integer deleted;

    @ApiModelProperty(value = "用户全局排序号")
    private Integer orderBy;

    @ApiModelProperty(value = "用户状态：1表示激活，0表示锁定，默认激活")
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "用户是否显示")
    private Boolean display;

    @ApiModelProperty(value = "手机端app版本号")
    private Integer appVersion;

    @ApiModelProperty(value = "是否统计")
    private Boolean statistics;

    @ApiModelProperty(value = "办公电话")
    private String officePhone;

    @ApiModelProperty(value = "湘教通老师id")
    @TableField(exist = false)
    private String xjtTeacherId;

    /**
     * CRM受理的客户类型（单位类型：1：标准型，2：项目型）
     */
    @TableField(exist = false)
    private String crmTenantType;

    @ApiModelProperty(value = "主要资费")
    @TableField(value = "main_tariff")
    private String mainTariff;

    @ApiModelProperty(value = "附加资费")
    @TableField(value = "surcharge")
    private String surcharge;

    /**
     * 用户性别 0：男， 1：女
     */
    @ApiModelProperty(value = "用户性别 0：男， 1：女")
    @TableField(value = "sex")
    private Integer sex;

    /**
     * 是否手写签批  0-不是 1-是
     */
    @ApiModelProperty(value = "是否手写签批  0-不是 1-是")
    @TableField(value = "is_write_sign")
    private Integer isWriteSign;

    /**
     * 领航群组号
     */
    @TableField(exist = false)
    private String groupNumber;

    /**
     * 操作标识
     */
    @TableField(exist = false)
    private String oPFlag;

    /**
     * APP版本号
     */
    @ApiModelProperty(value = "APP版本号")
    private String appVersionName;

    /**
     * 撤回签批条件  0-下一步处理人未阅 1-无
     */
    @ApiModelProperty(value = "撤回签批条件  0-下一步处理人未阅 1-无")
    private Integer withdrawCondition;

    /**
     * 审批后短信提醒  0-否 1-是
     */
    @ApiModelProperty(value = "审批后短信提醒  0-否 1-是")
    private Integer auditSms;

    /**
     * 通讯录是否显示本单位  0-不是 1-是
     */
    @ApiModelProperty(value = "通讯录是否显示本单位  0-不是 1-是")
    private Integer isAddressUnit;

    /**
     * 备用手机号（用英文逗号分割号码）
     */
    @ApiModelProperty(value = "备用手机号（用英文逗号分割号码）")
    @TableField(typeHandler = WestoneBakMobileDivisionDesHandler.class)
    private String backupMobile;


    /**
     * 用于关联商信的ID
     */
    @ApiModelProperty(value = "商信ID")
    private String strId;

    /**
     * 用于关联数据所的ID
     */
    @ApiModelProperty(value = "数据所ID")
    private String sjsStrId;

    /**
     * 用户密级（10:普通　20:秘密　30:机密）
     */
    @ApiModelProperty(value = "用户密级（10:普通　20:秘密　30:机密）")
    private String strClassified;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号")
    private String strIdCardNo;


    /**
     * 单位信任号
     */
    @ApiModelProperty(value = "单位信任号")
    private String strUnitTrustNo;


    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源")
    private String userOrigin;

    /**
     * 签名图片url
     */
    @ApiModelProperty(value = "签名图片url")
    @TableField(value = "signature_image_url")
    private String signatureImageURL;

    @ApiModelProperty(value = "审核状态 0:待审核 1:通过 2:驳回")
    @TableField(value = "examine_status")
    private Integer examineStatus;

    @ApiModelProperty(value = "涉密类别code")
    @TableField(value = "security_classification_code")
    private String securityClassificationCode;

    @ApiModelProperty(value = "涉密类别code")
    @TableField(value = "security_classification_code_name")
    private String securityClassificationCodeName;


    @ApiModelProperty(value = "盖章图片地址")
    @TableField(value = "stamp_url")
    private String stampUrl;

    @ApiModelProperty(value = "起始号")
    @TableField(value = "start_no")
    private Integer startNo;

    @ApiModelProperty(value = "卫士通统一身份认证系统组织机构ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String westoneUserId;

    @ApiModelProperty("手机号码HMAC值，完整性校验")
    private String hmacMobile;

    @ApiModelProperty("身份证号")
    @TableField(value = "id_card_no", typeHandler = WestoneStrFullFromBase64ValueDesHandler.class)
    private String idCardNo;

    @ApiModelProperty("人员标签，多个标签用英文逗号分隔")
    private String personLabel;

    @ApiModelProperty("是否显示通讯录,0不显示，1显示")
    private Integer isDisplay;

    @ApiModelProperty("身份证号(禁用前")
    @TableField(value = "id_card_no_backup", typeHandler = WestoneStrFullFromBase64ValueDesHandler.class)
    private String idCardNoBackup;
    @ApiModelProperty("推送应用记录")
    private String pushAppCode;
    @ApiModelProperty("进入单位时间")
    private LocalDate entryTime;
    @ApiModelProperty("入职时间")
    private LocalDate offerTime;

    public void setNameHandle() {
        if (this.realName != null) {
            this.realName = this.realName.replaceAll(" ", "");
        }
        if (this.realNameStart != null) {
            this.realNameStart = this.realNameStart.replaceAll(" ", "");
        }
        if (this.realNameEnd != null) {
            this.realNameEnd = this.realNameEnd.replaceAll(" ", "");
        }
    }
}
