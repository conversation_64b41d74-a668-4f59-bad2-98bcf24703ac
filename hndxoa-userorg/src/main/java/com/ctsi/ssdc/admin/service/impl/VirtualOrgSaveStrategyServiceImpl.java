package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VirtualOrgSaveStrategyServiceImpl extends BaseOrgSaveStrategyServiceImpl {

    @Autowired
    private CscpOrgService cscpOrgService;
    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Override
    public Boolean verifyOrgParam(CscpOrgDTO cscpOrgDTO) {
        /*if (!SecurityUtils.isTenantName()){
            throw new BusinessException("只能由租户创建虚拟机构");
        }*/
//        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
//            throw new BusinessException("顶级机构只能是单位");
//        }
        if (!ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
//            if (Objects.isNull(parentOrg) || OrgTypeEnum.ORG_TYPE_3.getCode().equals(parentOrg.getType())) {
//                throw new BusinessException("虚拟机构的上级节点只能是单位或虚拟机构");
//            }
        }
        return true;
    }

    @Override
    public Map<String, String> getOrgCodeAndPathCode(CscpOrgDTO cscpOrgDTO) {

        // 因为虚拟机构的上级可以是单位或虚拟机构，查询所有虚拟机构和单位的 code 码
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
        queryWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);

        // 如果插入的虚拟机构为顶级
        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            if (CollectionUtils.isEmpty(orgList)) {
                String curOrgCode = ROOT_ORG_CODE;
                String curOrgCodePath = ROOT_ORG_CODE;
                String curOrgIdPath = "";
                return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
            }
            String curOrgCode = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgList.size() + 1),
                    ORG_CODE_LEN);
            String curOrgCodePath = curOrgCode;
            String curOrgIdPath = "";
            return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
        }

        // 如果插入的虚拟机构不是顶级，查询上级机构信息
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        if (StringUtils.isBlank(parentOrg.getCode()) && StringUtils.isBlank(parentOrg.getPathCode())) {
            throw new BusinessException("虚拟机构的上级节点单位编码和单位完整编码不能为空");
        }
        String parentOrgPathCode = parentOrg.getPathCode();

        // 这里不管多少级虚拟机构和单位的编码都为 4 位，规则可能需要修改
//        List<Long> orgCodeList = orgList.stream().map(org -> Long.parseLong(org.getCode()))
//                .collect(Collectors.toList());
//        Long maxOrgCode = Collections.max(orgCodeList);
        String curOrgCode = parentOrgPathCode;
        String parentOrgCodePath = parentOrg.getOrgCodePath();
        String parentOrgIdPath = parentOrg.getOrgIdPath();
        String curOrgCodePath = parentOrgCodePath + "|" + cscpOrgDTO.getOrgCode();
        String curOrgIdPath = StringUtils.isEmpty(parentOrgIdPath) ? parentOrg.getId().toString()
                : parentOrgIdPath + "|" + parentOrg.getId();
        return this.assembledMap(curOrgCode, curOrgCodePath, curOrgIdPath);
    }

    @Override
    public Map<String, String> getRegionCode(CscpOrgDTO cscpOrgDTO) {
        return this.getRegionCode_new(cscpOrgDTO);
    }

    @SuppressWarnings("all")
    public Map<String, String> getRegionCode_new(CscpOrgDTO cscpOrgDTO) {
        //获取上级机构信息
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        // 获取同级中最后一个机构信息
        CscpOrg org = cscpOrgService.getMaxCodeCscpOrg(cscpOrgDTO);
        // 如果是顶级单位
        if (cscpOrgDTO.getParentId().equals(0L)) {
            String orgCode = ROOT_ORG_CODE_DEETAULT;
            if (org != null) orgCode = org.getOrgCode();
            String strOrgCode = org.getOrgCode().trim();
            BigInteger bigInteger = new BigInteger(strOrgCode);
            BigInteger resultCode = bigInteger.add(BigInteger.ONE);
            String orgCodeStr = resultCode.toString();
            String maxNumber = orgCodeStr.substring(orgCodeStr.length() - 4);
            maxNumber = Integer.valueOf(maxNumber).toString();
            String curOrgCodePath = orgCodeStr;
            String curOrgIdPath = "";
            return this.regionMap("", orgCodeStr, maxNumber, curOrgCodePath, curOrgIdPath, "");
        }
        String orgCode = "";
        String strMaxNumber = "";
        if(org == null){
            orgCode = parentOrg.getOrgCode() + "0001";
            strMaxNumber = "1";
        }else{
            String strOrgCode = org.getOrgCode().trim();
            BigInteger bigInteger = new BigInteger(strOrgCode);
            BigInteger resultCode = bigInteger.add(BigInteger.ONE);
            String orgCodeStr = resultCode.toString();
            String maxNumber = orgCodeStr.substring(orgCodeStr.length() - 4);
            orgCode = orgCodeStr;
            strMaxNumber = String.valueOf(maxNumber);
        }
        String curOrgCodePath = parentOrg.getOrgCodePath() + "|" + orgCode;
        String curOrgIdPath = StringUtils.isEmpty(parentOrg.getOrgIdPath()) ? parentOrg.getId().toString()
                : parentOrg.getOrgIdPath() + "|" + parentOrg.getId();
        String orderBy = parentOrg.getOrderBy() != null ? (100000 + parentOrg.getOrderBy()) + "" : "";
        return this.regionMap(parentOrg.getRegionCode(), orgCode,strMaxNumber, curOrgCodePath, curOrgIdPath, orderBy);
    }

    @Override
    public CscpOrgDTO getCompanyId(CscpOrgDTO cscpOrgDTO) {

        // 如果虚拟机构为顶级节点，则其单位ID默认为0L
        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            cscpOrgDTO.setCompanyId(ROOT_ORG_ID);
            return cscpOrgDTO;
        }

        // 因为虚拟机构的父级可以是单位或者虚拟机构，单位ID必须为其父级单位的ID
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        int i = 0;
        // 或者找到父节点，或者直接报异常
        while (parentOrg.getParentId().longValue()!=0L) {
            if (parentOrg.getType() == 2) {
                cscpOrgDTO.setCompanyId(parentOrg.getId());
                break;
            } else {
                parentOrg = cscpOrgRepository.selectById(parentOrg.getParentId());
                i++;
                if (i == 5) {
                    throw new BusinessException("找不到虚拟机构的单位");
                }
            }
        }
        cscpOrgDTO.setCompanyId(parentOrg.getId());
        return cscpOrgDTO;
    }
    /**
     * 修改组织机构排序：大于该机构排序号的所有排序号都 + 1
     *
     * @param cscpOrgDTO
     * @return
     */
    @Override
    public boolean updateOrgSort(CscpOrgDTO cscpOrgDTO) {
        // 先查询当前单位下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
//        lambdaQueryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
//        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCountNoAdd(lambdaQueryWrapper);

        // 如果存在，则把该单位下的大于等于该排序的所有部门排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
//            queryWrapper.eq(CscpOrg::getCompanyId, cscpOrgDTO.getCompanyId());
//            queryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_3.getCode());
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            cscpOrgRepository.selectListNoAdd(queryWrapper).stream().map(i -> {
                i.setOrderBy(i.getOrderBy() + 1);
                return cscpOrgRepository.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }

}
