package com.ctsi.ssdc.controller;
import com.ctsi.ssdc.entity.dto.BizHrsUserInfoDetailDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

import com.ctsi.ssdc.entity.dto.BizHrsUserInfoDTO;
import com.ctsi.ssdc.service.IBizHrsUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;

import javax.servlet.http.HttpServletResponse;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizHrsUserInfo")
@Api(value = "人社厅人员表", tags = "人社厅人员表接口")
public class BizHrsUserInfoController extends BaseController {

    private static final String ENTITY_NAME = "bizHrsUserInfo";

    @Autowired
    private IBizHrsUserInfoService bizHrsUserInfoService;



    /**
     *  新增人社厅人员表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizHrsUserInfo.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增人社厅人员表批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizHrsUserInfo.add')")
    public ResultVO createBatch(@RequestBody List<BizHrsUserInfoDTO> bizHrsUserInfoList) {
       Boolean  result = bizHrsUserInfoService.insertBatch(bizHrsUserInfoList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizHrsUserInfo.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增人社厅人员表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizHrsUserInfo.add')")
    public ResultVO<BizHrsUserInfoDTO> create(@RequestBody BizHrsUserInfoDTO bizHrsUserInfoDTO)  {
        BizHrsUserInfoDTO result = bizHrsUserInfoService.create(bizHrsUserInfoDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizHrsUserInfo.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新人社厅人员表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizHrsUserInfo.update')")
    public ResultVO update(@RequestBody BizHrsUserInfoDTO bizHrsUserInfoDTO) {
	    Assert.notNull(bizHrsUserInfoDTO.getId(), "general.IdNotNull");
        int count = bizHrsUserInfoService.update(bizHrsUserInfoDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除人社厅人员表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizHrsUserInfo.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizHrsUserInfo.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizHrsUserInfoService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
   // @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizHrsUserInfoDTO bizHrsUserInfoDTO = bizHrsUserInfoService.findOne(id);
        return ResultVO.success(bizHrsUserInfoDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizHrsUserInfoPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
   // @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizHrsUserInfoDTO>> queryBizHrsUserInfoPage(BizHrsUserInfoDTO bizHrsUserInfoDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizHrsUserInfoService.queryListPage(bizHrsUserInfoDTO, basePageForm));
    }

   /**
    * 查询机构，分页
    */
   @GetMapping("/queryBizHrsUnitInfo")
   @ApiOperation(value = "查询人社厅机构，分页", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<PageResult<BizHrsUserInfoDTO>> queryBizHrsUnitInfo(BizHrsUserInfoDTO bizHrsUserInfoDTO, BasePageForm basePageForm) {
       return ResultVO.success(bizHrsUserInfoService.queryHrsUnitPage(bizHrsUserInfoDTO, basePageForm));
   }

   /**
    * 查询用户，分页
    */
   @GetMapping("/queryBizHrsUserInfoByStrId")
   @ApiOperation(value = "查询人社厅用户，分页", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<PageResult<BizHrsUserInfoDetailDTO>> queryBizHrsUserInfoByStrId(BizHrsUserInfoDTO bizHrsUserInfoDTO, BasePageForm basePageForm) {
       return ResultVO.success(bizHrsUserInfoService.queryBizHrsUserInfoPage(bizHrsUserInfoDTO, basePageForm));
   }

    /**
     * 同步人社厅用户信息到系统用户.
     */
    @PostMapping("/syncBizHrsUserInfo")
    @ApiOperation(value = "同步人社厅用户信息", notes = "传入人社厅单位名称和系统部门名称")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "同步人社厅用户信息")
    public ResultVO<List<BizHrsUserInfoDTO>> syncUserInfo(
            @RequestParam(name = "hrsUnitName", required = false, defaultValue = "") String hrsUnitName,
            @RequestParam(name = "deptName", required = false, defaultValue = "") String deptName) {
        List<BizHrsUserInfoDTO> list = bizHrsUserInfoService.syncUserInfo(hrsUnitName, deptName);
        return ResultVO.success(list);
    }

    /**
     * 导出所有人社厅人员数据，按单位分组生成Excel文件并打包下载
     */
    @GetMapping("/exportAllUsersByUnit")
    @ApiOperation(value = "导出所有人社厅人员数据按单位分组", notes = "异步导出所有数据并根据strUnitName分成一个个的excel文件名为strUnitName，并打包成压缩包，导出完成后可在导出记录中查看和下载")
    @OperationLog(dBOperation = DBOperation.ADD, message = "导出人社厅人员数据按单位分组")
    public ResultVO exportAllUsersByUnit(HttpServletResponse response) {
        try {
            Boolean result = bizHrsUserInfoService.exportAllUsersByUnit(response);
            if (result) {
                return ResultVO.success("导出任务已启动，请稍后在导出记录中查看导出结果");
            } else {
                return ResultVO.error("导出任务启动失败，请联系管理员");
            }
        } catch (Exception e) {
            log.error("启动导出任务失败", e);
            return ResultVO.error("导出任务启动失败：" + e.getMessage());
        }
    }



}
