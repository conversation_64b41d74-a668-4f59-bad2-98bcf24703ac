package com.ctsi.hndx.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

@Slf4j
@Service("jobTaskLock")
public class JobTaskLock {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    private static final String REDIS_JOBLOCK = "JobTaskLock:";

    // 确保初始化时设置key序列化器为StringRedisSerializer（关键！）
    @PostConstruct
    public void init() {
        if (redisTemplate.getKeySerializer() == null) {
            redisTemplate.setKeySerializer(new StringRedisSerializer());
        }
    }

    /**
     * 判断是否有定时任务锁
     * @param jobkey 任务锁key
     * @param time 有效时间（秒）
     * @return true：可执行任务；false：已存在锁
     */
    public boolean isJobTaskLock(String jobkey, long time) {
        String redisJobKey = REDIS_JOBLOCK + jobkey;
        try {
            // 强制使用原生Redis连接执行INCR，避免序列化问题
            Long ops = redisTemplate.execute(
                    (RedisCallback<Long>) connection -> connection.stringCommands().incr(
                            redisJobKey.getBytes(StandardCharsets.UTF_8)
                    )
            );
            // 设置过期时间（首次加锁时）
            if (ops != null && ops == 1) {
                // redisTemplate.expire(redisJobKey, time, TimeUnit.SECONDS);
                return true;
            }
            return false;
        } catch (Exception e) {
            // 处理序列化或命令执行异常（如value非数字）
            log.error("任务锁操作失败", e);
            return false;
        }
    }

    /**
     * 删除定时任务锁（修复键名拼接问题）
     * @param jobkey 任务锁key
     */
    public void delJobTaskLock(String jobkey) {
        // 修复：拼接前缀，确保与加锁时的key一致
        String redisJobKey = REDIS_JOBLOCK + jobkey;
        try {
            // 延迟10分钟删除（业务需求，若无需延迟可移除）
            Thread.sleep(1000 * 60 * 10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 保留中断状态
            return;
        }
        redisTemplate.delete(redisJobKey);
    }
}
