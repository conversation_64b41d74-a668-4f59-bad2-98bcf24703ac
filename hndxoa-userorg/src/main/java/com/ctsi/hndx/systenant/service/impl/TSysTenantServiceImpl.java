package com.ctsi.hndx.systenant.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.constant.UserConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.hndx.systenant.entity.dto.TSysTenantDTO;
import com.ctsi.hndx.systenant.entity.dto.TenantUserListDTO;
import com.ctsi.hndx.systenant.mapper.TSysTenantMapper;
import com.ctsi.hndx.systenant.service.ITSysTenantService;
import com.ctsi.hndx.systenant.service.ITTenantMenuService;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.tree.impl.BaseTreeCurdServiceImpl;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpSelectFuzzySearchUserList;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserRoleDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpRolesService;
import com.ctsi.ssdc.admin.service.CscpUserRoleService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */

@Slf4j
@Service
public class TSysTenantServiceImpl extends BaseTreeCurdServiceImpl<TSysTenantDTO, TSysTenant, TSysTenantMapper> implements ITSysTenantService {

    @Autowired
    private TSysTenantMapper tSysTenantMapper;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpUserRoleService cscpUserRoleService;


    @Autowired
    private CscpOrgService cscpOrgService;


    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private CscpRolesService cscpRolesService;

    @Autowired
    private ITTenantMenuService itTenantMenuService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    private ThreadLocal<Long> threadLocal = new ThreadLocal();

    /**
     * 租户根节点ID
     */
    private Long ROOT_TENANT_ID = 0L;


    /**
     * 翻页
     *
     * @param dto
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<TSysTenantDTO> pageTSysTenant(TSysTenantDTO dto, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TSysTenant> queryWrapper = new LambdaQueryWrapper();
        IPage<TSysTenant> pageData = tSysTenantMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                queryWrapper);
        //返回
        IPage<TSysTenantDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TSysTenantDTO.class));
        return data;
    }


    /**
     * 列表查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<TSysTenantDTO> findList(TSysTenantDTO dto) {
        LambdaQueryWrapper<TSysTenant> queryWrapper = new LambdaQueryWrapper();
        List<TSysTenant> tSysTenantList = tSysTenantMapper.selectList(queryWrapper);
        List<TSysTenantDTO> dtoList = ListCopyUtil.copy(tSysTenantList, TSysTenantDTO.class);
        for (TSysTenantDTO tSysTenantDTO : dtoList) {
            List<Long> roleIdList = cscpRolesService.queryRolesByTenantId(tSysTenantDTO.getId()).stream()
                    .map(x -> x.getId()).distinct().collect(Collectors.toList());
            tSysTenantDTO.setRoleIdList(roleIdList);
        }
        return dtoList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSysTenantDTO findOne(Long id) {
        TSysTenant tSysTenant = tSysTenantMapper.selectById(id);
        if (Objects.isNull(tSysTenant)) {
            return null;
        }
        TSysTenantDTO dto = new TSysTenantDTO();
        BeanUtils.copyProperties(tSysTenant, dto);

//        // 如果当前租户的快捷导航为空，需查询其父级租户快捷导航
//        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getCustomNavigation())) {
//            String customNavigations = this.getCustomNavigationByTenantId(id);
//            dto.setCustomNavigation(customNavigations);
//        }

        return dto;
    }


    /**
     * 新增
     *
     * @param dto the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSysTenantDTO create(TSysTenantDTO dto) {
        // 租户只能通过平台管理员创建
        if (!SecurityUtils.isSystemName()) {
            throw new BusinessException("租户只能由平台管理员创建");
        }
        // 设置是否PC端权限控制和是否移动端权限默认值为true
        if (Objects.isNull(dto.getPcAuthority())) {
            dto.setPcAuthority(true);
        }
        if (Objects.isNull(dto.getAppAuthority())) {
            dto.setAppAuthority(true);
        }

        TSysTenant entity = new TSysTenant();
        this.updateOrderBy(dto);
        BeanUtils.copyProperties(dto, entity);
        Long id = Long.valueOf(SnowflakeIdUtil.getSnowFlakeId());
        entity.setId(id);

        // 建租户管理员
        CscpUser user = new CscpUser();
        String name = SurnamePinyinUtil.getPinyin(dto.getTenantName(), "");
        String newName = cscpUserService.getUserNameMaxNumber(name + UserConstant.TENANT_LOGIN_NAME_PREFIXAL);
        user.setLoginName(newName);
        user.setRealName(dto.getTenantName() + "租户管理员");
        user.setMobile(SysConstant.DELAULT_MOBILE);
        user.setOrderBy(CscpUserDTO.DEFAULT_USER_SORT);
        String defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        user.setPassword(passwordEncoder.encode(defaultPassword));
        user.setStatistics(false);
        user.setDisplay(false);
        //名字
//         RealNameSplitType realNameSplitType = StringSplitUtils.realNameSplit(user.getRealName());
        //姓
        user.setRealNameStart(user.getRealName());
        //名
        user.setRealNameEnd(user.getRealName());

        //电话开头
        MobileSplitType mobileSplitType = StringSplitUtils.mobileSplit(user.getMobile());
        user.setMobileStart(mobileSplitType.getMobileStart());
        //电话中间
        user.setMobileMiddle(mobileSplitType.getMobileMiddle());
        //电话结尾
        user.setMobileEnd(mobileSplitType.getMobileEnd());
        cscpUserService.save(user);

        // 建租户
        entity.setTenantLoginId(user.getId());
        entity.setTenantLoginName(user.getLoginName());
        save(entity);

        // 租户管理员关联角色
        List<Long> roleIds = dto.getRoleIdList();
        roleIds.add(Long.valueOf(SystemRole.TENANT_ROLE.getId()));
        for (Long roleId : roleIds) {
            CscpUserRoleDTO cscpUserRoleDTO = new CscpUserRoleDTO();
            cscpUserRoleDTO.setRoleId(roleId);
            cscpUserRoleDTO.setUserId(user.getId());
            cscpUserRoleService.insert(cscpUserRoleDTO);
        }

        // 保存租户菜单关系
        itTenantMenuService.insertTenantMenus(dto.getMenuIdList(), id);

        //给当前租户默认建立一个单位，单位名称和租户名称相同，
        // 临时注释下，此处不再使用。
       /* CscpOrgDTO cscpOrgDTO = new CscpOrgDTO();
        cscpOrgDTO.setOrgName(dto.getTenantName());
        cscpOrgDTO.setParentId(0L);
        cscpOrgDTO.setOrderBy(1);
        cscpOrgDTO.setType(OrgTypeEnum.ORG_TYPE_2.getCode());
        cscpOrgDTO.setDescription(dto.getTenantName());
        cscpOrgDTO.setTenantId(entity.getId());

        cscpOrgService.save(cscpOrgDTO);*/
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 修改
     *
     * @param dto the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSysTenantDTO dto) {
        TSysTenant entity = new TSysTenant();
        this.updateOrderBy(dto);
        BeanUtils.copyProperties(dto, entity);
        int updateCount = tSysTenantMapper.updateById(entity);
        BeanUtils.copyProperties(entity, dto);

        // 保存租戶管理員和角色關係
        List<Long> roleIdList = dto.getRoleIdList();
        cscpUserRoleService.saveUserRoles(dto.getTenantLoginId(), roleIdList);

        // 保存租户菜单关系
        itTenantMenuService.insertTenantMenus(dto.getMenuIdList(), dto.getId());

        return updateCount;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 查询是否有组织机构与之关联
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrg::getTenantId, id);
        List<CscpOrg> cscpOrgList = cscpOrgService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(cscpOrgList)) {
            throw new BusinessException("请先删除相关联的组织机构信息！");
        }

        // 因为删除相关组织机构需要先删除组织机构关联的用户信息，所以这里可以直接删除租户关联的用户
        cscpUserService.deleteUserByTenantId(id);

        // 删除租户管理员帐号
        TSysTenantDTO tSysTenantDTO = this.findOne(id);
        if (Objects.nonNull(tSysTenantDTO.getTenantLoginId())) {
            cscpUserService.removeById(tSysTenantDTO.getTenantLoginId());
        }

        return tSysTenantMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSysTenantId
     * @return
     */
    @Override
    public boolean existByTSysTenantId(Long TSysTenantId) {
        if (TSysTenantId != null) {
            LambdaQueryWrapper<TSysTenant> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSysTenant::getId, TSysTenantId);
            List<TSysTenant> result = tSysTenantMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<TSysTenantDTO> dtoList) {
        List<TSysTenant> dataList = ListCopyUtil.copy(dtoList, TSysTenant.class);
        return saveBatch(dataList);
    }


    /**
     * 根据当前租户获取最顶层以下的所有租户
     *
     * @param id
     * @return
     */
    @Override
    public List<Node<TSysTenantDTO>> getTenants(Long id) {
        TSysTenant tSysTenant = tSysTenantMapper.selectById(TSysTenant.builder().id(id).build());
        List<Node<TSysTenantDTO>> list = new LinkedList();


        //传入的租户id不为空时，如果本身就是最顶层直接放回
        if (StringUtils.isNotNull(tSysTenant) && 0 == tSysTenant.getParentId()) {
            Node<TSysTenantDTO> tSysTenantDTONode = this.selectNodeByParentId(tSysTenant.getId());
            list.add(tSysTenantDTONode);
            return list;
        }

        //传入的租户id不为空时，递归查询到这个租户的顶级
        if (StringUtils.isNotNull(tSysTenant) && 0 != tSysTenant.getParentId()) {
            //递归,拿到顶级的租户id
            Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(tSysTenant.getParentId());
            Node<TSysTenantDTO> tSysTenantDTONode = this.selectNodeByParentId(topFloorTenamtId);
            list.add(tSysTenantDTONode);
            return list;
        }
        return list;
    }


    @Override
    public List<TSysTenantDTO> getDataDtOFromDomin(List<TSysTenant> list) {
        return ListCopyUtil.copy(list, TSysTenantDTO.class);
    }

    @Override
    public TSysTenantDTO copyDto(TSysTenant tSysTenant, TSysTenantDTO tSysTenantDTO) {
        tSysTenantDTO = new TSysTenantDTO();
        BeanUtils.copyProperties(tSysTenant, tSysTenantDTO);
        return tSysTenantDTO;
    }

    /**
     * 递归查询当前租户的最顶级
     *
     * @param id
     * @return
     */
    public Long getTopFloorTenamtId(Long id) {
        TSysTenant tSysTenant = tSysTenantMapper.selectById(TSysTenant.builder().id(id).build());
        if (StringUtils.isNotNull(tSysTenant) && 0 != tSysTenant.getParentId()) {
            return getTopFloorTenamtId(tSysTenant.getParentId());
        }
        return tSysTenant.getId();
    }


    /**
     * 获取该租户的所在的所有父子租户
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<TSysTenantDTO> getAllTenantList(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new BusinessException("tenantId不能为空");
        }
        List<TSysTenantDTO> sysTenantDTOList = new ArrayList<>();
        TSysTenantDTO tenantDTO = this.findOne(tenantId);
        if (Objects.isNull(tenantDTO)) {
            throw new BusinessException("没有找到该租户信息");
        }
        sysTenantDTOList.add(tenantDTO);

        // 查询其所有父租户信息
        while (!SysConstant.ROOT_TENANT_ID.equals(tenantDTO.getParentId())) {
            tenantDTO = this.findOne(tenantDTO.getParentId());
            sysTenantDTOList.add(tenantDTO);
        }

        // 获取本租户信息
        TSysTenantDTO sysTenantDTO = sysTenantDTOList.get(0);

        // 反转租户列表
        Collections.reverse(sysTenantDTOList);

        // 查询所有子租户信息
        List<TSysTenantDTO> childTenantList = this.getAllChildTenantList(sysTenantDTO.getId());
        sysTenantDTOList.addAll(childTenantList);

        return sysTenantDTOList;
    }

    /**
     * 查询所有子租户信息
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<TSysTenantDTO> getAllChildTenantList(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return new ArrayList<>();
        }

        // 存最底层的子租户
        List<TSysTenantDTO> tenantDTOList = new ArrayList<>();

        // 存具有子节点的租户
        LinkedList<TSysTenantDTO> parentTenantDTOList = new LinkedList<>();

        List<TSysTenantDTO> ChildTenantDTOList = this.getChildTenantList(tenantId);
        for (TSysTenantDTO sysTenantDTO : ChildTenantDTOList) {
            if (this.hasChildTenant(sysTenantDTO.getId())) {
                tenantDTOList.add(sysTenantDTO);
                parentTenantDTOList.add(sysTenantDTO);
            } else {
                tenantDTOList.add(sysTenantDTO);
            }
        }

        // 如果没有具有子节点的租户，则直接返回存最底层的子租户的集合即可
        if (CollectionUtils.isEmpty(parentTenantDTOList)) {
            return tenantDTOList;
        }

        // 如果子租户还有子节点，则继续遍历
        while (CollectionUtils.isNotEmpty(parentTenantDTOList)) {
            TSysTenantDTO tempTenantDTO = parentTenantDTOList.removeFirst();
            ChildTenantDTOList = this.getChildTenantList(tempTenantDTO.getId());
            for (TSysTenantDTO sysTenantDTO : ChildTenantDTOList) {
                if (this.hasChildTenant(sysTenantDTO.getId())) {
                    tenantDTOList.add(sysTenantDTO);
                    parentTenantDTOList.add(sysTenantDTO);
                } else {
                    tenantDTOList.add(sysTenantDTO);
                }
            }
        }
        return tenantDTOList;
    }

    /**
     * 查询子租户信息
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<TSysTenantDTO> getChildTenantList(Long tenantId) {
        LambdaQueryWrapper<TSysTenant> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TSysTenant::getParentId, tenantId);
        List<TSysTenant> childTenants = tSysTenantMapper.selectList(queryWrapper);
        return ListCopyUtil.copy(childTenants, TSysTenantDTO.class);
    }

    /**
     * 获取当前租户的父级租户信息
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<TSysTenantDTO> getAllFatherTenantList(Long tenantId) {
        List<TSysTenantDTO> sysTenantDTOList = new ArrayList<>();

        if (!Objects.isNull(tenantId)) {
            TSysTenantDTO tenantDTO = this.findOne(tenantId);
            if (Objects.isNull(tenantDTO)) {
                throw new BusinessException("没有找到该租户信息");
            }
            sysTenantDTOList.add(tenantDTO);

            // 查询其所有父租户信息
            while (!SysConstant.ROOT_TENANT_ID.equals(tenantDTO.getParentId())) {
                tenantDTO = this.findOne(tenantDTO.getParentId());
                sysTenantDTOList.add(tenantDTO);
            }

            // 获取本租户信息
            TSysTenantDTO sysTenantDTO = sysTenantDTOList.get(0);

            // 反转租户列表
            Collections.reverse(sysTenantDTOList);

            return sysTenantDTOList;
        }
        return sysTenantDTOList;
    }

    /**
     * 搜索获取全部租户下的用户
     *
     * @param userName
     */
    @Override
    public TenantUserListDTO getAllTenantUserList(String userName) {
        //通过当前用户获取所在租户信息，再获取它的上下级租户的id
        HashMap<Long, TSysTenant> tenantHashMap = getAllTenant();

        // 查询组装数据
        TenantUserListDTO tenantOrgUserList = this.getAllTenantOrgUserList(userName, tenantHashMap);
        return tenantOrgUserList;
    }

    private TenantUserListDTO getAllTenantOrgUserList(String userName, HashMap<Long, TSysTenant> tenantHashMap) {
        CscpSelectFuzzySearchUserList condition = null;
        if (StringUtils.isNull(userName) || "".equals(userName) || "null".equalsIgnoreCase(userName)) {
            List<TSysTenant> tenantList = new LinkedList<>();
            for (TSysTenant tenant : tenantHashMap.values()) {
                tenantList.add(tenant);
            }
            List<Tree<String>> tenantTree = this.assembledTreeNodeList(tenantList);
            return TenantUserListDTO.builder().cscpUsers(null).orgTree(null).tenantTree(tenantTree).build();
        } else {
            //根据条件查询对应的用户信息和单位部门
            condition = CscpSelectFuzzySearchUserList.builder()
                    .realName(this.division(userName))
                    .realNameStart(KeyCenterUtils.encrypt(userName))
                    .realNameEnd(KeyCenterUtils.encrypt(userName))
                    .tenantList(new LinkedList<>(tenantHashMap.keySet()))
                    .build();

            //查询指定租户下的所有用户（根据名称模糊查询）
            List<CscpUserDTO> cscpUsers = cscpUserRepository.selectFuzzySearchUserList(condition);

            //存放用户对应的机构
            LinkedList<CscpOrg> cscpOrgList = new LinkedList();
            //存放用户对应的租户
            List<TSysTenant> tenantList = new LinkedList<>();

            if (!cscpUsers.isEmpty()) {
                //查询用户对应的单位和部门信息，填充给对应的用户
                List<CscpOrg> companyList = cscpOrgRepository.selectListNoAdd(
                        new LambdaQueryWrapper<CscpOrg>()
                                .select(CscpOrg::getId, CscpOrg::getOrgName, CscpOrg::getParentId, CscpOrg::getOrderBy, CscpOrg::getPathCode, CscpOrg::getLevel)
                                .orderByAsc(CscpOrg::getLevel)
                                .orderByAsc(CscpOrg::getOrderBy)
                                .in(CscpOrg::getTenantId, tenantHashMap.keySet()));
                Map<Long, CscpOrg> orgMap = companyList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg -> CscpOrg));
                //填充
                cscpUsers.stream().forEach(i -> {
                    CscpOrg org = orgMap.get(i.getCompanyId());
                    if (org != null) {
                        i.setCompanyName(org.getOrgName());
                    }
                    CscpOrg orgByDepartment = orgMap.get(i.getCompanyId());
                    if (orgByDepartment != null) {
                        i.setDepartmentName(orgByDepartment.getOrgName());
                    }
                });
                cscpOrgList.addAll(companyList);
            }

            //获取对应用户的租户，反正出现重复的租户
            Set<Long> tenantSet = new HashSet<>();
            cscpUsers.stream().forEach(i -> {
                TSysTenant tenant = tenantHashMap.get(i.getTenantId());
                if (!tenantSet.contains(tenant.getId())) {
                    tenantSet.add(tenant.getId());
                    tenantList.add(tenant);
                }
            });

            //处理构建机构存在的问题
            //租户
            Map<Long, Long> collect = tenantList.stream().collect(Collectors.toMap(TSysTenant::getId, TSysTenant::getParentId));
            tenantList.forEach(i -> {
                if (!collect.containsKey(i.getParentId())) {
                    i.setParentId(0L);
                }
            });
            //机构
            Map<Long, Long> orgMap = cscpOrgList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg::getParentId));
            cscpOrgList.forEach(i -> {
                if (!orgMap.containsKey(i.getParentId())) {
                    i.setParentId(0L);
                }
            });

            //将租户和机构组装成树
            List<Tree<String>> tenantTree = this.assembledTreeNodeList(tenantList);
            List<Tree<String>> orgTree = this.assembledTreeNodeOrgList(cscpOrgList);

            return TenantUserListDTO.builder().cscpUsers(cscpUsers).orgTree(orgTree).tenantTree(tenantTree).build();
        }
    }

    /**
     * 搜索获取上下级租户下的用户
     *
     * @param userName
     */
    @Override
    public TenantUserListDTO getTenantUserList(String userName) {
        if (SecurityUtils.isSystemName()) {
            return null;
        }
        //通过当前用户获取所在租户信息，再获取它的上下级租户的id
        HashMap<Long, TSysTenant> tenantHashMap = getLevelTenant();

        // 查询组装数据
        TenantUserListDTO tenantOrgUserList = this.getTenantOrgUserList(userName, tenantHashMap);
        return tenantOrgUserList;
    }


    private TenantUserListDTO getTenantOrgUserList(String userName, HashMap<Long, TSysTenant> tenantHashMap) {
        //如果用户名称为空直接放回租户信息
       /* if (StringUtils.isNull(userName) || "".equals(userName) || "null".equalsIgnoreCase(userName)) {
            List<Tree<String>> tenantTree = this.assembledTreeNodeList(tenantHashMap.values().stream().collect(Collectors.toList()));
            return TenantUserListDTO.builder().cscpUsers(null).orgTree(null).tenantTree(tenantTree).build();
        }*/
        CscpSelectFuzzySearchUserList condition = null;
        if (StringUtils.isNull(userName) || "".equals(userName) || "null".equalsIgnoreCase(userName)) {

            condition = CscpSelectFuzzySearchUserList.builder()
                    .tenantList(new LinkedList<>(tenantHashMap.keySet()))
                    .build();
        } else {
            //根据条件查询对应的用户信息和单位部门
            //根据条件查询对应的用户信息和单位部门
            condition = CscpSelectFuzzySearchUserList.builder()
                    .realName(this.division(userName))
                    .realNameStart(KeyCenterUtils.encrypt(userName))
                    .realNameEnd(KeyCenterUtils.encrypt(userName))
                    .tenantList(new LinkedList<>(tenantHashMap.keySet()))
                    .build();
        }

        //查询指定租户下的所有用户（根据名称模糊查询）
        List<CscpUserDTO> cscpUsers = cscpUserRepository.selectFuzzySearchUserList(condition);

        //存放用户对应的机构
        LinkedList<CscpOrg> cscpOrgList = new LinkedList();
        //存放用户对应的租户
        List<TSysTenant> tenantList = new LinkedList<>();

        if (!cscpUsers.isEmpty()) {
            //查询用户对应的单位和部门信息，填充给对应的用户
            List<CscpOrg> companyList = cscpOrgRepository.selectListNoAdd(
                    new LambdaQueryWrapper<CscpOrg>()
                            .select(CscpOrg::getId, CscpOrg::getOrgName, CscpOrg::getParentId,CscpOrg::getOrderBy,CscpOrg::getPathCode,CscpOrg::getLevel)
                            .orderByAsc(CscpOrg::getLevel)
                            .orderByAsc(CscpOrg::getOrderBy)
                            .in(CscpOrg::getTenantId, tenantHashMap.keySet()));
            Map<Long, CscpOrg> orgMap = companyList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg -> CscpOrg));
            //填充
            cscpUsers.stream().forEach(i -> {
                CscpOrg org = orgMap.get(i.getCompanyId());
                if (org != null) {
                    i.setCompanyName(org.getOrgName());
                }
                CscpOrg orgByDepartment = orgMap.get(i.getCompanyId());
                if (orgByDepartment != null) {
                    i.setDepartmentName(orgByDepartment.getOrgName());
                }
            });
            cscpOrgList.addAll(companyList);
        }

        //获取对应用户的租户，反正出现重复的租户
        Set<Long> tenantSet = new HashSet<>();
        cscpUsers.stream().forEach(i -> {
            TSysTenant tenant = tenantHashMap.get(i.getTenantId());
            if (!tenantSet.contains(tenant.getId())) {
                tenantSet.add(tenant.getId());
                tenantList.add(tenant);
            }
        });

        //处理构建机构存在的问题
        //租户
        Map<Long, Long> collect = tenantList.stream().collect(Collectors.toMap(TSysTenant::getId, TSysTenant::getParentId));
        tenantList.forEach(i -> {
            if (!collect.containsKey(i.getParentId())) {
                i.setParentId(0L);
            }
        });
        //机构
        Map<Long, Long> orgMap = cscpOrgList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg::getParentId));
        cscpOrgList.forEach(i -> {
            if (!orgMap.containsKey(i.getParentId())) {
                i.setParentId(0L);
            }
        });

        //将租户和机构组装成树
        List<Tree<String>> tenantTree = this.assembledTreeNodeList(tenantList);
        List<Tree<String>> orgTree = this.assembledTreeNodeOrgList(cscpOrgList);

        return TenantUserListDTO.builder().cscpUsers(cscpUsers).orgTree(orgTree).tenantTree(tenantTree).build();
    }

    /**
     * 搜索获取本租户下的用户
     *
     * @param userName
     * @return
     */
    @Override
    public TenantUserListDTO getCurrentTenantUserList(String userName) {
        // 查询本租户信息
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        if (Objects.isNull(tenantId)) {
            return null;
        }
        TSysTenant tSysTenant = tSysTenantMapper.selectById(tenantId);
        if (Objects.isNull(tSysTenant)) {
            return null;
        }

        HashMap<Long, TSysTenant> tenantHashMap = new HashMap<>(1);
        tenantHashMap.put(tenantId, tSysTenant);
        return this.getTenantOrgUserList(userName, tenantHashMap);
    }

    /**
     * 模糊搜索获取上下级租户下的单位
     *
     * @param orgName
     * @return
     */
    @Override
    public TenantUserListDTO getTenantCompanyList(String orgName) {
        if (!SecurityUtils.isTenantName()) {
            return null;
        }
        //获取上下级租户
        //获取当前用户的租户信息
        HashMap<Long, TSysTenant> tenanthashMap = getLevelTenant();

        //如果租户名称为空直接放回租户信息
        if (StringUtils.isNull(orgName) || "".equals(orgName)) {
            List<Tree<String>> tenantTree = this.assembledTreeNodeList(tenanthashMap.values().stream().collect(Collectors.toList()));
            return TenantUserListDTO.builder().cscpUsers(null).orgTree(null).tenantTree(tenantTree).build();
        }

        //模糊查询对应的机构
        List<CscpOrg> companyList = cscpOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getId, CscpOrg::getOrgName, CscpOrg::getParentId, CscpOrg::getTenantId, CscpOrg::getType).in(CscpOrg::getTenantId, tenanthashMap.keySet()).like(CscpOrg::getOrgName, orgName).groupBy(CscpOrg::getId));

        //获取机构对应的租户
        Set<TSysTenant> tSysTenantSet = new HashSet<>();
        companyList.stream().forEach(i -> {
            if (tenanthashMap.containsKey(i.getTenantId())) {
                tSysTenantSet.add(tenanthashMap.get(i.getTenantId()));
            }
        });

        //处理parentId等于0的问题
        Map<Long, Long> collect = tSysTenantSet.stream().collect(Collectors.toMap(TSysTenant::getId, TSysTenant::getParentId));
        tSysTenantSet.forEach(i -> {
            if (!collect.containsKey(i.getParentId())) {
                i.setParentId(0L);
            }
        });

        //处理虚拟机构的问题
        Map<Long, CscpOrg> cscpOrgMap = companyList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg -> CscpOrg));
        companyList.forEach(i -> {
            threadLocal.set(i.getId());
            processingOrganization(i, cscpOrgMap);
            threadLocal.remove();
        });


        //将租户和机构组装成树
        List<Tree<String>> tenantTree = this.assembledTreeNodeList(new LinkedList<>(tSysTenantSet));
        List<Tree<String>> orgTree = this.assembledTreeNodeOrgList(new LinkedList<>(cscpOrgMap.values()));
        return TenantUserListDTO.builder().orgTree(orgTree).tenantTree(tenantTree).build();
    }

    /**
     * 处理机构parentId问题
     *
     * @param cscpOrg
     * @param cscpOrgMap
     */
    public void processingOrganization(CscpOrg cscpOrg, Map<Long, CscpOrg> cscpOrgMap) {
        //如果不包含，说明它没有父级
        if (!cscpOrgMap.containsKey(cscpOrg.getParentId())) {
            //查询对应的父级
            CscpOrg cscpOrg1 = cscpOrgRepository.selectOneNoAdd(
                    new LambdaQueryWrapper<CscpOrg>()
                            .select(CscpOrg::getId, CscpOrg::getOrgName, CscpOrg::getParentId, CscpOrg::getTenantId)
                            .eq(CscpOrg::getId, cscpOrg.getParentId()));
            if (!Objects.isNull(cscpOrg) && cscpOrg.getParentId() != 0) {
                processingOrganization(cscpOrg1, cscpOrgMap);
            } else {
                cscpOrgMap.get(threadLocal.get()).setParentId(0L);
            }
        } else {
            cscpOrgMap.get(threadLocal.get()).setParentId(cscpOrgMap.get(cscpOrg.getParentId()).getId());
        }

    }

    public void getTenantIdList(HashMap<Long, TSysTenant> tenantIdList, Long id) {
        //查询父级下的租户
        List<TSysTenant> tSysTenants = tSysTenantMapper.selectListNoAdd(
                new LambdaQueryWrapper<TSysTenant>()
                        .select(TSysTenant::getId, TSysTenant::getTenantName, TSysTenant::getParentId)
                        .eq(TSysTenant::getParentId, id)
        );

        if (!tSysTenants.isEmpty()) {
            tSysTenants.stream().forEach(i -> {
                tenantIdList.put(i.getId(), i);
            });
            tSysTenants.stream().forEach(i -> {
                getTenantIdList(tenantIdList, i.getId());
            });
        }
    }


    /**
     * 判断是否还有子租户
     *
     * @param tenantId
     * @return
     */
    private Boolean hasChildTenant(Long tenantId) {
        LambdaQueryWrapper<TSysTenant> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TSysTenant::getParentId, tenantId);
        Integer count = tSysTenantMapper.selectCount(queryWrapper);
        return count > 0;
    }


    /**
     * 组装租户树
     *
     * @param tSysTenants
     * @return
     */
    public List<Tree<String>> assembledTreeNodeList(List<TSysTenant> tSysTenants) {

        // 构建的整个树数据
        List<TreeNode<String>> treeNodeList = tSysTenants.stream().map(tSysTenant -> {

            // 单个树数据构建
            TreeNode<String> treeNode = new TreeNode<String>()
                    .setId(String.valueOf(tSysTenant.getId()))
                    .setParentId(String.valueOf(tSysTenant.getParentId()))
                    .setName(tSysTenant.getTenantName())
                    .setWeight(tSysTenant.getOrderBy());

            //处理模糊查询的机构父id不是0,就不组装树问题
            if (tSysTenant.getParentId() != 0) {
                List<TSysTenant> collect = tSysTenants.stream().filter(i -> i.getId().equals(tSysTenant.getParentId())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    treeNode.setParentId(String.valueOf(tSysTenant.getParentId()));
                } else {
                    treeNode.setParentId("0");
                }
            } else {
                treeNode.setParentId(String.valueOf(tSysTenant.getParentId()));
            }
            return treeNode;
        }).collect(Collectors.toList());

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("title");
        treeNodeConfig.setChildrenKey("children");
        // 最大递归深度
        treeNodeConfig.setDeep(6);

        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(treeNodeList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                });
        return treeNodes;
    }


    /**
     * 组装租户树
     *
     * @param cscpOrgsList
     * @return
     */
    public List<Tree<String>> assembledTreeNodeOrgList(List<CscpOrg> cscpOrgsList) {

        // 构建的整个树数据
        List<TreeNode<String>> treeNodeList = cscpOrgsList.stream().map(tSysTenant -> {

            Map<String, Object> map = new HashMap<>();
            map.put("type", tSysTenant.getLevel());

            // 单个树数据构建
            TreeNode<String> treeNode = new TreeNode<String>()
                    .setId(String.valueOf(tSysTenant.getId()))
                    .setParentId(String.valueOf(tSysTenant.getParentId()))
                    .setName(tSysTenant.getOrgName())
                    .setExtra(map)
                    .setWeight(tSysTenant.getOrderBy());
            return treeNode;
        }).collect(Collectors.toList());

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("title");
        treeNodeConfig.setChildrenKey("children");
        // 最大递归深度
        treeNodeConfig.setDeep(6);

        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(treeNodeList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    tree.putExtra("type", treeNode.getExtra().getOrDefault("type", null));
                });
        return treeNodes;
    }


    public HashMap<Long, TSysTenant> getLevelTenant() {
        //通过当前用户获取所在租户信息，再获取它的上下级租户的id
        HashMap<Long, TSysTenant> tenanthashMap = new HashMap<>();
        Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        TSysTenant tSysTenant = tSysTenantMapper.selectOneNoAdd(new LambdaQueryWrapper<TSysTenant>().select(TSysTenant::getId, TSysTenant::getTenantName, TSysTenant::getParentId).eq(TSysTenant::getId, topFloorTenamtId));
        tenanthashMap.put(tSysTenant.getId(), tSysTenant);
        //递归查询当前用户所在租户的上下级租户
        this.getTenantIdList(tenanthashMap, topFloorTenamtId);
        return tenanthashMap;
    }

    public HashMap<Long, TSysTenant> getAllTenant() {
        HashMap<Long, TSysTenant> tenanthashMap = new HashMap<>();
        List<TSysTenant> list = tSysTenantMapper.selectListNoAdd(new LambdaQueryWrapper<TSysTenant>().
                select(TSysTenant::getId, TSysTenant::getTenantName, TSysTenant::getParentId).eq(TSysTenant::getParentId, 0));
        list.forEach(tSysTenant->{
            tenanthashMap.put(tSysTenant.getId(), tSysTenant);
            //递归查询当前用户所在租户的上下级租户
            this.getTenantIdList(tenanthashMap, tSysTenant.getId());
        });
        return tenanthashMap;
    }

    @Override
    public String getCustomNavigationByTenantId(Long tenantId) {
        log.info("TTenantMenuServiceImpl.getCustomNavigationByTenantId tenantId = {}", tenantId);
        TSysTenant tenant = tSysTenantMapper.selectById(tenantId);
        if (Objects.isNull(tenant)) {
            throw new BusinessException("未找到该租户信息, 租户ID = " + tenantId);
        }
        String customNavigation = tenant.getCustomNavigation();

        // 如果当前租户快捷导航为空，则取其父级租户的快捷导航
        if (org.apache.commons.lang3.StringUtils.isBlank(customNavigation)) {
            while (!SysConstant.ROOT_TENANT_ID.equals(tenant.getParentId())) {
                tenant = tSysTenantMapper.selectById(tenant.getParentId());
                if (Objects.isNull(tenant)) {
                    throw new BusinessException("未找到该租户的父节点信息, 父节点租户ID = " + tenant.getParentId());
                }
                customNavigation = tenant.getCustomNavigation();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(customNavigation)) {
                    return customNavigation;
                }
            }
        }
        return customNavigation;
    }


    /**
     * 根据用户id获取其顶级租户信息
     *
     * @param userId
     * @return
     */
    @Override
    public TSysTenantDTO getRootTenantInfo(Long userId) {
        // 获取用户所在租户
        CscpUser cscpUser = cscpUserService.getById(userId);
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException("未找到该用户信息");
        }
        Long tenantId = cscpUser.getTenantId();
        if (Objects.isNull(tenantId)) {
//            throw new BusinessException("未找到该用户的租户信息");
            return null;
        }
        TSysTenantDTO tSysTenantDTO = this.findOne(tenantId);
        if (Objects.isNull(tSysTenantDTO)) {
//            throw new BusinessException("未找到该用户的租户信息");
            return null;
        }

        while (!ROOT_TENANT_ID.equals(tSysTenantDTO.getParentId())) {
            tSysTenantDTO = this.findOne(tSysTenantDTO.getParentId());
            if (Objects.isNull(tSysTenantDTO)) {
//                throw new BusinessException("未找到该用户的父级租户信息");
                return null;
            }
        }
        return tSysTenantDTO;
    }

    /**
     * 根据用户id获取其租户信息
     *
     * @param userId
     * @return
     */
    @Override
    public TSysTenantDTO getTenantInfoByUserId(Long userId) {
        CscpUser cscpUser = cscpUserService.getById(userId);
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException("未找到该用户信息");
        }
        TSysTenantDTO tSysTenantDTO = this.findOne(cscpUser.getTenantId());
        return tSysTenantDTO;
    }

    /**
     * 根据id查询下级租户，id为0时查询所有顶层租户
     *
     * @param id
     * @return
     */
    @Override
    public List<TSysTenantDTO> selectTenantById(Long id) {
        log.info("TSysTenantServicelmpl Long = {}", id);
        if (Objects.isNull(id)) {
            throw new BusinessException("租户id不能为空");
        } else {
            LambdaQueryWrapper<TSysTenant> tSysTenantLambdaQueryWrapper = Wrappers.lambdaQuery();
            if (id == 0 && SecurityUtils.isTenantName()) {
                id = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
                tSysTenantLambdaQueryWrapper.eq(TSysTenant::getId, id);
            } else {
                tSysTenantLambdaQueryWrapper.eq(TSysTenant::getParentId, id);
            }
            tSysTenantLambdaQueryWrapper.orderByAsc(TSysTenant::getOrderBy);
            List<TSysTenant> tSysTenantList = tSysTenantMapper.selectList(tSysTenantLambdaQueryWrapper);
            List<TSysTenantDTO> tSysTenantDTOList = ListCopyUtil.copy(tSysTenantList, TSysTenantDTO.class);
            //判断是否有下级租户并设置标识
            tSysTenantDTOList.forEach(i -> {
                if (this.hasChildTenant(i.getId())) {
                    i.setIsValuable(1);
                } else {
                    i.setIsValuable(0);
                }
            });
            return tSysTenantDTOList;
        }

    }


    /**
     * 排序号自增
     *
     * @param dto
     * @return
     */
    public Boolean updateOrderBy(TSysTenantDTO dto) {
        LambdaQueryWrapper<TSysTenant> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TSysTenant::getParentId, dto.getParentId());
        lambdaQueryWrapper.eq(TSysTenant::getOrderBy, dto.getOrderBy());
        int count = tSysTenantMapper.selectCount(lambdaQueryWrapper);
        if (count > 0) {
            tSysTenantMapper.updataSort(
                    SortEnum.builder()
                            .sort(dto.getOrderBy())
                            .parentId(dto.getParentId())
                            .id(dto.getId())
                            .tableName("t_sys_tenant")
                            .sortName("order_by")
                            .additionOrsubtraction("+")
                            .build());
        }
        return true;
    }


    /**
     * 获取对应租户下的所有用户
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<CscpUserDTO> selectTenantUsers(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new BusinessException("租户id不能为空!");
        }
        List<CscpUserDTO> cscpUsers = cscpUserRepository.selectTenantUsers(tenantId);
        return cscpUsers;
    }

    //分割字符串加密
    public String division(String realName){
        String realNames = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames;
    }
}
