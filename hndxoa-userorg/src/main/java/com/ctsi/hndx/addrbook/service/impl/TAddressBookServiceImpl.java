package com.ctsi.hndx.addrbook.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.addrbook.constant.AddrBookConstant;
import com.ctsi.hndx.addrbook.entity.TAddressBook;
import com.ctsi.hndx.addrbook.entity.TLabelOrg;
import com.ctsi.hndx.addrbook.entity.dto.*;
import com.ctsi.hndx.addrbook.mapper.TAddressBookMapper;
import com.ctsi.hndx.addrbook.mapper.TLabelOrgMapper;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.encryption.Base64Encrypt;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.enums.OrgTypeEnum;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRoleRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import com.ctsi.ssdc.util.RedisUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 通讯录 通讯录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */

@Slf4j
@Service
public class TAddressBookServiceImpl extends SysBaseServiceImpl<TAddressBookMapper, TAddressBook> implements ITAddressBookService {

    @Autowired
    private TAddressBookMapper tAddressBookMapper;

    @Autowired
    private TLabelOrgMapper tLabelOrgMapper;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private CscpUserRoleRepository cscpUserRoleRepository;

    @Autowired
    private ExportToExcelService exportToExcelService;

    @Autowired
    private TAddressBookLabelServiceImpl tAddressBookLabelService;
    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private OrgPathService orgPathService;

    private final static Base64Encrypt base64Encrypt = new Base64Encrypt();

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<QueryListPageDTO> queryListPage(QueryTAddressBookDTO entityDTO, BasePageForm basePageForm) {
        CscpUserDetail userDetail = SecurityUtils.getCurrentCscpUserDetail();
        if(!SecurityUtils.isSystemName() && !SecurityUtils.isRegionAdmin()){
            // 非admin用户
            entityDTO.setCompanyId(userDetail.getCompanyId());
        }
        if (SecurityUtils.isRegionAdmin() && entityDTO.getOrgId() == null){
            List<CscpOrgDTO> cscpOrgList = cscpOrgService.selectDivisionOrgById_new(null, null);
            if (CollectionUtils.isNotEmpty(cscpOrgList)){
                entityDTO.setRootOrgCodePath(cscpOrgList.stream().map(CscpOrgDTO::getOrgCodePath).distinct().collect(Collectors.toList()));
            }
        }

        if (entityDTO.getOrgId()!=null){
            entityDTO.setCompanyId(entityDTO.getOrgId());
        }

        if (StringUtils.isNotEmpty(entityDTO.getRealName())) {
            entityDTO.setRealPinYin(entityDTO.getRealName());
            entityDTO.setRealName(Arrays.asList(entityDTO.getRealName().split("")).stream().map(v -> KeyCenterUtils.encrypt(String.valueOf(v))).collect(Collectors.joining(",")));
        }

        if (StringUtils.isNotEmpty(entityDTO.getDefaultPhone())) {
            entityDTO.setDefaultPhone(Arrays.asList(entityDTO.getDefaultPhone().split("")).stream().map(v -> KeyCenterUtils.encrypt(String.valueOf(v))).collect(Collectors.joining(",")));
        }
        IPage<QueryListPageDTO> pageDTOIPage = tAddressBookMapper.queryListPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);

        //boolean isCipherMachine = sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);

        if (westoneEncryptService.isCipherMachine()) {
            pageDTOIPage.getRecords().stream().forEach(x -> {
                // TODO 数据完整性校验
                String hmacDefaultMobile = x.getHmacDefaultPhone();
                String hmacDefaultMobileSrc = x.getRealName() + x.getDefaultPhone();
                boolean isIntegrity = westoneEncryptService.compareSM3HMAC(hmacDefaultMobileSrc, hmacDefaultMobile);
                if (isIntegrity) {
                    x.setDataIsIntegrity(0);
                } else {
                    x.setDataIsIntegrity(1);
                }
                // 是否base64加密是则解密，否则不处理
                //if (isCipherMachine){
                //    x.setTelephone(westoneEncryptService.decryptMobilePhoneWithFPE(x.getTelephone()));
                //}else {
                //    x.setTelephone(base64Encrypt.decrypt(x.getTelephone()));
                //}
                // 用户姓名和手机号脱敏
                x.setRealName(DesensitizeUtil.desensitizedName(x.getRealName()));
                x.setDefaultPhone(DesensitizeUtil.desensitizedPhoneNumber(x.getDefaultPhone()));
            });
        }
        return pageDTOIPage;
    }

    /**
     * 判断一个字符是否是汉字
     * PS：中文汉字的编码范围：[\u4e00-\u9fa5]
     * @param c 需要判断的字符
     * @return 是汉字(true), 不是汉字(false)
     */
    public static boolean isChineseChar(String c) {
        return String.valueOf(c).matches("[\u4e00-\u9fa5]");

    }

    /**
     * 单个查询
     *
     * @param addressId the id of the entity
     * @param orgId
     * @return
     */
    @Override
    public FindOneDTO findOne(Long addressId, Long orgId) {
        //获取指定的通讯录数据
        TAddressBook tAddressBook = tAddressBookMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBook>()
                .eq(TAddressBook::getId, addressId));

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 数据完整性校验
            String hmacDefaultMobile = tAddressBook.getHmacDefaultPhone();
            String hmacDefaultMobileSrc = tAddressBook.getRealName() + tAddressBook.getDefaultPhone();
            boolean isIntegrity = westoneEncryptService.compareSM3HMAC(hmacDefaultMobileSrc, hmacDefaultMobile);
            if (!isIntegrity) {
                throw new BusinessException(ResultCode.DATA_NOT_INTEGRITY);
            }
        }

        //获取这个通讯录对应的单位部门信息
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUserOrg>()
                        .select(CscpUserOrg::getOrgId, CscpUserOrg::getCompanyId, CscpUserOrg::getPost)
                        .eq(CscpUserOrg::getUserId, tAddressBook.getUserId())
                        .eq(CscpUserOrg::getOrgId, orgId)
        );

        //通讯录对应的单位信息
        CscpOrg company = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getOrgName).eq(CscpOrg::getId, cscpUserOrgs.get(0).getCompanyId())).get(0);
        //通讯录对应的部门信息
        CscpOrg org = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getOrgName).eq(CscpOrg::getId, orgId)).get(0);

        //判断app是否显示手机号码和座机号码
//        Set<String> permissions = SecurityUtils.getCurrentCscpUserDetail().getPermissions();
        if (AddrBookConstant.NOT_WHETHER_SHOW.equals(tAddressBook.getWhetherShow())) {
            tAddressBook.setDefaultPhone(null);
            tAddressBook.setTelephone(null);
        }

        //构建对象
        FindOneDTO createTAddressBookDTO = BeanConvertUtils.copyProperties(tAddressBook, FindOneDTO.class);
        //部门名称
        createTAddressBookDTO.setOrgName(org.getOrgName());
        createTAddressBookDTO.setOrgId(org.getId());

        createTAddressBookDTO.setTelephone(tAddressBook.getTelephoneEncrypt1());
        //单位名称
        createTAddressBookDTO.setCompanyName(company.getOrgName());
        createTAddressBookDTO.setCompanyId(company.getId());
        //职务
        cscpUserOrgs.stream().forEach(i -> {
            if (i.getOrgId().longValue() == orgId.longValue() || i.getCompanyId().longValue() == orgId.longValue()) {
                createTAddressBookDTO.setJobTitle(i.getPost());
            }
        });

        return createTAddressBookDTO;
    }


    /**
     * 新增
     *
     * @param createTAddressBookDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public CreateTAddressBookDTO create(CreateTAddressBookDTO createTAddressBookDTO) {

        //如果这个类型是空的说明是自己需要同步（为了防止系统新增用户调用同步通讯录又新增了一个系统用户）
//        if (Objects.isNull(createTAddressBookDTO.getAddType())) {
//            //机构id
//            List<Long> longList = new LinkedList<>();
//            longList.add(createTAddressBookDTO.getOrgId());
//            //增加用户信息
//            String userName = PinyinUtil.getPinyin(createTAddressBookDTO.getRealName(), "");
//
//            CscpUserDTO build = CscpUserDTO.builder()
//                    .mobile(createTAddressBookDTO.getDefaultPhone())
//                    .realName(createTAddressBookDTO.getRealName())
//                    .loginName(userName)
//                    .post(createTAddressBookDTO.getJobTitle())
//                    .orgIdList(longList)
//                    .defaultDepart(createTAddressBookDTO.getOrgId())
//                    .orderBy(createTAddressBookDTO.getSort()).build();
//
//            //判断用户名是否存在
//            String newuserName = cscpUserService.getUserNameMaxNumber(userName);
//            build.setLoginName(newuserName);
//            build.setAddType(1);
//            CscpUserDTO cscpUserDTO = cscpUserService.insert(build);
//            createTAddressBookDTO.setUserId(cscpUserDTO.getId());
//        }
        TAddressBook tAddressBookId = tAddressBookMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBook>().select(TAddressBook::getId).eq(TAddressBook::getUserId, createTAddressBookDTO.getUserId()));
        if (Objects.nonNull(tAddressBookId)){
            return BeanConvertUtils.copyProperties(tAddressBookId, CreateTAddressBookDTO.class);
        }

        TAddressBook tAddressBook = BeanConvertUtils.copyProperties(createTAddressBookDTO, TAddressBook.class);

        //名字(全值加密)
        tAddressBook.setRealName(createTAddressBookDTO.getRealName());
        //姓(逗号隔开加密)
        tAddressBook.setLastNameEncrypt(createTAddressBookDTO.getRealName());
        //名字首字母拼音
        tAddressBook.setRealPinYin(PinyinUtil.getFirstLetter(tAddressBook.getRealName(), ""));

        //手机号(逗号隔开加密)
        tAddressBook.setDefaultPhoneEncrypt1(createTAddressBookDTO.getDefaultPhone());

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String hmacDefaultMobileSrc = createTAddressBookDTO.getRealName() + createTAddressBookDTO.getDefaultPhone();
            tAddressBook.setHmacDefaultPhone(westoneEncryptService.calculateSM3HMAC(hmacDefaultMobileSrc));
        }

        //座机电话开头(逗号隔开加密)
        if (StringUtils.isNotEmpty(createTAddressBookDTO.getTelephone())) {
            tAddressBook.setTelephoneEncrypt1(createTAddressBookDTO.getTelephone());
        }

        //秘书电话开头(逗号隔开加密)
        if (StringUtils.isNotEmpty(createTAddressBookDTO.getSecretaryPhone())) {
            tAddressBook.setReservePhoneEncrypt1(createTAddressBookDTO.getSecretaryPhone());
        }

        //处理可见范围出现的字符串拼接问题
        if (!Objects.isNull(tAddressBook) && StringUtils.isNotEmpty(tAddressBook.getAddressBookLableId())) {
            String[] split = tAddressBook.getAddressBookLableId().split(",");
            String str = Arrays.stream(split).filter(i -> StringUtils.isNotEmpty(i)).collect(Collectors.joining(","));
            tAddressBook.setAddressBookLableId(str);
        }

        if (Objects.isNull(createTAddressBookDTO.getWhetherShow())){
            tAddressBook.setWhetherShow(1);
        }

        save(tAddressBook);

        return BeanConvertUtils.copyProperties(tAddressBook, CreateTAddressBookDTO.class);
    }


    /**
     * 修改
     *
     * @param updateTAddressBook the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int update(CreateTAddressBookDTO updateTAddressBook,Integer type) {
//        if (Objects.isNull(updateTAddressBook.getAddType())) {
//            //查询这个用户对应的角色
//            List<Long> roleIdList = cscpUserRoleRepository.selectListNoAdd(
//                    new LambdaQueryWrapper<CscpUserRole>()
//                            .select(CscpUserRole::getRoleId)
//                            .eq(CscpUserRole::getUserId, updateTAddressBook.getUserId())
//            ).stream().map(i -> i.getRoleId()).collect(Collectors.toList());
//            //同步修改系统用户信息
//            List orgIdList = new LinkedList();
//            orgIdList.add(updateTAddressBook.getOrgId());
//            CscpUserDTO build = CscpUserDTO.builder()
//                    .mobile(updateTAddressBook.getDefaultPhone())
//                    .realName(updateTAddressBook.getRealName())
//                    .post(updateTAddressBook.getJobTitle())
//                    .orgIdList(orgIdList)
//                    .roleIds(roleIdList)
//                    .officePhone(updateTAddressBook.getTelephone())
//                    .AddType(1)
//                    .orderBy(updateTAddressBook.getSort()).build();
//            build.setId(updateTAddressBook.getUserId());
//            cscpUserService.update(build);
//        }

        TAddressBook tAddressBook = BeanConvertUtils.copyProperties(updateTAddressBook, TAddressBook.class);

        //如果id为空，可能是系统用户修改调用了改服务，需要查询到对应的id进行修改
        if (Objects.isNull(tAddressBook.getId())) {
            TAddressBook tAddressBookId = tAddressBookMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBook>().select(TAddressBook::getId).eq(TAddressBook::getUserId, tAddressBook.getUserId()));
            if (!Objects.isNull(tAddressBookId)) {
                tAddressBook.setId(tAddressBookId.getId());
                if (ObjectUtil.equals(type,1)){
                    tAddressBook.setWhetherShow(tAddressBookId.getWhetherShow());
                }
            } else {
                //增加用户已经到系统但不存在通讯录进行添加操作， 用于统一机构已经推送的用户使用
                this.create(updateTAddressBook);
                return 0;
            }
        } else {
            //判断是否修改了通讯录的可视范围，如果把可视范围全部取消掉使用一个替代字符1
            QueryLabelVisualRangePcDTO queryLabelVisualRangePcDTO = tAddressBookLabelService.queryLabelVisualRangePc(tAddressBook.getId());
            boolean b1 = !Objects.isNull(updateTAddressBook.getAddressBookLableId()) && (updateTAddressBook.getAddressBookLableId().split(",").length == 0 || !StringUtils.isNotEmpty(updateTAddressBook.getAddressBookLableId()));
            if (b1) {
                tAddressBook.setAddressBookLableId(new StringBuffer(updateTAddressBook.getAddressBookLableId()).append("1").toString());
            }
            //如果没有修改可视范围，不要进行修改
            String[] split = updateTAddressBook.getAddressBookLableId().split(",");
            Boolean b = !Objects.isNull(queryLabelVisualRangePcDTO)
                    && !Objects.isNull(queryLabelVisualRangePcDTO.getSelectedLabelId())
                    && queryLabelVisualRangePcDTO.getSelectedLabelId().size() == updateTAddressBook.getAddressBookLableId().split(",").length
                    && queryLabelVisualRangePcDTO.getSelectedLabelId().containsAll(Arrays.stream(split).map(i -> Long.valueOf(i)).collect(Collectors.toList()));
            if (b) {
                tAddressBook.setAddressBookLableId(null);
            }
        }

        //默认手机号(加密逗号隔开)
        tAddressBook.setDefaultPhoneEncrypt1(updateTAddressBook.getDefaultPhone());

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String hmacDefaultMobileSrc = tAddressBook.getRealName() + tAddressBook.getDefaultPhone();
            tAddressBook.setHmacDefaultPhone(westoneEncryptService.calculateSM3HMAC(hmacDefaultMobileSrc));
        }

        //姓名
        if (StringUtils.isNotEmpty(updateTAddressBook.getRealName())){
            //姓(逗号隔开加密)
            tAddressBook.setLastNameEncrypt(updateTAddressBook.getRealName());
            //名字首字母拼音
            tAddressBook.setRealPinYin(SurnamePinyinUtil.getPinyin(updateTAddressBook.getRealName(), ""));
        }

        //座机电话(加密逗号隔开)
        if (StringUtils.isNotEmpty(updateTAddressBook.getTelephone())) {
            tAddressBook.setTelephoneEncrypt1(updateTAddressBook.getTelephone());
        }

        //秘书电话(加密逗号隔开)
        if (StringUtils.isNotEmpty(updateTAddressBook.getSecretaryPhone())) {
            tAddressBook.setSecretaryPhoneEncrypt1(updateTAddressBook.getSecretaryPhone());
        }

        //修改这条通讯录对应单位的职位
//        if (StringUtils.isNotEmpty(updateTAddressBook.getJobTitle())) {
//            cscpUserOrgRepository.update(
//                    CscpUserOrg.builder().post(updateTAddressBook.getJobTitle()).build(),
//                    new LambdaQueryWrapper<CscpUserOrg>()
//                            .eq(CscpUserOrg::getCompanyId, SecurityUtils.getCurrentCscpUserDetail().getCompanyId())
//                            .eq(CscpUserOrg::getUserId, updateTAddressBook.getUserId())
//            );
//        }

        //处理可见范围出现的字符串拼接问题
        if (!Objects.isNull(tAddressBook) && StringUtils.isNotEmpty(tAddressBook.getAddressBookLableId())) {
            String[] split = tAddressBook.getAddressBookLableId().split(",");
            String str = Arrays.stream(split).filter(i -> StringUtils.isNotEmpty(i)).collect(Collectors.joining(","));
            tAddressBook.setAddressBookLableId(str);
        }

        //修改通讯录信息
        int addressBookCount = tAddressBookMapper.updateById(tAddressBook);

        return addressBookCount > 0 ? 1 : 0;
    }

    /**
     * 通讯录机构需要的分页查询(Pc and app)
     *
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<ResOrgAddressBookDTO> queryListPage(ReqOrgAddressBookDTO entityDTO, BasePageForm page) {

        //根据部门id或者单位id查询对应的用户数据
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUserOrg>()
                        .select(CscpUserOrg::getUserId, CscpUserOrg::getPost, CscpUserOrg::getOrderBy)
                        .eq(CscpUserOrg::getOrgId, entityDTO.getOrgId()));
        String encrypt = null;
        if(StringUtils.isNotEmpty(entityDTO.getRealName())){
            encrypt = KeyCenterUtils.encrypt(entityDTO.getRealName());
        }
        //app 里面只查询部门里面的人
        if (AddrBookConstant.NOT_WHETHER_SHOW.equals(entityDTO.getIsShowType())) {
            CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(entityDTO.getOrgId());
            if (cscpOrgDTO != null && !ObjectUtil.equals(cscpOrgDTO.getType(),3)){
                return new PageResult(null, 0l, 0l);
            }
        }
        IPage<TAddressBook> pageData = new Page<>();
        Integer count = tAddressBookMapper.selectAdressCount(entityDTO.getOrgId(),encrypt);
        Integer start = getStart(count, page);
        List<TAddressBook> addressBooks = tAddressBookMapper.selectAdressInfo(entityDTO.getOrgId(),encrypt,start,page.getPageSize());
        if(CollectionUtil.isNotEmpty(addressBooks)){
            addressBooks.stream().forEach(addressBook -> {
                addressBook.setRealName(KeyCenterUtils.decrypt(addressBook.getRealName()));
                addressBook.setTelephone(KeyCenterUtils.decrypt(addressBook.getTelephone()));
                addressBook.setDefaultPhone(KeyCenterUtils.decrypt(addressBook.getDefaultPhone()));
            });
            pageData.setRecords(addressBooks);
            pageData.setTotal(Long.valueOf(count));
            pageData.setCurrent(Long.valueOf(page.getCurrentPage()));

            //app查询的时候判断这条通讯录数据是否显示 不显示就不把默认电话放回
            if (AddrBookConstant.NOT_WHETHER_SHOW.equals(entityDTO.getIsShowType())) {
                pageData.setRecords(pageData.getRecords().stream().map(i -> {
                    if (AddrBookConstant.NOT_WHETHER_SHOW.equals(i.getWhetherShow())) {
                        i.setDefaultPhone(null);
                    }
                    return i;
                }).collect(Collectors.toList()));
            }


            //返回
            IPage<ResOrgAddressBookDTO> convert = pageData.convert(entity -> {
                ResOrgAddressBookDTO resOrgAddressBookDTO = BeanConvertUtils.copyProperties(entity, ResOrgAddressBookDTO.class);
                //判断当前通讯录是否和查询出来的机构信息相同，相同的话将这个机构的职务赋值给ResOrgAddressBookDTO
                cscpUserOrgs.stream().forEach(s -> {
                    if (entity.getUserId().longValue() == s.getUserId().longValue()) {
                        resOrgAddressBookDTO.setJobTitle(s.getPost());
                    }
                });
                return resOrgAddressBookDTO;
            });

            //填充部门和单位
            Set<Long> orgidSet = new HashSet<>();
            orgidSet.add(entityDTO.getOrgId());
            Map<Integer, Object> integerObjectMap = this.setAddressBookOrg(orgidSet);
            Map<Long, CscpUserOrg> longCscpUserOrgMap = (Map<Long, CscpUserOrg>) integerObjectMap.get(1);
            convert.getRecords().forEach(i -> {
                CscpUserOrg cscpUserOrg = longCscpUserOrgMap.get(i.getUserId());
                i.setOrgId(cscpUserOrg.getOrgId());
                i.setCompanyId(cscpUserOrg.getCompanyId());
            });
            //填充部门名称和单位名称
            Map<Long, CscpOrg> longCscpUserMap = (Map<Long, CscpOrg>) integerObjectMap.get(2);
            convert.getRecords().forEach(i -> {
                CscpOrg cscpOrg = longCscpUserMap.get(i.getCompanyId());
                i.setCompanyName(cscpOrg.getOrgName());
                CscpOrg cscpOrg1 = longCscpUserMap.get(i.getOrgId());
                i.setOrgName(cscpOrg1.getOrgName());
            });
            //if(page.getCurrentPage()>pageData.getCurrent()){
            //    List<ResOrgAddressBookDTO> nullDTO = new ArrayList<>();
            //    convert.setRecords(nullDTO);
            //
            //}
            return new PageResult(convert.getRecords(),
                    pageData.getTotal(), pageData.getCurrent());
        }else{
            return new PageResult(null,
                    0l, 0l);
        }
        /*//根据部门id或者单位id查询对应的用户数据
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUserOrg>()
                        .select(CscpUserOrg::getUserId, CscpUserOrg::getPost)
                        .eq(CscpUserOrg::getOrgId, entityDTO.getOrgId()));
//        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgRepository.queryListCscpUserOrg(entityDTO.getOrgId());
        if (cscpUserOrgs.isEmpty()) {
            return new PageResult<ResOrgAddressBookDTO>();
        }

        //查询哪些用户是已经停用了
        List<Long> userIdList = cscpUserRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUser>()
                        .select(CscpUser::getId)
                        .in(CscpUser::getId, cscpUserOrgs.stream().map(i -> i.getUserId()).collect(Collectors.toList()))
                        .eq(CscpUser::getDisplay, SysConstant.STATUS_IS_STOP)
        ).stream().map(i -> i.getId()).collect(Collectors.toList());

        //删除掉已经停用的用户
        List<CscpUserOrg> cscpUserOrgs1 = cscpUserOrgs.stream().filter(i -> !userIdList.contains(i.getUserId())).collect(Collectors.toList());

        IPage<TAddressBook> pageData = new Page<>();
        if (!cscpUserOrgs1.isEmpty()) {
            //设置条件
            LambdaQueryWrapper<TAddressBook> queryWrapper = new LambdaQueryWrapper();
            queryWrapper
                    .in(TAddressBook::getUserId, cscpUserOrgs1.stream().map(i -> i.getUserId()).collect(Collectors.toList()))
                    .eq(StringUtils.isNotEmpty(entityDTO.getRealName()), TAddressBook::getRealName, KeyCenterUtils.encrypt(entityDTO.getRealName()))
                    .or()
                    .eq(StringUtils.isNotEmpty(entityDTO.getRealName()), TAddressBook::getLastNameEncrypt, KeyCenterUtils.encrypt(entityDTO.getRealName()))
                    .or()
                    .eq(StringUtils.isNotEmpty(entityDTO.getRealName()), TAddressBook::getFirstNameEncrypt,
                            KeyCenterUtils.encrypt(entityDTO.getRealName()))
                    .orderByAsc(TAddressBook::getSort)
            ;


            pageData = tAddressBookMapper.selectPageNoAdd(
                    PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);

            //app查询的时候判断这条通讯录数据是否显示 不显示就不把默认电话放回
            Set<String> permissions = SecurityUtils.getCurrentCscpUserDetail().getPermissions();
            if (AddrBookConstant.NOT_WHETHER_SHOW.equals(entityDTO.getIsShowType())) {
                pageData.setRecords(pageData.getRecords().stream().map(i -> {
                    if (AddrBookConstant.NOT_WHETHER_SHOW.equals(i.getWhetherShow()) && !permissions.contains("cscp.person.edit")) {
                        i.setDefaultPhone(null);
                    }
                    return i;
                }).collect(Collectors.toList()));
            }
        }*/
    }

    private Integer getStart(Integer count ,BasePageForm basePageForm) {

        Integer start = 0;
        Integer pageSize = basePageForm.getPageSize();
        Integer pageCount = count%pageSize == 0 ? count/pageSize : count/pageSize + 1;

        Integer currentPage = basePageForm.getCurrentPage();
        if(pageCount == 0){
            start = 0;
        }else{
            if(currentPage < 1){
                start = 0;
            }else if(currentPage <= pageCount){
                start = (currentPage - 1)*pageSize;
            }else{
                start = (pageCount - 1)*pageSize;
            }
        }

        return start;
    }

    @Override
    public PageResult<QueryAddressBookUserDTO> queryListPagePC(ReqOrgAddressBookDTO entityDTO, BasePageForm page) {
        // 来源于标签id
        Long labelId = entityDTO.getLabelId();
        Long orgId = entityDTO.getOrgId();
        // 1 获取 所有机构id ** 需要过滤
        // 如果label为空 怎么办
        // 需要展示的ids
        List<Long> showIds = cscpOrgService.getAllChildOrgIds(orgId, ListUtil.toList(labelId));
        if(orgId == null){
            //2.获取到所有标签下的机构
            List<TLabelOrg> tLabelOrgs = tLabelOrgMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TLabelOrg>()
                            .select(TLabelOrg::getOrgId)
                            .in(TLabelOrg::getDisplayRangeId, ListUtil.toList(labelId))
                            .groupBy(TLabelOrg::getOrgId)
            );
            // 2 找到所有组织机构id  <移动端全局搜索## >
            showIds = tLabelOrgs.stream().map(i -> i.getOrgId()).collect(Collectors.toList());
        }
        QueryTAddressBookConditionDTO build = QueryTAddressBookConditionDTO.builder()
                .labelIdList(ListUtil.toList(labelId))
                .orgId(showIds.stream().collect(Collectors.toSet()))
                // .realName(KeyCenterUtils.division(entityDTO.getRealName()))
                .defaultPhone(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(entityDTO.getDefaultPhone()) : KeyCenterUtils.division(entityDTO.getDefaultPhone()))
                .realName(westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(entityDTO.getRealName()) : KeyCenterUtils.division(entityDTO.getRealName()))
                .orgIdCondition(showIds)
                .build();
        // if(StrUtil.isNotEmpty(entityDTO.getRealName())){
        //     build.setRealName(Arrays.asList(entityDTO.getRealName().split(""))
        //             .stream().map(v -> KeyCenterUtils.division(v)).collect(Collectors.joining(",")) );
        // }
        // if(StrUtil.isNotEmpty(entityDTO.getDefaultPhone())){
        //     build.setDefaultPhone(Arrays.asList(entityDTO.getDefaultPhone().split(""))
        //             .stream().map(v -> KeyCenterUtils.division(v)).collect(Collectors.joining(",")));
        // }

        if(showIds.isEmpty()){
            return new PageResult(new ArrayList<QueryAddressBookUserDTO>(),
                    0, page.getPageSize());
        }

        //3.根据人名手机号机构查询
        List<QueryAddressBookUserDTO> queryAddressBookUserList =
                tAddressBookMapper.querytaddressbookuserPc(build);

        Integer pageSize = page.getPageSize();
        Integer pageNumber = page.getCurrentPage();
        // users.stream().skip((index - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        List<QueryAddressBookUserDTO> data =
                queryAddressBookUserList.stream().skip((pageNumber - 1) * pageSize).limit(pageSize)
                .collect(Collectors.toList());
        Set<String> permissions = SecurityUtils.getCurrentCscpUserDetail().getPermissions();
        //查询的时候判断这条通讯录数据是否显示 不显示就不把默认电话放回

        if (CollectionUtil.isNotEmpty(data)){
            data.stream().forEach(queryAddressBookUserDTO -> {
                if (AddrBookConstant.NOT_WHETHER_SHOW.equals(queryAddressBookUserDTO.getWhetherShow())) {
                    queryAddressBookUserDTO.setDefaultPhone(null);
                }
            });
        }
        return new PageResult(data,
                queryAddressBookUserList.size(), page.getPageSize());
    }

    /**
     * 通讯录导出
     *
     * @param response
     * @return
     */
    @Override
    public Boolean fileExport(HttpServletResponse response, Long orgId) {
        BasePageForm basePageForm = new BasePageForm();
        basePageForm.setCurrentPage(1);
        basePageForm.setPageSize(Integer.MAX_VALUE);
        QueryTAddressBookDTO dto = new QueryTAddressBookDTO();
        if(orgId!=null) {
            dto.setOrgId(orgId);
        }
        IPage<QueryListPageDTO> queryListPageDTOIPage = this.queryListPage(dto, basePageForm);
        // 导出数据用户姓名和手机号脱敏处理
        List<QueryListPageDTO> exportDTOList = queryListPageDTOIPage.getRecords().stream().map(x -> {
            x.setRealName(DesensitizeUtil.desensitizedName(x.getRealName()));
            x.setDefaultPhone(DesensitizeUtil.desensitizedPhoneNumber(x.getDefaultPhone()));
            return x;
        }).collect(Collectors.toList());
        Boolean bool = null;
        try {
            bool = exportToExcelService.exportToExcel(exportDTOList, QueryListPageDTO.class, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bool;
    }

    /**
     * 条件查询标签下面所有的通讯录，分页
     *
     * @param queryTAddressBookDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<QueryListPageDTO> queryTAddressBookAllPage(QueryTAddressBookAllDTO queryTAddressBookDTO, BasePageForm basePageForm) {
        //查询标签下的所有机构
        List<TLabelOrg> tLabelOrgs = tLabelOrgMapper.selectList(
                new LambdaQueryWrapper<TLabelOrg>()
                        //只需要获取orgId数据
                        .select(TLabelOrg::getOrgId)
                        .eq(TLabelOrg::getId, queryTAddressBookDTO.getLabelId())
        );

        //如果标签下没有机构直接放回null
        if (tLabelOrgs.isEmpty()) {
            return new PageResult<>();
        }

        //查询机构下的所有通讯录id
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgRepository.selectList(
                new LambdaQueryWrapper<CscpUserOrg>()
                        .select(CscpUserOrg::getUserId)
                        .in(CscpUserOrg::getOrgId, tLabelOrgs.stream().map(i -> i.getOrgId()).collect(Collectors.toList()))
        );


        //查询所有机构下面的所有用户分页查询
        if (cscpUserOrgs.isEmpty()) {
            return new PageResult<>();
        }

        IPage<TAddressBook> pageData = tAddressBookMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                new LambdaQueryWrapper<TAddressBook>()
                        .in(TAddressBook::getUserId, cscpUserOrgs.stream().map(i -> i.getUserId()).collect(Collectors.toList()))
                        //姓名模糊查询
                        .like(StringUtils.isNotEmpty(queryTAddressBookDTO.getRealName()), TAddressBook::getLastNameEncrypt, Arrays.asList(queryTAddressBookDTO.getRealName().split("")).stream().map(v -> KeyCenterUtils.encrypt(String.valueOf(v))).collect(Collectors.joining(",")))
                        //电话模糊查询
                        .like(StringUtils.isNotEmpty(queryTAddressBookDTO.getDefaultPhone()), TAddressBook::getDefaultPhoneEncrypt1, Arrays.asList(queryTAddressBookDTO.getDefaultPhone().split("")).stream().map(v -> westoneEncryptService.isCipherMachine() ? westoneEncryptService.encryptMobilePhoneWithFPE(v) : KeyCenterUtils.encrypt(v)).collect(Collectors.joining(",")))
        );

        IPage<QueryListPageDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, QueryListPageDTO.class));

        return new PageResult<>(data.getRecords(), pageData.getTotal(), pageData.getCurrent());
    }


    /**
     * NEW 根据用户名称，手机号模糊，机构，拼音搜索（app）
     *
     * @param nameOrPhone
     * @return
     */
    @Override
    public IPage<QueryAddressBookUserDTO> queryTAddressBookUserNew(String nameOrPhone,IPage iPage) {

        // 获取当前用户标签机构
        List<TAddressBookLabelDTO> list = tAddressBookLabelService.queryTAddressBookLabelApp();

        if (CollectionUtils.isEmpty(list)) {
            return new Page<>();
        }

        // 提取所有的id
        List<Long> ids = list.stream().map(TAddressBookLabelDTO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return new Page<>();
        }

        // 获取机构编码路径
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CscpOrg::getId, CscpOrg::getOrgCodePath).in(CscpOrg::getId, ids);
        List<CscpOrg> orgList = cscpOrgRepository.selectListOnlyAddTenantId(queryWrapper);
        Map<Long, String> orgCodePathMap = orgList.stream()
                .collect(Collectors.toMap(
                        CscpOrg::getId,
                        CscpOrg::getOrgCodePath,
                        (existing, replacement) -> {
                            log.info("发现重复的orgId: {}, 保留第一个值", existing);
                            return existing;
                        }
                ));

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        String currentOrgCode = null;
        String highestOrgCode = null;
        // 先检查是否存在 id=0 的情况，等于0表示可搜索全省
        boolean hasZeroId = list.stream().anyMatch(dto -> dto.getId() != null && dto.getId() == 0L);
        if (!hasZeroId) {
            for (TAddressBookLabelDTO dto : list) {
                Long id = dto.getId();
                if (id == null) {
                    log.error("通讯录标签ID不能为空:{}",dto);
                    continue;
                }
                String orgCodePath = orgCodePathMap.get(id);

                if (orgCodePath != null) {
                    if (Objects.equals(id, currentCompanyId)) {
                        currentOrgCode = orgCodePath;
                    } else {
                        highestOrgCode = orgCodePath;
                    }
                }
            }
        }

        // 搜索字段加密处理
        long encryptStart = System.currentTimeMillis();
        String searchKey = westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(nameOrPhone) : KeyCenterUtils.division(nameOrPhone);
        log.info("加密处理耗时: {}ms", (System.currentTimeMillis() - encryptStart));

        IPage<QueryAddressBookUserDTO> result;
        // 判断搜号码还是姓名或机构名
        long searchTypeStart = System.currentTimeMillis();
        // 判断内外网标志：in-内网，out-外网
        boolean outNetWorkFlag = sysConfigService.isWwBySysConfig();
        if (StrUtil.isNumeric(nameOrPhone)) {
            // 手机号搜索
            if (outNetWorkFlag) {
                result = tAddressBookMapper.selectAddressBookUserFullTextSearch(iPage, searchKey, null, null, currentOrgCode, highestOrgCode);
            } else {
                result = tAddressBookMapper.selectAddressBookUserBasic(iPage, searchKey, null, null, currentOrgCode, highestOrgCode);
            }
        } else {
            // 用户姓名或机构名搜索
            String value = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SEARCH_ORG);
            if (!StrUtil.equals(value, "1")) {
                // 不等于1，则不搜索机构
                nameOrPhone = null;
            }
            if (outNetWorkFlag) {
                result = tAddressBookMapper.selectAddressBookUserFullTextSearch(iPage, null, searchKey, nameOrPhone, currentOrgCode, highestOrgCode);
            } else {
                result = tAddressBookMapper.selectAddressBookUserBasic(iPage, null, searchKey, nameOrPhone, currentOrgCode, highestOrgCode);
            }
        }

        if (CollectionUtils.isEmpty(result.getRecords())) {
            return new Page<>();
        }
        log.info("搜索类型判断及查询耗时: {}ms", (System.currentTimeMillis() - searchTypeStart));

        // 收集所有需要查询的机构ID（跳过每个路径的第一个ID）
        Set<String> allOrgIds = result.getRecords().stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getOrgIdPath()))
                .flatMap(dto -> {
                    String[] orgIds = dto.getOrgIdPath().split("\\|");
                    // 跳过第一个ID，从第二个开始
                    return Arrays.stream(orgIds)
                            .skip(1)
                            .filter(StrUtil::isNotBlank);
                })
                .collect(Collectors.toSet());

        // 一次性批量查询所有机构名称（带缓存）
        Map<String, String> orgNameMap = orgPathService.batchGetOrgNames(allOrgIds);

        // 为每条记录构建机构路径
        result.getRecords().forEach(dto -> {
            if (StrUtil.isNotBlank(dto.getOrgIdPath())) {
                String orgPath = buildOrgPath(dto.getOrgIdPath(), dto.getCompanyName(), orgNameMap);
                dto.setCompanyName(orgPath);
            }
            dto.setOrgIdPath(null);
            dto.setOrgCode(null);
            if (dto.getOrgName()==null){
                dto.setOrgName("");
            }
            if (dto.getDefaultPhone()==null){
                dto.setDefaultPhone("");
            }
            if (dto.getCompanyName()==null){
                dto.setCompanyName("");
            }
            if (dto.getJobTitle()==null){
                dto.setJobTitle("");
            }
        });

        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<QueryAddressBookUserDTO> processedRecords = result.getRecords().stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    dto -> Arrays.asList(dto.getUserId(), dto.getOrgId(), dto.getCompanyId()),
                                    dto -> {
                                        // 处理号码是否显示
                                        if (ObjectUtil.equals(dto.getWhetherShow(), 0)) {
                                            dto.setDefaultPhone("");
                                        }
                                        return dto;
                                    },
                                    (existing, replacement) -> existing,
                                    LinkedHashMap::new
                            ),
                            map -> new ArrayList<>(map.values())
                    ));

            // 保持原有的分页信息，只更新records
            result.setRecords(processedRecords);
        }

        return result;
    }

    /**
     * 构建机构路径
     */
    private String buildOrgPath(String orgIdPath, String currentOrgName, Map<String, String> orgNameMap) {
        String[] orgIds = orgIdPath.split("\\|");
        if (orgIds.length <= 1) {
            return currentOrgName;
        }

        // 构建路径（跳过第一个ID）
        StringBuilder pathBuilder = new StringBuilder();
        for (int i = 1; i < orgIds.length; i++) {
            String orgName = orgNameMap.get(orgIds[i]);
            if (StrUtil.isNotBlank(orgName)) {
                if (pathBuilder.length() > 0) {
                    pathBuilder.append("-");
                }
                pathBuilder.append(orgName);
            }
        }

        // 添加当前机构名称
        if (pathBuilder.length() > 0) {
            pathBuilder.append("-").append(currentOrgName);
        } else {
            pathBuilder.append(currentOrgName);
        }

        return pathBuilder.toString();
    }


    /**
     * NEW 根据用户名称，手机号模糊，机构，拼音搜索（app）
     *
     * @param nameOrPhone
     * @return
     */
    public IPage<QueryAddressBookUserDTO> queryTAddressBookUserNew2(String nameOrPhone, IPage iPage ) {
        // 获取当前用户标签机构
        List<TAddressBookLabelDTO> list = tAddressBookLabelService.queryTAddressBookLabelApp2();

        if (CollectionUtils.isEmpty(list)) {
            return new Page<>();
        }

        // 提取所有的id
        List<Long> ids = list.stream().map(TAddressBookLabelDTO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return new Page<>();
        }

        // 获取机构编码路径
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CscpOrg::getId, CscpOrg::getOrgCodePath).in(CscpOrg::getId, ids);
        List<CscpOrg> orgList = cscpOrgRepository.selectListOnlyAddTenantId(queryWrapper);
        Map<Long, String> orgCodePathMap = orgList.stream()
                .collect(Collectors.toMap(
                        CscpOrg::getId,
                        CscpOrg::getOrgCodePath,
                        (existing, replacement) -> {
                            log.info("发现重复的orgId: {}, 保留第一个值", existing);
                            return existing;
                        }
                ));

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        String currentOrgCode = null;
        String highestOrgCode = null;
        // 先检查是否存在 id=0 的情况，等于0表示可搜索全省
        boolean hasZeroId = list.stream().anyMatch(dto -> dto.getId() != null && dto.getId() == 0L);
        if (!hasZeroId) {
            for (TAddressBookLabelDTO dto : list) {
                Long id = dto.getId();
                if (id == null) {
                    log.error("通讯录标签ID不能为空:{}",dto);
                    continue;
                }
                String orgCodePath = orgCodePathMap.get(id);

                if (orgCodePath != null) {
                    if (Objects.equals(id, currentCompanyId)) {
                        currentOrgCode = orgCodePath;
                    } else {
                        highestOrgCode = orgCodePath;
                    }
                }
            }
        }

        // 搜索字段加密处理
        long encryptStart = System.currentTimeMillis();
        String searchKey = westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(nameOrPhone) : KeyCenterUtils.division(nameOrPhone);
        log.info("加密处理耗时: {}ms", (System.currentTimeMillis() - encryptStart));

        IPage<QueryAddressBookUserDTO> result;
        // 判断搜号码还是姓名或机构名
        long searchTypeStart = System.currentTimeMillis();
        // 判断内外网标志：in-内网，out-外网
        boolean outNetWorkFlag = sysConfigService.isWwBySysConfig();
        if (StrUtil.isNumeric(nameOrPhone)) {
            // 手机号搜索
            if (outNetWorkFlag) {
                result = tAddressBookMapper.selectAddressBookUserFullTextSearch(iPage, searchKey, null, null, currentOrgCode, highestOrgCode);
            } else {
                result = tAddressBookMapper.selectAddressBookUserBasic(iPage, searchKey, null, null, currentOrgCode, highestOrgCode);
            }
        } else {
            // 用户姓名或机构名搜索
            String value = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SEARCH_ORG);
            if (!StrUtil.equals(value, "1")) {
                // 不等于1，则不搜索机构
                nameOrPhone = null;
            }
            if (outNetWorkFlag) {
                result = tAddressBookMapper.selectAddressBookUserFullTextSearch(iPage, null, searchKey, nameOrPhone, currentOrgCode, highestOrgCode);
            } else {
                result = tAddressBookMapper.selectAddressBookUserBasic(iPage, null, searchKey, nameOrPhone, currentOrgCode, highestOrgCode);
            }
        }

        if (CollectionUtils.isEmpty(result.getRecords())) {
            return new Page<>();
        }
        log.info("搜索类型判断及查询耗时: {}ms", (System.currentTimeMillis() - searchTypeStart));

        // 收集所有需要查询的机构ID（跳过每个路径的第一个ID）
        Set<String> allOrgIds = result.getRecords().parallelStream()
                .filter(dto -> StrUtil.isNotBlank(dto.getOrgIdPath()))
                .flatMap(dto -> Arrays.stream(dto.getOrgIdPath().split("\\|")).skip(1).filter(StrUtil::isNotBlank))
                .collect(Collectors.toSet());

        // 一次性批量查询所有机构名称（带缓存）
        RedisOrgNameCache orgNameCache = new RedisOrgNameCache(orgPathService, redisUtil);
        Map<String, String> orgNameMap = orgNameCache.batchGetOrgNames(allOrgIds);

        // 为每条记录构建机构路径
        if (CollUtil.isNotEmpty(result.getRecords())) {
            // 先去重，再处理数据，避免对重复数据进行不必要的处理
            Map<List<Object>, QueryAddressBookUserDTO> unique = new LinkedHashMap<>();
            for (QueryAddressBookUserDTO dto : result.getRecords()) {
                List<Object> key = Arrays.asList(dto.getUserId(), dto.getOrgId(), dto.getCompanyId());
                unique.putIfAbsent(key, dto);
            }

            // 对去重后的数据进行处理
            List<QueryAddressBookUserDTO> finalResult = unique.values().parallelStream()
                    .peek(dto -> {
                        // 构建机构路径
                        if (StrUtil.isNotBlank(dto.getOrgIdPath())) {
                            String orgPath = buildOrgPath2(dto.getOrgIdPath(), dto.getCompanyName(), orgNameMap);
                            dto.setCompanyName(orgPath);
                        }

                        // 处理号码是否显示
                        if (ObjectUtil.equals(dto.getWhetherShow(), 0)) {
                            dto.setDefaultPhone("");
                        }

                        // 使用 Optional 避免 NullPointerException，并确保前端接收到的是空字符串而不是 null
                        dto.setOrgName(Optional.ofNullable(dto.getOrgName()).orElse(""));
                        dto.setDefaultPhone(Optional.ofNullable(dto.getDefaultPhone()).orElse(""));
                        dto.setCompanyName(Optional.ofNullable(dto.getCompanyName()).orElse(""));
                        dto.setJobTitle(Optional.ofNullable(dto.getJobTitle()).orElse(""));

                        // 清理不再需要返回给客户端的数据
                        dto.setOrgIdPath(null);
                        dto.setOrgCode(null);
                    })
                    .collect(Collectors.toList());

            // 保持原有的分页信息，只更新records
            result.setRecords(finalResult);
        }
        return result;
    }

    /**
     * 构建机构路径
     */
    private String buildOrgPath2(String orgIdPath, String currentOrgName, Map<String, String> orgNameMap) {
        String[] orgIds = orgIdPath.split("\\|");
        if (orgIds.length <= 1) return currentOrgName;
        StringJoiner joiner = new StringJoiner("-");
        for (int i = 1; i < orgIds.length; i++) {
            String orgName = orgNameMap.get(orgIds[i]);
            if (StrUtil.isNotBlank(orgName)) joiner.add(orgName);
        }
        joiner.add(currentOrgName);
        return joiner.toString();
    }

    /**
     * 通讯录分页搜索，根据用户名称，手机号模糊，机构，拼音搜索（app）
     *
     * @param nameOrPhone
     * @return
     */
    public PageResult<QueryAddressBookUserDTO> queryTAddressBookUserPage(String nameOrPhone, BasePageForm basePageForm) {
        IPage iPage = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        String searcFlag = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SEARCH_FLAG);
        if (StrUtil.isNotBlank(searcFlag) && StrUtil.equals(searcFlag, "1")) {
            IPage<QueryAddressBookUserDTO> pageResult = this.queryTAddressBookUserNew(nameOrPhone, iPage);
            return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), pageResult.getCurrent());
        }else if (StrUtil.isNotBlank(searcFlag) && StrUtil.equals(searcFlag, "2")) {
            IPage<QueryAddressBookUserDTO> pageResult = this.queryTAddressBookUserNew2(nameOrPhone, iPage);
            return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), pageResult.getCurrent());
        }
        return new PageResult<>(new ArrayList<>(), 0L, basePageForm.getCurrentPage());
    }

    /**
     * 根据用户名称，手机号模糊，机构，拼音搜索（app）
     *
     * @param nameOrPhone
     * @return
     */
    @Override
    public List<QueryAddressBookUserDTO> queryTAddressBookUser(String nameOrPhone) {
        String limitStr = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SEARCH_LIMIT);
        Integer limit = 200;
        if (StrUtil.isNotBlank(limitStr) && StrUtil.isNumeric(limitStr)) {
            try {
                limit = Convert.toInt(limitStr);
            } catch (Exception e) {
                log.error("转换APP_SEARCH_LIMIT配置失败，使用默认值200", e);
            }
        }
        String searcFlag = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SEARCH_FLAG);
        if (StrUtil.isNotBlank(searcFlag) && StrUtil.equals(searcFlag, "1")) {
            return this.queryTAddressBookUserNew(nameOrPhone,new Page<>(1,limit)).getRecords();
        }else if (StrUtil.isNotBlank(searcFlag) && StrUtil.equals(searcFlag, "2")) {
            return this.queryTAddressBookUserNew2(nameOrPhone,new Page<>(1,limit)).getRecords();
        }
        long startTime = System.currentTimeMillis();

        // 搜索字段加密处理
        long encryptStart = System.currentTimeMillis();
        String searchKey = westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptMobileWithFPE(nameOrPhone) : KeyCenterUtils.division(nameOrPhone);
        log.info("加密处理耗时: {}ms",(System.currentTimeMillis() - encryptStart));

        List<QueryAddressBookUserDTO> result;
        // 判断搜号码还是姓名或机构名
        long searchTypeStart = System.currentTimeMillis();
        if (StrUtil.isNumeric(nameOrPhone)) {
            // 手机号搜索
            result = tAddressBookMapper.selectAddressBookUserApp(searchKey, null, null, limit);
        } else {
            //用户姓名或机构名搜索
            String value = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SEARCH_ORG);
            if (!StrUtil.equals(value, "1")) {
                // 不等于1，则不搜索机构
                nameOrPhone=null;
            }
            result = tAddressBookMapper.selectAddressBookUserApp(null, searchKey, nameOrPhone, limit);
        }
        log.info("搜索类型判断及查询耗时: {}ms",(System.currentTimeMillis() - searchTypeStart));

        // 获取用户可见的标签（使用缓存）
        long cacheStart = System.currentTimeMillis();
        List<String> orgCodeList = getUserVisibleOrgCodes();
        log.info("获取用户可见标签耗时: {}ms",(System.currentTimeMillis() - cacheStart));

        List<String> finalOrgCodeList = orgCodeList;

        // 根据userId、orgId和companyId联合去重
        long filterStart = System.currentTimeMillis();
        if (CollUtil.isNotEmpty(result)) {
            result = result.stream()
                    .filter(dto -> {
                        String restCode = dto.getOrgCode();
                        // 如果orgCode为空，不进行过滤
                        if (restCode == null) {
                            return true;
                        }
                        // 检查是否有任何baseCode是restCode的前缀
                        return finalOrgCodeList.stream()
                                .anyMatch(baseCode -> restCode.startsWith(baseCode));
                    })
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    dto -> Arrays.asList(dto.getUserId(), dto.getOrgId(), dto.getCompanyId()),
                                    dto -> {
                                        // 处理号码是否显示
                                        if (ObjectUtil.equals(dto.getWhetherShow(), 0)) {
                                            dto.setDefaultPhone("");
                                        }
                                        return dto;
                                    },
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
        }
        log.info("数据过滤和去重耗时: {}ms",(System.currentTimeMillis() - filterStart));

        log.info("方法总耗时: {}ms",(System.currentTimeMillis() - startTime));
        return result;
    }

    /**
     * 获取用户可见的组织代码（带缓存）
     *
     * @return 可见组织代码集合
     */
    private List<String> getUserVisibleOrgCodes() {
        String cacheKey = "user_visible_org_codes:" + SecurityUtils.getCurrentUserId();
        String cachedJson = (String) redisUtil.get(cacheKey);
        List<String> orgCodeList = new ArrayList<>();
        if (StrUtil.isNotEmpty(cachedJson)) {
            try {
                // 解压缩并解析缓存数据
                String uncompressedJson = GzipUtil.uncompress(cachedJson);
                orgCodeList = JSON.parseArray(uncompressedJson, String.class);
            } catch (Exception e) {
                log.error("解析缓存数据失败，将重新查询", e);
                // 如果解析失败，继续执行正常查询流程
            }
        }

        if (CollUtil.isEmpty(orgCodeList)) {
            // 查询用户 display 字段
            TAddressBook tAddressBook = tAddressBookMapper.selectOne(
                    new LambdaQueryWrapper<TAddressBook>()
                            .select(TAddressBook::getDisplay)
                            .eq(TAddressBook::getUserId, SecurityUtils.getCurrentUserId())
            );

            // 查询用户授权的标签列表
            List<TAddressBookAuthorizeDTO> authorizeList = tAddressBookMapper.selectUserLabelIdList(
                    Arrays.asList(SecurityUtils.getCurrentCompanyId(),
                            SecurityUtils.getCurrentCscpUserDetail().getDepartmentId())
            );
            log.info("0---查询到的标签列表: {}", authorizeList);

            if (CollUtil.isNotEmpty(authorizeList)) {
                orgCodeList = authorizeList.stream()
                        .filter(i -> tAddressBook.getDisplay() != 0 || !"本单位".equals(i.getLabelName()))
                        .map(TAddressBookAuthorizeDTO::getOrgCode).distinct()
                        .collect(Collectors.toList());
            } else {
                String orgCode = cscpOrgRepository.selectOneNoAdd(
                        new LambdaQueryWrapper<CscpOrg>()
                                .select(CscpOrg::getOrgCode)
                                .eq(CscpOrg::getId, SecurityUtils.getCurrentCompanyId())
                                .last("limit 1")
                ).getOrgCode();
                orgCodeList.add(orgCode);
            }
            log.info("1----初步获取的组织代码列表: {}", orgCodeList);

            // 缓存组织代码，设置1小时过期
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String value = objectMapper.writeValueAsString(orgCodeList);
                String compressStr = GzipUtil.compress(value);
                redisUtil.set(cacheKey, compressStr, 1, TimeUnit.HOURS);
            } catch (Exception e) {
                log.error("存储标签授权机构数据到Redis缓存失败", e);
            }
        }

        return orgCodeList;
    }

    @Override
    public void updateLastNameEncrypt() {
        // 测试 查空的数据
        // TAddressBook tAddressBook = tAddressBookMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBook>().eq(TAddressBook::getId , "*********8562589698"));
        // String name = tAddressBook.getRealName();
        // String lastNameEncrypt = tAddressBook.getLastNameEncrypt();
        // tAddressBook.setLastNameEncrypt(name);
        // saveOrUpdate(tAddressBook);
        //
        // CscpUser item = cscpUserService.selectOneNoAdd(new LambdaQueryWrapper<CscpUser>().eq(CscpUser::getId,
        //         tAddressBook.getUserId()));
        // String realName = item.getRealName();
        // String mobile = item.getMobile();
        // item.setMobileEnd(mobile);
        // item.setMobileMiddle(mobile);
        // item.setMobileStart(mobile);
        // item.setRealNameEnd(realName);
        // item.setRealNameStart(realName);

        List<TAddressBook> tAddressBooks =
                tAddressBookMapper.selectListNoAdd(new LambdaQueryWrapper<TAddressBook>());//.eq(TAddressBook::getId , "*********8562589698")
        tAddressBooks.forEach(i -> {
            i.setLastNameEncrypt(i.getRealName());
            i.setDefaultPhoneEncrypt1(i.getDefaultPhone());
            if (westoneEncryptService.isCipherMachine()) {
                // TODO 计算SM3HMAC
                String hmacDefaultMobileSrc = i.getRealName() + i.getDefaultPhone();
                i.setHmacDefaultPhone(westoneEncryptService.calculateSM3HMAC(hmacDefaultMobileSrc));
            }
        });

        // 方式1 全量更新
        // saveOrUpdateBatch(tAddressBooks);

        // 1 更新通讯录表
        // 方式2 每次更新100条
        int size = tAddressBooks.size();
        for (int i = 1 ; 100 * (i - 1)  < size ; i++ ) {
            // 最后一组
            List<TAddressBook> list = new ArrayList<>();
            if (i * 100 > size) {
                list = tAddressBooks.subList(100 * (i - 1) , size);
            } else {
                list = tAddressBooks.subList(100 * (i - 1) , 100 * i);
            }
            saveOrUpdateBatch(list);
        }
        // 2 更新用户表 *********7665008642
        List<CscpUser> cscpUsers =
                cscpUserService.selectListNoAdd(new LambdaQueryWrapper<CscpUser>());//
        cscpUsers.forEach(item -> {
            String realName = item.getRealName();
            String mobile = item.getMobile();
            item.setMobile(mobile);// 修改加密方式
            if (westoneEncryptService.isCipherMachine()) {
                // TODO 计算SM3HMAC
                String src = item.getLoginName() + item.getPassword() + item.getMobile();
                item.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
            }
            item.setRealName(realName);// 修改加密方式
            item.setMobileEnd(mobile);
            item.setMobileMiddle(mobile);
            item.setMobileStart(mobile);
            item.setRealNameEnd(realName);
            item.setRealNameStart(realName);
        });

        int sizeUsers = cscpUsers.size();
        for (int i = 1 ; 100 * (i - 1)  < sizeUsers ; i++ ) {
            // 最后一组
            List<CscpUser> list = new ArrayList<>();
            if (i * 100 > sizeUsers) {
                list = cscpUsers.subList(100 * (i - 1) , sizeUsers);
            } else {
                list = cscpUsers.subList(100 * (i - 1) , 100 * i);
            }
            cscpUserService.saveOrUpdateBatch(list);
        }


    }

    /**
     * 根据指定的机构进行查询
     *
     * @param orgAll
     * @param orgQuery
     * @param orgIdSet
     * @return
     */
    private List<Long> buildOrgId(List<CscpOrg> orgAll, List<CscpOrg> orgQuery, Set<Long> orgIdSet) {
        List<Long> orgIdList = new LinkedList<>();

        //如果查询条件中有单位机构，所以就根据单位进行查询
        List<CscpOrg> companyId = orgQuery.stream().filter(i -> i.getType().equals(OrgTypeEnum.ORG_TYPE_2.getCode())).collect(Collectors.toList());
        if (!companyId.isEmpty()) {
            orgQuery = companyId;
        }

        orgQuery.stream().forEach(i -> {
            //判断这个机构id是否已经查询过了
            if (!orgIdSet.contains(i.getId())) {
                List<Long> children = getChildren(i, orgAll);
                children.add(i.getId());
                //机构上下级数据
                orgIdList.addAll(children);
                //判断是否已经查询过的set
                orgIdSet.addAll(children);
            }
        });
        return orgIdList;
    }

    /**
     * 递归查询当前机构的下级
     *
     * @param org
     * @param orgList
     * @return
     */
    private List<Long> getChildren(CscpOrg org, List<CscpOrg> orgList) {
        // 子节点parentId = 父节点ID
        return orgList.stream().filter(m1 -> org.getId().equals(m1.getParentId()))
                .peek(m1 -> {
                    getChildren(m1, orgList);
                }).map(i -> i.getId()).collect(Collectors.toList());
    }

    /**
     * 填充单位和部门
     *
     * @param orgIdList
     */
    private Map<Integer, Object> setAddressBookOrg(Set<Long> orgIdList) {
        Map<Integer, Object> map = new HashMap<>(2);
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUserOrg>()
                        .select(CscpUserOrg::getUserId, CscpUserOrg::getOrgId, CscpUserOrg::getCompanyId)
                        .in(CscpUserOrg::getOrgId, orgIdList)
        );
        Map<Long, CscpUserOrg> o1 = cscpUserOrgs.stream()
                .collect(Collectors.toMap(CscpUserOrg::getUserId, cscpUserOrg -> cscpUserOrg, (existing, replacement) -> existing));
        map.put(1, o1);

        //填充部门名称和单位名称
        List<Long> orgIdList1 = cscpUserOrgs.stream().map(i -> i.getOrgId()).collect(Collectors.toList());
        orgIdList1.addAll(cscpUserOrgs.stream().map(i -> i.getCompanyId()).collect(Collectors.toList()));
        Map<Long, CscpOrg> orgMap = cscpOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpOrg>().in(CscpOrg::getId, orgIdList1)).stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg -> CscpOrg, (existing, replacement) -> existing));
        map.put(2, orgMap);
        return map;

    }

    /**
     * 删除通讯录用户
     */

    @Transactional(rollbackFor = Exception.class)
    public void deleteAddressBookUser(Long userId) {
        tAddressBookMapper.delete(new LambdaQueryWrapper<TAddressBook>()
                .eq(TAddressBook::getUserId, userId));
    }

    @Override
    public void updateUserStatus(List<Long> userIds,Integer status) {
        List<TAddressBook> tAddressBookList =
                tAddressBookMapper.selectListNoAdd(new LambdaQueryWrapper<TAddressBook>().select(TAddressBook::getId)
                        .in(TAddressBook::getUserId,userIds));
        tAddressBookList.forEach(item -> item.setStatus(status));
        this.updateBatchById(tAddressBookList);
    }

}
