<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.ssdc.admin.repository.CscpUserRoleRepository">

    <delete id="deleteUserRoleByUserId" parameterType="java.lang.Long">
        UPDATE cscp_user_role SET deleted = 1
        WHERE user_id = #{userId, jdbcType=BIGINT}
        AND deleted = 0
    </delete>
    <select id="queryRoleByUserIds" resultType="com.ctsi.ssdc.admin.domain.CscpRoles">
        select  r.id,r.name,ur.user_id
    	from cscp_roles r,cscp_user_role ur
    	where r.id=ur.role_id
        and ur.user_id in
    	<foreach collection="userIds" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
    	and ur.deleted = 0 and r.deleted = 0
    </select>
    <select id="selectMenuIdsByUserIds" resultType="java.lang.Long">
        SELECT DISTINCT menu_id
        FROM cscp_role_menu crm
                 JOIN cscp_user_role cur
                      on cur."role_id" = crm."role_id"
        where cur."user_id" = #{userId}
          and cur."deleted" = 0
    </select>

    <!-- 1. 根据角色ID查询应用ID列表 -->
    <select id="selectAppIdsByRoleId" resultType="java.lang.Long">
        SELECT id
        FROM t_sync_app_system_manage
        WHERE role_id = #{roleId}
    </select>

    <!-- 2. 批量插入版主数据 -->
    <insert id="batchInsertModerators">
        INSERT INTO t_sync_app_moderator_manage (id, app_id, moderator_id, deleted, CREATE_TIME, CREATE_BY, CREATE_NAME)
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (#{item.id}, #{item.appId}, #{item.moderatorId}, 0, NOW(), #{item.createBy}, #{item.createName})
        </foreach>
    </insert>


    <update id="deleteModeratorsForRole">
    UPDATE
        t_sync_app_moderator_manage
    SET
        deleted = 1,
        UPDATE_TIME = NOW(),
        UPDATE_BY = #{updateBy},
        UPDATE_NAME = #{updateName}
    WHERE
        moderator_id IN
        <foreach item="userId" collection="userIdList" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND app_id IN
        (
            SELECT id FROM t_sync_app_system_manage WHERE role_id = #{roleId}
        )
    </update>
</mapper>