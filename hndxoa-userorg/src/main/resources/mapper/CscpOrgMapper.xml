<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.ssdc.admin.repository.CscpOrgRepository">
    <resultMap id="BaseResultMap" type="com.ctsi.ssdc.admin.domain.CscpOrg">
        <!--@mbg.generated-->
        <!--@Table cscp_org-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="order_by" jdbcType="INTEGER" property="orderBy"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="path_code" jdbcType="VARCHAR" property="pathCode"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="create_name_id" jdbcType="BIGINT" property="createNameId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_name_id" jdbcType="BIGINT" property="updateNameId"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="lan_id" jdbcType="VARCHAR" property="lanId"/>
        <result column="lan" jdbcType="VARCHAR" property="lan"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_name, description, parent_id, order_by, code, path_code, level, type,
        create_name_id, create_name, create_date, update_name_id, update_name, update_date,
        lan_id, lan, tenant_id
    </sql>




    <delete id="deleteRealById">

        delete from cscp_org where id=#{id}

    </delete>

    <select id="selectorgList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT *
        from cscp_org
        where id not in (SELECT org_id from cscp_roles where org_id!='')
           or
            id =#{id,jdbcType=BIGINT}
    </select>

    <!--<select id="selectOrgList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
            smo.*
        from sys_menu_org smo
                 INNER JOIN cscp_roles cr on cr.org_id = smo.id
                 INNER JOIN cscp_user_role cur on cr.id = cur.role_id
        where cur.user_id = #{id} group by smo.id
    </select>-->

    <select id="getChildOrgListNoAddTxl" resultType="com.ctsi.hndx.addrbook.entity.dto.TLabelOrgDTO">
        SELECT
        tlo.org_id as id

        FROM
        t_label_org tlo
        LEFT JOIN cscp_org org
        ON org.id = tlo.org_id
        WHERE
        tlo.deleted = 0
        AND org.deleted=0

        AND org.parent_id= #{orgId}
        AND tlo.display_range_id IN
        <foreach collection="labelIds" index="index" item="lid" open="(" separator="," close=")">
            #{lid}
        </foreach>

    </select>
    <update id="updatePathCode">
        update cscp_org set path_code = CONCAT(#{newPathCode},substring(path_code,#{len}))
        where path_code like concat(#{pathCode},'%')
    </update>

    <select id="countUserByOrg" resultType="java.lang.Integer">
        select count(1) from cscp_user a
                                 left join
                             cscp_user_org b on a.id=b.user_id
                                 left join
                             t_address_book tab ON tab.USER_ID = b.user_id
        where  b.deleted = 0 AND tab.display = 1 AND a.deleted = 0
          and b.org_id=#{orgId}
    </select>

    <select id="countUserByParentId" resultType="java.lang.Integer">
        select count(1)
        from cscp_user a
                 left join
             cscp_user_org b on a.id=b.user_id
                 left join
             cscp_org co on co.id=b.org_id
                 left join
             t_address_book tab ON tab.USER_ID = b.user_id
        where  b.deleted = 0 AND tab.display = 1 AND a.deleted = 0
          and co.parent_id=#{orgId}
    </select>
    <select id="countUserByCompany" resultType="java.lang.Integer">
        select count(1) from cscp_user a
                                 left join
                             cscp_user_org b  on a.id=b.user_id
                                 left join
                             t_address_book tab ON tab.USER_ID = b.user_id
        where  b.deleted=0 AND tab.display = 1  AND a.deleted = 0
          and b.company_id=#{companyId}
    </select>

    <select id="countParentId" resultType="java.lang.Integer">
        SELECT count(1) FROM cscp_user a, cscp_user_org b WHERE a.id = b.user_id AND a.statistics = 1 AND b.deleted = 0
        AND b.company_id in
        <foreach collection="resultList" index="index" item="lid" open="(" separator="," close=")">
            #{lid}
        </foreach>
        and a.display = 1
    </select>

    <select id="getLabelList" resultType="java.lang.Long">
        SELECT a.id FROM cscp_org a LEFT JOIN t_label_org b on a.id = b.org_id where b.display_range_id = #{parentId} and b.deleted = 0 and a.type = 2

    </select>
    <!--根据当前机构id获取上级机构-->
    <select id="selectParentOrg" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT * from cscp_org where id = (select parent_id from cscp_org where id=#{orgId});
    </select>

    <!--根据用户id获取当前机构id-->
    <select id="selectOrgByUserId" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT cu.id,cuo.org_name,cuo.company_id
        FROM cscp_user_org AS cuo
                 LEFT JOIN cscp_user AS cu ON cu.id = cuo.user_id
        WHERE
            cu.str_id = #{strId}
          AND cuo.deleted=0
          AND cu.deleted=0
          AND cu.display = 1
          AND cu.status = 1
        limit 1
    </select>

    <select id="selectAllDeptsByParentDeptId" resultType="Long">
        SELECT id
        FROM cscp_org
        WHERE
        deleted = 0
        <if test="parentDeptId != null">
            AND parent_id in #{parentDeptId}
        </if>
    </select>

    <update id="updateUnitTypeById">
        UPDATE cscp_org SET unit_type = null WHERE id = #{id}
    </update>
    <update id="updateRootPath">
        UPDATE cscp_org SET org_id_path = '', org_code_path = org_code, level = 1, order_by_path = 100000 + order_by
        WHERE parent_id = 0
    </update>

    <select id="getOneByParentIdMaxNumberDesc" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select * from cscp_org where parent_id = #{parentId}
        order by TO_NUMBER(org_code) desc limit 1
    </select>
    <select id="selectSelf2RootOrg" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT *
        FROM cscp_org
                 START WITH id = #{orgId}
            CONNECT BY PRIOR parent_id = id
        order by id
    </select>

    <select id="selectOrgLengthOne" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT * FROM cscp_org
        where CHAR_LENGTH(org_code) = 12
        ORDER BY "order_by" limit 1
    </select>

    <select id="selectOrgCityStateList" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT DISTINCT * FROM cscp_org where id != #{id} AND deleted = 0
        AND id IN (
            SELECT id FROM
            cscp_org
            START WITH
            parent_id = #{id}
            CONNECT BY
            PRIOR parent_id = id
        )
        ORDER BY order_by
    </select>

    <select id = "selectOrgCityByOrgCode" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT DISTINCT * FROM cscp_org WHERE "deleted" = 0
                                          AND "org_code" LIKE CONCAT(SUBSTRING(#{orgCode}),'%') AND CHAR_LENGTH(org_code) = 12
        order by order_by
    </select>

    <select id="selectOrgChildrenMaxNumber" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT "parent_id" as parent_id, MAX("max_number") as max_number FROM cscp_org WHERE deleted = 0 and  "parent_id" IN
        (SELECT "id" FROM cscp_org WHERE deleted = 0
        and org_code IN
        <foreach collection="codeList" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        )
        GROUP BY "parent_id"
    </select>
    <select id="getAllOrgCodeByParentId" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select * from cscp_org where parent_id = #{parentId}
    </select>
    <select id="selectAllWithChildPaths" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT * FROM cscp_org
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            deleted = 0
            <foreach collection="codes" item="code" open="AND (" close=")" separator=" OR ">
                org_code_path LIKE CONCAT(#{code}, '%')
            </foreach>
        </trim>
        ORDER BY org_code, order_by
    </select>

    <select id="selectOrgStartWith" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select id,org_name,org_code,parent_id,org_code_path from cscp_org
        WHERE deleted = 0
        START WITH id = #{id}
        CONNECT BY PRIOR id = parent_id
    </select>
    <select id="getOrgIgnoreDeleted" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select * from cscp_org where id = #{id}
    </select>

    <select id = "selectOrgByCodePath" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select * from (
                          select
                              type,LEVEL as org_level,id,org_name,org_code,
                              parent_id,external_id,deleted,order_by
                          from cscp_org
                                   START WITH parent_id = #{id}
                              CONNECT BY PRIOR id = parent_id
                      ) a
        where deleted = 0 and external_id IS NOT NULL
        order by org_level,to_number(org_code),order_by;
    </select>
    <select id="selectSubOrgListWithChildrenStatus" resultType="com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO">
        SELECT
            o.id,
           CASE
                WHEN o.alias_name IS NOT NULL AND o.alias_name != '' THEN o.alias_name
                ELSE o.org_name
            END AS org_name,
            o.type,
            o.order_by,
            CASE
                WHEN EXISTS (
                    SELECT 1
                    FROM cscp_org child
                    WHERE child.parent_id = o.id and child.deleted=0
                    ) THEN 1
                ELSE 0
                END as is_valuable
        FROM cscp_org o
        WHERE o.parent_id = #{parentId} and o.deleted=0
        ORDER BY o.order_by ASC
    </select>

    <select id = "selectOrgChildrenByParentId" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT *
        FROM cscp_org
        where deleted = 0
        START WITH parent_id = #{id}
        CONNECT BY PRIOR id = parent_id
    </select>
    <select id="selectDeletedList" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT *
        FROM cscp_org
        where deleted = 1

        <if test="orgName != null and orgName != ''">
            and org_name like concat('%',concat(#{orgName},'%'))
        </if>

        <if test="orgCode != null and orgCode != ''">
            and org_code like concat('%',concat(#{orgCode},'%'))
        </if>

    </select>
    <select id = "getAllOrgsFlat" resultType="com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO">
        SELECT o.id,
               o.org_name,
               o.parent_id,
               o."org_code",
               o."credit_code",
               o."order_by",
               o.type,
               p.org_name AS parent_name
        FROM cscp_org o
                 LEFT JOIN cscp_org p ON o.parent_id = p.id
        WHERE o.deleted = '0'
        START WITH o.parent_id = 0
CONNECT BY PRIOR o.id = o.parent_id
ORDER SIBLINGS BY order_by
;

    </select>

    <select id = "getFlatOrgList" resultType="com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO">
        SELECT o.id,
               o.org_name,
               o.parent_id,
               o."org_code",
               o."code",
               o."credit_code",
               o."order_by",
               o."type",
               o."path_Code",
               p.org_name AS parent_name
        FROM cscp_org o
                 LEFT JOIN cscp_org p ON o.parent_id = p.id
        WHERE o.deleted = '0'
        START WITH o.id = #{id}
CONNECT BY PRIOR o.id = o.parent_id
ORDER SIBLINGS BY order_by;
    </select>
    <select id="selectChildrenByCondition" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT *
        FROM cscp_org
        where "deleted" = 0
          <if test="orgNameList != null and orgNameList.size() > 0">
              and "org_name" in
                  <foreach collection="orgNameList" item="orgName" open="(" close=")" separator=",">
                      #{orgName}
                  </foreach>
          </if>
            START WITH id = #{orgId}
        CONNECT BY PRIOR id = parent_id
        ORDER by "org_id_path"
    </select>

    <select id="selectAllChild" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        SELECT *
        FROM cscp_org o
        WHERE o.deleted = '0'
        START WITH
        <foreach collection="partitionedIds" item="subList" separator=" OR ">
            id IN
            <foreach collection="subList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </foreach>
        CONNECT BY PRIOR o.id = o.parent_id
        ORDER SIBLINGS BY order_by
    </select>

    <update id="updateAppCodesById">
        update cscp_org set push_app_code = #{appCodes} where id = #{orgId}
    </update>

    <select id="selectOrgUnitStartWithById" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select DISTINCT *
        from cscp_org
        where deleted = 0 and type = 2
        START WITH
            id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        CONNECT BY
            PRIOR id = parent_id
    </select>

    <update id="updateLocateAppCodeBatch">
        update cscp_org SET push_app_code = CONCAT(push_app_code, ',',#{appCode})
        where deleted = 0 and LOCATE(#{appCode}, push_app_code) = 0
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectOrgHasDeleted" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
        select * from cscp_org
        <where>
        id in
            <foreach collection="ids" item="id" close=")" open="(" separator=",">
              #{id}
            </foreach>
        </where>
    </select>

    <select id="getCompanyInfoByDepartmentId" resultType="com.ctsi.ssdc.admin.domain.CscpOrg">
    SELECT id, credit_code
      FROM (
        SELECT t.id AS id, t.credit_code, t.type
          FROM cscp_org t
        WHERE t.deleted = 0
        START WITH t.id = #{departmentId}
        CONNECT BY NOCYCLE PRIOR t.parent_id = t.id
        ORDER BY t.level desc
      ) temp
     WHERE temp.type = 2
       AND ROWNUM = 1
    </select>


</mapper>
