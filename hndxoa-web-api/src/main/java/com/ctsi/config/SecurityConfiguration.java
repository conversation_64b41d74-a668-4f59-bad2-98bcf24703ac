package com.ctsi.config;


import com.ctsi.ssdc.security.LogoutSuccessHandlerImpl;
import com.ctsi.ssdc.security.jwt.JWTConfigurer;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.vote.AuthenticatedVoter;
import org.springframework.security.access.vote.RoleVoter;
import org.springframework.security.access.vote.UnanimousBased;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.access.expression.WebExpressionVoter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.web.filter.CorsFilter;
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@Import(SecurityProblemSupport.class)
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {

    @Autowired
    private TokenProvider tokenProvider;

    /**
     * 跨域过滤器
     */
    @Autowired
    private CorsFilter corsFilter;


    @Autowired
    private SecurityProblemSupport problemSupport;

    /**
     * 退出处理类
     */
    @Autowired
    private LogoutSuccessHandlerImpl logoutSuccessHandler;

    @Bean
    public AccessDecisionManager accessDecisionManager() {
        RoleVoter voter = new RoleVoter();
        voter.setRolePrefix(StringUtils.EMPTY);

        List<AccessDecisionVoter<? extends Object>> decisionVoters = Arrays.asList(new WebExpressionVoter(), voter,
                new AuthenticatedVoter());

        return new UnanimousBased(decisionVoters);
    }


    @Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring()
                .antMatchers(HttpMethod.OPTIONS, "/**")
                .antMatchers("/swagger-ui/*")
                .antMatchers("/api/system/login")
                .antMatchers("/api/system/login2")
                .antMatchers("/api/system/loginByCas")
                .antMatchers("/api/system/aoLianlogin")

                // APP移动端修改密码
                .antMatchers("/api/system/modifyVpnPwd")

                .antMatchers("/api/activiti/query/addOrUpdateHaoqianByFormDataIdByMerge")
                .antMatchers("/api/activiti/query/downLoadLatestHaoqianFile")

                .antMatchers("/api/system/appLogin")
                .antMatchers("/api/system/appLoginNew")

                .antMatchers("/api/documentFile/wpsDownloadFormFiles/*")
                .antMatchers("/api/documentFile/wpsUploadFormFiles")
                .antMatchers("/api/cscpRedfile/downloadFormFiles/*/*")
                .antMatchers("/api/cscpSeal/downloadFormFiles/*/*")
                .antMatchers("/api/commonFile/downloadFileByFilePath/*")
                .antMatchers("/api/commonFile/fileEncrypt")
                .antMatchers("/api/commonFile/fileDecrypt")
                .antMatchers("/api/system/crmToBiyi/*")
                .antMatchers("/api/system/loginNew")
                .antMatchers("/api/system/sendSmsVerificationCode")
                .antMatchers("/api/cformLogOpration/create")
                .antMatchers("/api/bizLeaderOutgoInbox/queryBizLeaderOutCount")
//                .antMatchers("/api/tSysWpsCallback/*")
                // 新增wps中台v6在线预览接口
                .antMatchers("/api/wps/v6/callback/preview/getWpsJssdkPreviewConf")
                // 新增wps中台v6正文编辑接口
                .antMatchers("/api/wps/v6/callback/edit/getWpsJssdkEditConf")
                // 新增wps中台v6回调接口
                .antMatchers("/api/wps/v6/callback/*")

                // 新增插入PDF和删除PDF生成的临时图片文件接口放行
                .antMatchers("/api/enclosureFile/insertPdf")
                .antMatchers("/api/enclosureFile/delPdfImgs")
                .antMatchers("/api/tSysConfig/getSwDomainAddressConfig")
                .antMatchers("/api/sxSyncUserOrgData/sync")
                .antMatchers("/api/documentFile/wpsDownloadOFD/*")
                .antMatchers("/api/activiti/query/getCountActivelyTaskListByStrId")
                // 批注ofd文件保存
                .antMatchers("/api/documentFile/ofdFileSava")

                .antMatchers("/api/external/getAllCornerCountByStrId")
                .antMatchers("/api/external/**")
                .antMatchers("/api/enclosureFile/wpsDownloadOFD/*")
                .antMatchers("/api/documentFile/shuKeofdSava")
                .antMatchers("/api/system/queryUserUpdatePassword/*")
                .antMatchers("/api/commonFile/fileBatchEncryptByFileServer")
                .antMatchers("/api/tCirculate/syncApprovalForwardRecord")
                .antMatchers("/api/activiti/query/syncApproveManagementCode")
                .antMatchers("/api/tServiceInfo/searchServiceByOrgId")
                .antMatchers("/api/getApprovalCountOnCompanyId/*")

                // .antMatchers("/api/tSfMeetingUser/**")
                .antMatchers("/api/sfw/**")
                .antMatchers("/api/tTopOrgRelation/initData/**")
                .antMatchers("/api/tzggNotice/getSFWNoticePage/*")
                .antMatchers("/api/swLeadershipConcernsItem/queryStatistics/*")
                .antMatchers("/api/bizSwoaBI/*")
                .antMatchers("/api/tUniversalResearchActivity/queryResearchActivityCount")
                .antMatchers("/api/tUniversalResearchActivity/queryNoReadingCount")
                .antMatchers("/api/fficialActivity/queryNoApprovalCount")
                .antMatchers("/api/system/downloadUpdateIdCardNoSql")
                .antMatchers("/api/tSysConfig/getInOrOutNetFlag")
                .antMatchers("/api/tSysConfig/getNewLoginUrl")
                .antMatchers("/api/system/queryMainOrgTree")
                .antMatchers("/api/system/queryUnitOrgTree")
                .antMatchers("/api/system/queryUnitOrgTreeByName")
                .antMatchers("/api/system/queryUserByTreeIdAndRoleId")
                .antMatchers("/api/system/queryUserByTreeIdAndRoleCode")
                .antMatchers("/api/system/sendSmsVerificationCodeWw")
                .antMatchers("/api/system/appLoginWwSec")
                .antMatchers("/api/system/generateFixesOrgCode")
                .antMatchers("/api/tSysConfig/getSysConfigByCode/in.or.out.net.flag")
                .antMatchers("/api/tSyncUserHistroyRecord/sync/callback")
                .antMatchers("/api/foreign/**")



        ;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {

        http
                // CSRF禁用，因为不使用session
                .csrf().disable()
                // 认证失败处理类
                .exceptionHandling().authenticationEntryPoint(problemSupport)
                .accessDeniedHandler(problemSupport)
                .and()
                // .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse()).and()
                .headers().frameOptions().sameOrigin()
                .and()
                // 基于token，所以不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .apply(securityConfigurerAdapter())
                .and()
                .authorizeRequests()
                .accessDecisionManager(accessDecisionManager())

                .antMatchers("/v2/api-docs/**").permitAll()
                .antMatchers("/swagger-resources/configuration/ui").permitAll()
                .antMatchers("/swagger-ui/*").permitAll()

                // 获取验证码
                .antMatchers("/api/tServiceInfo/searchServiceByOrgId").permitAll()
                .antMatchers("/api/catpcha/digitalCaptcha").permitAll()
                //发送短信验证码
                .antMatchers("/api/system/sendSmsVerificationCode").permitAll()
                //验证手机验证码是否正确
                .antMatchers("/api/system/checkVerificationCode").permitAll()

                .antMatchers("/api/SsoGetUserController/getUserNumber").permitAll()
                .antMatchers("/api/system/sendCaptcha").permitAll()
                .antMatchers("/synchronizationController/test").permitAll()
                .antMatchers("/synchronizationController/test1").permitAll()
                .antMatchers("/api/system/checkCaptcha").permitAll()
                .antMatchers("/api/accessToken/getAccessToken").permitAll()
                // 下载图片
                .antMatchers("/api/tSysBannerImage/downloadImgFileByKey").permitAll()

                .antMatchers("/api/cformLogOpration/create").permitAll()
                .antMatchers("/api/bizLeaderOutgoInbox/queryBizLeaderOutCount").permitAll()
                .antMatchers("/api/system/login").permitAll()
                .antMatchers("/api/system/swLogin").permitAll()
                .antMatchers("/api/system/swLoginPC").permitAll()
                .antMatchers("/api/commonFile/*").permitAll()
                .antMatchers("/api/system/refreshToken").authenticated()
                .antMatchers("/api/system/cscpMenus").authenticated()
                .antMatchers("/api/system/cscpCurrentUserDetails").authenticated()

                .antMatchers("/api/system/cscpUserDetailsByUserId").authenticated()
                .antMatchers("/api/system/cscpUserPassword").authenticated()

                .antMatchers(HttpMethod.GET, "/api/system/cscpRolessByCriteria").authenticated()
                .antMatchers(HttpMethod.PUT, "/api/system/cscpUserDetails").authenticated()

                .antMatchers("/api/system/cscpUserPassword").permitAll()
                .antMatchers("/api/system/updateUserPassword").permitAll()
                .antMatchers("/api/system/cscpUserPasswordRule").permitAll()
                .antMatchers("/api/system/crmToBiyi").permitAll()
                .antMatchers("/api/tSysConfig/getSwDomainAddressConfig").permitAll()
                 .antMatchers("/api/documentFile/wpsDownloadOFD/*").permitAll()
                // .antMatchers("/api/documentFile/shuKeofdSava/*")
                 .antMatchers("/api/documentFile/shuKeofdSava").permitAll()
                 .antMatchers("/api/activiti/query/getCountActivelyTaskListByStrId").permitAll()
                // 批注ofd文件保存
                 .antMatchers("/api/documentFile/ofdFileSava").permitAll()

                .antMatchers("/api/external/getAllCornerCountByStrId").permitAll()
                .antMatchers("/api/external/**").permitAll()

                .antMatchers("/api/enclosureFile/wpsDownloadOFD/*").permitAll()

                .antMatchers("/api/system/queryUserUpdatePassword/*").permitAll()

                .antMatchers("/api/commonFile/fileBatchEncryptByFileServer").permitAll()

                .antMatchers("/api/tCirculate/syncApprovalForwardRecord").permitAll()

                .antMatchers("/api/activiti/query/syncApproveManagementCode").permitAll()
                .antMatchers("/api/getApprovalCountOnCompanyId/*").permitAll()
                .antMatchers("/api/tzggNotice/getSFWNoticePage/*").permitAll()
                .antMatchers("/api/swLeadershipConcernsItem/queryStatistics/*").permitAll()
                .antMatchers("/api/bizSwoaBI/*").permitAll()
                .antMatchers("/api/tUniversalResearchActivity/queryResearchActivityCount").permitAll()
                .antMatchers("/api/tUniversalResearchActivity/queryNoReadingCount").permitAll()
                .antMatchers("/api/fficialActivity/queryNoApprovalCount").permitAll()
                .antMatchers("/api/tSysConfig/getInOrOutNetFlag").permitAll()
                .antMatchers("/api/tSysConfig/getNewLoginUrl").permitAll()
                //单点登录相关
                .antMatchers("/api/openapi/system/v1/createToken").permitAll()
                .antMatchers("/api/openapi/system/v1/validateToken").permitAll()
                .antMatchers("/api/openapi/system/v1/logout").permitAll()
                .antMatchers("/api/openapi/system/v1/getCurrUserInfo").permitAll()
                .antMatchers("/api/openapi/system/v1/refreshToken").permitAll()
                .antMatchers("/api/system/downloadUpdateIdCardNoSql").permitAll()
                .antMatchers("/api/system/sendSmsVerificationCodeWw").permitAll()
                .antMatchers("/api/system/appLoginWwSec").permitAll()
                .antMatchers("/api/tSysConfig/getSysConfigByCode/in.or.out.net.flag").permitAll()


                // .antMatchers("/api/tSfMeetingUser/**").permitAll()
                .antMatchers("/api/sfw/**").permitAll()
                .antMatchers("/api/tTopOrgRelation/initData/**").permitAll()
                .antMatchers("/api/system/generateFixesOrgCode").permitAll()
                .antMatchers("/api/foreign/**").permitAll()
                .antMatchers("/api/**").authenticated()


        ;
        //退出登录
        http.logout().logoutUrl("/api/logout").logoutSuccessHandler(logoutSuccessHandler);
        // 添加CORS filter
        http.addFilterBefore(corsFilter, UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(corsFilter, LogoutFilter.class);
    }

    /**
     * 添加token拦截校验的过滤器
     *
     * @return
     */
    private JWTConfigurer securityConfigurerAdapter() {
        return new JWTConfigurer(tokenProvider);
    }

}
