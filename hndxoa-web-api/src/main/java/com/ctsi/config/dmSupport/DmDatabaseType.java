//package com.ctsi.config.dmSupport;
//
//import org.apache.shardingsphere.spi.database.type.BranchDatabaseType;
//import org.apache.shardingsphere.spi.database.type.DatabaseType;
//import org.apache.shardingsphere.underlying.common.database.type.DatabaseTypes;
//
//import java.util.Collection;
//import java.util.Collections;
//
///**
// * Database type of DM.
// */
//public final class DmDatabaseType  implements BranchDatabaseType {
//
//    @Override
//    public String getName() {
//        System.out.println("获取达梦名称");
//        return "DM";
//    }
//
//    @Override
//    public Collection<String> getJdbcUrlPrefixAlias() {
//        return Collections.emptyList();
//    }
//
//    @Override
//    public DMDataSourceMetaData getDataSourceMetaData(final String url, final String username) {
//        return new DMDataSourceMetaData(url, username);
//    }
//
//    @Override
//    public DatabaseType getTrunkDatabaseType() {
//        return DatabaseTypes.getActualDatabaseType("Oracle");
//    }
//}