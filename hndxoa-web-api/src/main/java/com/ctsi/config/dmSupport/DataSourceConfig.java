//package com.ctsi.config.dmSupport;
//
//import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
//import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;
//import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
//import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
//import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
//import com.ctsi.hndx.config.DbConst;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//import org.springframework.transaction.support.TransactionTemplate;
//
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//import java.util.Map;
//
//@Configuration
//@MapperScan(basePackages = {"com.ctsi.**.mapper", "com.ctsi.**.*Repository", "com.ctsi.ssdc.admin.repository"})
//@EnableTransactionManagement
//public class DataSourceConfig {
//
//    /**
//     * 动态数据源配置项
//     */
//    @Resource
//    private DynamicDataSourceProperties properties;
//
//    /**
//     * 动态数据源提供者
//     * 只使用配置文件中定义的数据源，不再集成ShardingSphere
//     * @return
//     */
//    @Bean
//    public DynamicDataSourceProvider dynamicDataSourceProvider() {
//        Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();
//        return new AbstractDataSourceProvider() {
//            @Override
//            public Map<String, DataSource> loadDataSources() {
//                // 只创建配置文件中定义的数据源
//                return createDataSourceMap(datasourceMap);
//            }
//        };
//    }
//
//    /**
//     * 将dynamic-datasource设置为主数据源
//     * 默认使用master数据源
//     * @return
//     */
//    @Primary
//    @Bean
//    public DynamicRoutingDataSource dataSource() {
//        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
//        // 设置默认数据源为master
//        dataSource.setPrimary(DbConst.MASTER);
//        dataSource.setStrict(properties.getStrict());
//        dataSource.setStrategy(properties.getStrategy());
//        dataSource.setP6spy(properties.getP6spy());
//        dataSource.setSeata(properties.getSeata());
//        return dataSource;
//    }
//
//    /**
//     * 配置事务管理器
//     * PlatformTransactionManager是Spring的事务管理接口，用于抽象底层的事务管理机制
//     * 主要用途：
//     *    提供底层的事务能力
//     *    Transaction注解的使用
//     * @param dynamicRoutingDataSource
//     * @return
//     */
//    @Bean(name = "platformTransactionManager")
//    public PlatformTransactionManager transactionManager(DynamicRoutingDataSource dynamicRoutingDataSource) {
//        return new DataSourceTransactionManager(dynamicRoutingDataSource);
//    }
//
//
//    /**
//     * TransactionTemplate:
//     * 提供更为细化粒度的事务能力，依赖于PlatformTransactionManager能力
//     *
//     * @param platformTransactionManager
//     * @return
//     */
//    @Bean
//    public TransactionTemplate transactionTemplate(@Qualifier("platformTransactionManager") PlatformTransactionManager platformTransactionManager) {
//        return new TransactionTemplate(platformTransactionManager);
//    }
//
//}