-- SQL提交规范
-- 格式：日期，姓名，功能作用说明

-- 案例：
-- 1、张三 2024年12月02日 新建XXX表，XX某块
-- 2、张三 2024年12月02日 XX表新增XX字段

-- 1、yangjieling 2025年2月19日 cscp_user用户表新增身份证号码字段
alter table "cscp_user" add column(ID_CARD_NO VARCHAR(255));
comment on column "cscp_user"."ID_CARD_NO" is '身份证号码';

-- 2、2025/03/03 t_sync_app_system_manage 推送应用表新增所属机构编码字段
ALTER TABLE "t_sync_app_system_manage" add "in_owned_org_id" VARCHAR(128) NULL;
COMMENT ON COLUMN "t_sync_app_system_manage"."in_owned_org_id" IS '拥有机构编码';

ALTER TABLE "t_sync_app_system_manage" ADD "request_mode" varchar(5) DEFAULT '1' NULL;
COMMENT ON COLUMN "t_sync_app_system_manage"."request_mode" IS '请求方式(0:MQ,1:HTTP[默认])';

-- 历史数据request_mode初始化为1
update t_sync_app_system_manage set request_mode = '1' where request_mode is null and deleted = 0;

-- 3、2025/03/03 t_sys_dict/t_sys_dict_record 用户职级-字典(对应导入模板
INSERT INTO "t_sys_dict" ("id","code","name","remarks","create_by","create_name","create_time","update_by","update_name","update_time","department_id","company_id","tenant_id","deleted","dept_id") VALUES (1896455771131469825,'userRank','用户职级',NULL,2,'系统管理员','2025-03-03 15:01:05',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204381559476226, '一级主任科员', '一级主任科员', 15, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:52:31.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204002742521857, '一级巡视员', '一级巡视员', 5, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:51:01.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204134892457985, '一级调研员', '一级调研员', 9, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:51:32.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204466099867650, '三级主任科员', '三级主任科员', 17, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:52:51.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204243126472706, '三级调研员', '三级调研员', 11, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:51:58.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896459290769088514, '乡科级副职', '乡科级副职', 14, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:15:04.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896459246082973698, '乡科级正职', '乡科级正职', 13, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:14:53.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204421493444610, '二级主任科员', '二级主任科员', 16, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:52:40.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204047030177793, '二级巡视员', '二级巡视员', 6, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:51:11.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204203486105602, '二级调研员', '二级调研员', 10, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:51:48.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896459010765742081, '厅局级副职', '厅局级副职', 4, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:13:57.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896458938279780354, '厅局级正职', '厅局级正职', 3, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:13:40.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896459175547363330, '县处级副职', '县处级副职', 8, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:14:37.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896459128118173698, '县处级正职', '县处级正职', 7, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:14:25.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204541148549122, '四级主任科员', '四级主任科员', 18, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:53:09.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1951204301192417281, '四级调研员', '四级调研员', 12, 0, 1896455771131469825, 2, '系统管理员', '2025-08-01 16:52:12.000', NULL, NULL, NULL, '', 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896458856855756802, '省部级副职', '省部级副职', 2, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:13:21.000', NULL, NULL, NULL, NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896458668086910978, '省部级正职', '省部级正职', 1, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:12:36.000', NULL, NULL, '2025-03-03 15:13:06.000', NULL, 0);
INSERT INTO "t_sys_dict_record"
("id", "code", "name", "sorted", "defaults", "dict_id", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "remarks", "deleted")
VALUES(1896459556079788034, '科员及以下', '科员及以下', 19, 0, 1896455771131469825, 2, '系统管理员', '2025-03-03 15:16:07.000', NULL, NULL, '2025-08-01 16:53:20.000', NULL, 0);


-- 2025-03-19 调整应用管理字段长度
ALTER TABLE t_sync_app_system_manage MODIFY IN_OWNED_ORG_ID VARCHAR(4096);
-- 2025-03-21 增加应用管理自动推送字段
ALTER TABLE "t_sync_user_histroy_record" add "request_mode" VARCHAR(5) NULL;
COMMENT ON COLUMN "t_sync_user_histroy_record"."request_mode" IS '请求方式(0:MQ,1:HTTP)';
-- 2025-03-25 推送用户记录、机构记录添加request_mode字段
ALTER TABLE "t_sync_org_histroy_record" add "request_mode" VARCHAR(5) NULL;
COMMENT ON COLUMN "t_sync_org_histroy_record"."request_mode" IS '请求方式(0:MQ,1:HTTP)';


ALTER TABLE "t_sync_app_system_manage" add "auto_push" VARCHAR(5) DEFAULT '0' NULL;
COMMENT ON COLUMN "t_sync_app_system_manage"."auto_push" IS '自动推送(0:关闭,1:开启)';

-- 2025-04-01 机构表新增外部来源id存储字段
ALTER TABLE "cscp_org" add "external_id" VARCHAR(255) NULL;
COMMENT ON COLUMN "cscp_org"."external_id" IS '外部来源主键id';

-- 2025-04-08 机构表新增外部机构来源字段
ALTER TABLE "cscp_org" add "org_origin" VARCHAR(32) NULL;
COMMENT ON COLUMN "cscp_org"."org_origin" IS '外部机构来源';

-- 2025-04-23 新增外部机构用导入菜单
INSERT INTO "cscp_menus" ("id", "name", "icon", "title", "url", "http_method", "component", "parent_id", "type", "permission_code", "order_by", "platform_admin_use", "create_by", "create_name", "create_time", "update_by", "update_time", "update_name", "deleted", "home_display", "home_area", "home_icon", "home_count_url", "iframe_url", "home_iframe_display", "extranet_link_url")
VALUES(1914202528456974337, 'sysOrgImport', 'ios-folder-outline', '外部机构用户导入', 'sysOrgImport', NULL, 'views/hnjcoa/system/sysOrgImport/sysOrgImport.vue', 800, 'menu2', '', 1159, 1, 2, '系统管理员', '2025-04-21 14:20:22.000', NULL, '2025-04-21 14:32:50.000', NULL, 0, 0, '1', '', '', '', 0, NULL);

ALTER TABLE "t_address_book" MODIFY JOB_TITLE VARCHAR(1000) NULL;

-- 2025-05-12 新增机构审核表
CREATE TABLE "cscp_org_audit_remove"
(
    "id" BIGINT NOT NULL,
    "org_id" BIGINT,
    "org_name" VARCHAR(255),
    "org_code" VARCHAR(128),
    "audit_create_name" VARCHAR(255),
    "audit_explain" VARCHAR(500),
    "audit_type" INT DEFAULT 0 NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(255),
    "update_by" BIGINT,
    "update_name" VARCHAR(255),
    "deleted" INT DEFAULT 0 NOT NULL,
    "create_time" TIMESTAMP(0),
    "update_time" TIMESTAMP(0),
    NOT CLUSTER PRIMARY KEY("id"));

COMMENT ON TABLE "cscp_org_audit_remove" IS '机构删除审核表';
COMMENT ON COLUMN "cscp_org_audit_remove"."audit_create_name" IS '审核申请人';
COMMENT ON COLUMN "cscp_org_audit_remove"."audit_explain" IS '审核说明';
COMMENT ON COLUMN "cscp_org_audit_remove"."audit_type" IS '审核类型[0:未审核 1:已审核]';
COMMENT ON COLUMN "cscp_org_audit_remove"."create_by" IS '创建人id';
COMMENT ON COLUMN "cscp_org_audit_remove"."create_name" IS '创建人名称';
COMMENT ON COLUMN "cscp_org_audit_remove"."create_time" IS '创建时间';
COMMENT ON COLUMN "cscp_org_audit_remove"."deleted" IS '删除标识[0:正常 1:删除]';
COMMENT ON COLUMN "cscp_org_audit_remove"."org_code" IS '机构编码';
COMMENT ON COLUMN "cscp_org_audit_remove"."org_id" IS '机构id';
COMMENT ON COLUMN "cscp_org_audit_remove"."org_name" IS '机构名称';
COMMENT ON COLUMN "cscp_org_audit_remove"."update_by" IS '更新人id';
COMMENT ON COLUMN "cscp_org_audit_remove"."update_name" IS '更新人名称';
COMMENT ON COLUMN "cscp_org_audit_remove"."update_time" IS '更新时间';


ALTER TABLE "cscp_org_audit_remove" ADD company_id BIGINT NULL;
ALTER TABLE "cscp_org_audit_remove" ADD department_id BIGINT NULL;
ALTER TABLE "cscp_org_audit_remove" ADD tenant_id BIGINT NULL;

-- 2025-05-19 新增是否显示通讯录
ALTER TABLE "cscp_user" ADD is_display INTEGER DEFAULT 1 NULL;
COMMENT ON COLUMN "cscp_user"."is_display" IS '是否显示通讯录,0不显示，1显示';

-- 2025-05-21 新增删除方式
ALTER TABLE "cscp_org_audit_remove" ADD remove_type INTEGER DEFAULT 1 NULL;
COMMENT ON COLUMN "cscp_org_audit_remove"."remove_type" IS '删除方式[0:直接删除;1:申请删除]';

-- 2025-06-05 新增身份证号码备份
ALTER TABLE "cscp_user" ADD id_card_no_backup VARCHAR(255) NULL;
COMMENT ON COLUMN "cscp_user"."id_card_no_backup" IS '身份证备份';

-- 2025-06-18 导出用户记录表
CREATE TABLE "t_sync_export_user_record" (
    "id" BIGINT NOT NULL,
    "file_name" VARCHAR(255) NULL,
    "file_path" VARCHAR(512) NULL,
    "file_size" BIGINT NULL,
    "export_status" VARCHAR(50) NULL,
    "export_count" INT NULL,
    "export_org" VARCHAR(255) NULL,
    "operator_id" BIGINT NULL,
    "operator_name" VARCHAR(255) NULL,
    "deleted" INT NULL,
    "tenant_id" BIGINT NULL,
    "company_id" bigint NULL,
    "department_id" BIGINT NULL,
    "create_by" BIGINT NULL,
    "create_name" VARCHAR(255) NULL,
    "create_time" TIMESTAMP NULL,
    "update_by" BIGINT NULL,
    "update_name" VARCHAR(255) NULL,
    "update_time" TIMESTAMP NULL,
    NOT CLUSTER PRIMARY KEY("ID"));


COMMENT ON COLUMN "t_sync_export_user_record"."export_count" IS '导出条数';
COMMENT ON COLUMN "t_sync_export_user_record"."export_org" IS '数据所属单位';
COMMENT ON COLUMN "t_sync_export_user_record"."file_name" IS '文件名';
COMMENT ON COLUMN "t_sync_export_user_record"."file_path" IS '文件路径';
COMMENT ON COLUMN "t_sync_export_user_record"."file_size" IS '文件大小';
COMMENT ON COLUMN "t_sync_export_user_record"."operator_id" IS '操作人id';
COMMENT ON COLUMN "t_sync_export_user_record"."operator_name" IS '操作人';
COMMENT ON COLUMN "t_sync_export_user_record"."export_status" IS '导出状态';

-- 2025-06-18 机构表添加授权应用管理
ALTER TABLE "cscp_user" ADD push_app_code VARCHAR(255) NULL;
COMMENT ON COLUMN "cscp_user"."push_app_code" IS '授权推送应用';

-- 2025-06-18 机构表添加授权应用管理
ALTER TABLE "cscp_org" ADD push_app_code VARCHAR(255) NULL;
COMMENT ON COLUMN "cscp_org"."push_app_code" IS '授权推送应用';

-- 2025-06-26 统一菜单、统一角色配置
-- value值加密，需要手动配置对应服务ip:port
-- OA
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1917136537081126914, 'sync:rbac:oa:server:url', 'NULL', 'OA', 2, '系统管理员', '2025-04-29 16:39:04', null, null, '2025-06-03 14:31:29', 0);
-- SFW
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1917136636586795010, 'sync:rbac:sfw:server:url', 'NULL', '三服务', 2, '系统管理员', '2025-04-29 16:39:27', null, null, '2025-05-07 14:20:34', 0);
-- DUCHA
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1917136707088850945, 'sync:rbac:db:server:url', 'NULL', '督查', 2, '系统管理员', '2025-04-29 16:39:44', null, null, '2025-04-29 17:14:21', 0);
-- GWCSOA
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1937317128063238145, 'sync:rbac:gwcs:server:url', 'NULL', '公文传输', 2, '系统管理员', '2025-06-24 09:09:32', null, null, null, 0);
-- DIAOYAN
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1937317410201485313, 'sync:rbac:dy:server:url', 'NULL', '调研', 2, '系统管理员', '2025-06-24 09:10:39', null, null, '2025-06-26 16:42:06', 0);
-- SJCSX
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1937394566604603394, 'sync:rbac:exploit:server:url', 'NULL', '涉基层事项', 2, '系统管理员', '2025-06-24 14:17:14', null, null, '2025-06-24 15:34:13', 0);
-- WJZD
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1938052024970321921, 'sync:rbac:wjzd:server:url', 'NULL', '文件制定', 2, '系统管理员', '2025-06-26 09:49:45', null, null, '2025-06-26 09:58:17', 0);
-- BASC
insert into "t_sys_config"("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values(1938076073620480001, 'sync:rbac:basc:server:url', 'NULL', '备案审查', 2, '系统管理员', '2025-06-26 11:25:18', null, null, '2025-06-26 11:26:24', 0);

-- 2025-06-30 机构类型
ALTER TABLE "cscp_org" ADD "org_level_type" VARCHAR(5) NULL;
COMMENT ON COLUMN "cscp_org"."org_level_type" IS '机构类型(0:省、1:市、2:县、3:乡镇)';

ALTER TABLE "cscp_org" ADD org_classify varchar(10) NULL;
COMMENT ON COLUMN "cscp_org"."org_classify" IS '机构分类[00:默认;01:学校;02:医院]';

-- 2025-06-30 添加应用授权市县区单位表
CREATE TABLE t_sync_app_system_manage_company
(
    id                NUMBER(32,0) NOT NULL,
    app_id            NUMBER(32,0),
    app_name          VARCHAR(255),
    app_code          VARCHAR(100),
    org_id            NUMBER(32,0),
    org_name          VARCHAR(255),
    company_id        NUMBER(32,0),
    department_id     NUMBER(32,0),
    tenant_id         NUMBER(32,0),
    deleted           CHAR(5) DEFAULT '0' NOT NULL,
    create_by         NUMBER(32,0),
    create_name       VARCHAR(255),
    create_time       TIMESTAMP(6),
    update_by         NUMBER(32,0),
    update_name       VARCHAR(255),
    update_time       TIMESTAMP(6),
    PRIMARY KEY (id)
)
    PARTITION BY HASH (id)
    PARTITIONS 10
    STORAGE (ON "HN_SWOA", CLUSTERBTR);

COMMENT ON TABLE "t_sync_app_system_manage_company" IS '应用授权市县区单位';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."app_code" IS '应用编码';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."app_id" IS '应用id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."app_name" IS '应用名称';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."company_id" IS '单位id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."create_by" IS '创建人id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."create_name" IS '创建人名称';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."create_time" IS '创建时间';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."deleted" IS '是否删除[0:否;1:是]';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."department_id" IS '部门id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."id" IS '主键';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."org_id" IS '机构id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."org_name" IS '机构名称';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."tenant_id" IS '租户id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."update_by" IS '更新人id';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."update_name" IS '更新人名称';
COMMENT ON COLUMN "t_sync_app_system_manage_company"."update_time" IS '更新时间';

-- 2025-07-03 修改应用管理字段长度
ALTER TABLE t_sync_app_system_manage MODIFY sync_url VARCHAR(255);
ALTER TABLE t_sync_app_system_manage MODIFY in_system_url VARCHAR(255);

-- 2025-07-08 区划授权表添加索引
CREATE INDEX idx_manage_company_app_id ON "t_sync_app_system_manage_company" ("app_id");
CREATE INDEX idx_manage_company_org_id ON "t_sync_app_system_manage_company" ("org_id");

-- 2025-07-23 推送机构记录表添加批次字段
ALTER TABLE "t_sync_org_histroy_record" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202504" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202505" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202506" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202507" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202508" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202509" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202510" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202511" ADD event_id VARCHAR(50) NULL;
ALTER TABLE "t_sync_org_histroy_record_202512" ADD event_id VARCHAR(50) NULL;
-- 被动推送url
ALTER TABLE "t_sync_app_system_manage" ADD passive_url varchar(128) NULL;

-- 2025-07-31 被动推送-拉取次数
ALTER TABLE "t_sync_org_histroy_record" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202504" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202505" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202506" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202507" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202508" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202509" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202510" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202511" ADD sync_count int NULL;
ALTER TABLE "t_sync_org_histroy_record_202512" ADD sync_count int NULL;

ALTER TABLE "t_sync_user_histroy_record" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202504" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202505" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202506" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202507" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202508" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202509" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202510" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202511" ADD sync_count int NULL;
ALTER TABLE "t_sync_user_histroy_record_202512" ADD sync_count int NULL;
