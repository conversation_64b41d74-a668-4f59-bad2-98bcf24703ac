-- SQL提交规范
-- 格式：日期，姓名，功能作用说明

-- 案例：
-- 1、张三 2024年12月02日 新建XXX表，XX某块
-- 2、张三 2024年12月02日 XX表新增XX字段

-- 宋勇 2025年2月20日， 删除机构表组合唯一索引，新增机构编码索引
SELECT
    INDEX_NAME,
    COLUMN_NAME,
    COLUMN_POSITION
FROM ALL_IND_COLUMNS
WHERE TABLE_NAME = 'cscp_org'
ORDER BY INDEX_NAME, COLUMN_POSITION;

DROP INDEX INDEX33572873;
CREATE INDEX idx_org_code ON cscp_org(org_code);
-- 2025年2月26日 删除约束，约束条件要使用达梦客户才能查看到
alter table HN_SWOA."cscp_org" drop constraint "unique_org_code";

-- 2025年2月28日 修改用户表字段手写签批默认值
ALTER TABLE cscp_user  MODIFY is_write_sign INT(10) DEFAULT 0;

-- 2025年3月4日 新增部门管理权限表
CREATE TABLE "t_dept_management_authority" (
   "ID" BIGINT NOT NULL,
   "USER_ID" BIGINT NOT NULL,
   "DEPT_ID" BIGINT NOT NULL,
   "DEPT_NAME" VARCHAR(32),
   "CREATE_TIME" TIMESTAMP,
   "CREATE_BY" BIGINT,
   "CREATE_NAME" VARCHAR(32),
   "UPDATE_TIME" TIMESTAMP,
   "UPDATE_BY" BIGINT,
   "UPDATE_NAME" VARCHAR(32),
   "DEPARTMENT_ID" BIGINT,
   "COMPANY_ID" BIGINT,
   "TENANT_ID" BIGINT,
   "DELETED" INT DEFAULT 0,
   CLUSTER PRIMARY KEY("ID")
);

COMMENT ON TABLE "T_DEPT_MANAGEMENT_AUTHORITY" IS '部门管理权限表';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."ID" IS '主键ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."USER_ID" IS '管理员用户ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."DEPT_ID" IS '被管理的部门ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."DEPT_NAME" IS '被管理的部门名称';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."CREATE_BY" IS '创建人ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."UPDATE_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."UPDATE_BY" IS '最后更新人ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."UPDATE_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."DEPARTMENT_ID" IS '部门ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."COMPANY_ID" IS '单位ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."TENANT_ID" IS '租户ID';
COMMENT ON COLUMN "T_DEPT_MANAGEMENT_AUTHORITY"."DELETED" IS '删除标识（0：未删除，1：已删除）';


-- 2025年3月19日 宋勇 添加默认部门角色
INSERT INTO cscp_roles (id, name, role_type, create_by, create_name, create_time, update_by, update_name, update_time, department_id, company_id, tenant_id, deleted, main_tariff, surcharge, range_type, HMAC_ID, HMAC_NAME) VALUES (1453911658682986499, '部门管理', 2, 1, '安全保密管理员', '2025-03-05 12:24:01', null, null, null, null, null, null, 0, null, null, null, null, null);

-- 2025年3月19日 宋勇 添加角色操作数据
ALTER TABLE "cscp_menus"
    ADD "extranet_link_url" VARCHAR(5000);
INSERT INTO cscp_menus(id, name, icon, title, url, http_method, component, parent_id, type, permission_code, order_by, platform_admin_use, create_by, create_name, create_time, update_by, update_time, update_name, deleted, home_display, home_area, home_icon, home_count_url, iframe_url, home_iframe_display, extranet_link_url) VALUES (302, 'role-add', 'person-stalker', '增加角色', 'roleAdd', '*', 'views/biyi-admin/src/system-page/authority/roleEdit.vue', 300, 'non-menu', 'cscp.role.add', 383, 1, null, null, null, null, '2024-01-30 00:00:00', null, 0, 0, null, null, null, null, 0, null);
INSERT INTO cscp_menus(id, name, icon, title, url, http_method, component, parent_id, type, permission_code, order_by, platform_admin_use, create_by, create_name, create_time, update_by, update_time, update_name, deleted, home_display, home_area, home_icon, home_count_url, iframe_url, home_iframe_display, extranet_link_url) VALUES (303, 'role-del', 'person-stalker', '删除角色', null, '*', 'views/biyi-admin/src/system-page/authority/userEdit.vue', 300, 'button', 'cscp.role.del', 384, 1, null, null, null, null, '2024-01-30 00:00:00', null, 0, 0, null, null, null, null, 0, null);
INSERT INTO cscp_menus(id, name, icon, title, url, http_method, component, parent_id, type, permission_code, order_by, platform_admin_use, create_by, create_name, create_time, update_by, update_time, update_name, deleted, home_display, home_area, home_icon, home_count_url, iframe_url, home_iframe_display, extranet_link_url) VALUES (304, 'role-edit', 'person-stalker', '编辑角色', 'roleEdit', '*', 'views/biyi-admin/src/system-page/authority/roleEdit.vue', 300, 'non-menu', 'cscp.role.edit', 385, 1, null, null, null, null, '2024-01-30 00:00:00', null, 0, 0, null, null, null, null, 0, null);
INSERT INTO cscp_menus(id, name, icon, title, url, http_method, component, parent_id, type, permission_code, order_by, platform_admin_use, create_by, create_name, create_time, update_by, update_time, update_name, deleted, home_display, home_area, home_icon, home_count_url, iframe_url, home_iframe_display, extranet_link_url) VALUES (1427200590725455873, null, 'ios-folder-outline', '关联用户', null, null, null, 300, 'button', 'cscp.role.user', 386, 1, 1, '管理员', '2021-08-16 17:28:32', null, '2024-01-30 00:00:00', null, 0, 0, null, null, null, null, 0, null);

-- 2025年3月19日 宋勇 补充角色管理操作管理关系
INSERT INTO cscp_role_menu (id,menu_id, role_id)
VALUES (1899380363979583490,302, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id)
VALUES (1899380363979583491,303, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id)
VALUES (1899380363979583492,304, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id)
VALUES (1899380363979583493,1427200590725455873, 1453911658682986498);

-- 2025年3月24日 宋勇 部门管理员管理用户管理角色
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583494,1453911658682986498, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583495,202, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583496,203, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583497,204, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583498,1468133849052643329, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583499,1478640057194774529, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583500,1478640332009766914, 1453911658682986498);
INSERT INTO cscp_role_menu (id,menu_id, role_id) VALUES (1899380363979583501,1481443298928369666, 1453911658682986498);

-- 2025年3月26日 宋勇 统一机构单位管理员新增区划机构管理菜单角色
INSERT INTO cscp_role_menu (id,
                            role_id,
                            menu_id)
SELECT (SELECT id FROM cscp_menus WHERE title = '区划机构管理'),
       1453911658682986498,
       (SELECT id FROM cscp_menus WHERE title = '区划机构管理')
FROM dual
WHERE EXISTS(SELECT 1 FROM cscp_menus WHERE title = '区划机构管理');

-- 2025年3月27日 宋勇 表 cscp_user 添加字段 person_label
ALTER TABLE cscp_user  ADD person_label VARCHAR(255);
COMMENT ON COLUMN cscp_user.person_label IS '人员标签，多个标签用英文逗号分隔';

-- 2025年3月28日 宋勇 新增人员标签字典数据
INSERT INTO t_sys_dict(id, code, name, remarks, create_by, create_name, create_time, update_by, update_name, update_time, department_id, company_id, tenant_id, deleted, dept_id) VALUES (1905097227639955457, 'person_label', '人员标签', null, 2, '系统管理员', '2025-03-27 11:19:09', null, null, null, null, null, null, 0, null);
INSERT INTO t_sys_dict_record(id, code, name, sorted, defaults, dict_id, create_by, create_name, create_time, update_by, update_name, update_time, remarks, deleted) VALUES (1905097329926447106, '1', '会议', 1, 0, 1905097227639955457, 2, '系统管理员', '2025-03-27 11:19:33', null, null, null, null, 0);
INSERT INTO t_sys_dict_record(id, code, name, sorted, defaults, dict_id, create_by, create_name, create_time, update_by, update_name, update_time, remarks, deleted) VALUES (1905097356606414849, '2', '调研', 2, 0, 1905097227639955457, 2, '系统管理员', '2025-03-27 11:19:39', null, null, null, null, 0);

-- 2025年4月3日 宋勇 新增推送用户默认登录密码配置
INSERT INTO t_sys_config (id, code, "value", remark, create_by, create_name, create_time, update_by, update_name, update_time, deleted) VALUES (1907613973888303106, 'login:pushDefaultPassword', 'MQ==', '推送用户默认登录密码：1-是，0-否', 2, '系统管理员', '2025-04-03 09:59:48', null, null, null, 0);


-- 2025年4月7日 宋勇 新增人社厅人员表
CREATE TABLE "BIZ_HRS_USER_INFO" (
     "ID" BIGINT NOT NULL,
     "STR_USER_ID" VARCHAR(32),
     "ID_CARD_NO" VARCHAR(32),
     "STR_MOBILE" VARCHAR(64),
     "STR_UNIT_NAME" VARCHAR(128),
     "CREDIT_CODE" VARCHAR(64),
     "CREATE_TIME" TIMESTAMP,
     "CREATE_BY" BIGINT,
     "CREATE_NAME" VARCHAR(32),
     "UPDATE_TIME" TIMESTAMP,
     "UPDATE_BY" BIGINT,
     "UPDATE_NAME" VARCHAR(32),
     "DEPARTMENT_ID" BIGINT,
     "COMPANY_ID" BIGINT,
     "TENANT_ID" BIGINT,
     "DELETED" INT DEFAULT 0,
     CLUSTER PRIMARY KEY("ID")) ;

COMMENT ON TABLE "BIZ_HRS_USER_INFO" IS '人社厅人员表';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."STR_USER_ID" IS '姓名';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."ID_CARD_NO" IS '身份证号';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."STR_MOBILE" IS '手机号';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."STR_UNIT_NAME" IS '单位名称';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."CREDIT_CODE" IS '单位统一社会信用码';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."CREATE_BY" IS '创建人ID';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."UPDATE_TIME" IS '最后更新时间';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."UPDATE_BY" IS '最后更新人ID';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."UPDATE_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."DEPARTMENT_ID" IS '部门ID';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."COMPANY_ID" IS '公司ID';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."TENANT_ID" IS '租户ID';
COMMENT ON COLUMN "BIZ_HRS_USER_INFO"."DELETED" IS '是否删除：0-未删除，1-已删除';

-- 2025年4月8日 宋勇 新增人社厅人员表索引
CREATE INDEX idx_str_unit_name ON BIZ_HRS_USER_INFO(STR_UNIT_NAME);
CREATE INDEX idx_biz_hrs_user_info_user_id ON BIZ_HRS_USER_INFO(STR_USER_ID);
CREATE INDEX idx_biz_hrs_user_info_id_card_no ON BIZ_HRS_USER_INFO(ID_CARD_NO);
CREATE INDEX idx_biz_hrs_user_info_mobile ON BIZ_HRS_USER_INFO(STR_MOBILE);

-- 2025年5月21日 宋勇 新增学习阅读授权菜单
INSERT INTO cscp_menus(id, name, icon, title, url, http_method, component, parent_id, type, permission_code, order_by, platform_admin_use, create_by, create_name, create_time, update_by, update_time, update_name, deleted, home_display, home_area, home_icon, home_count_url, iframe_url, home_iframe_display, extranet_link_url) VALUES (1925121465357746178, '/read', 'ios-folder-outline', '学习阅读授权', '/read', null, 'views/main/main.vue', 1919938138672828418, 'menu2', '', 3, 1, 2, '系统管理员', '2025-05-21 17:28:19', null, null, null, 0, 0, '1', '', '', '', 0, 'http://科创学习阅读ip和端口号/read/loginbysso.do?');

-- 2025年5月21日 宋勇 推送机构和用户表新增推送类型
ALTER TABLE t_sync_user_histroy_record  ADD operate_type VARCHAR(6);
COMMENT ON COLUMN t_sync_user_histroy_record.operate_type IS '0-手动推送，1-自动推送';
ALTER TABLE t_sync_org_histroy_record  ADD operate_type VARCHAR(6);
COMMENT ON COLUMN t_sync_org_histroy_record.operate_type IS '0-手动推送，1-自动推送';

-- 2025年5月22日 宋勇 应用系统管理表新增角色id和角色名称
ALTER TABLE t_sync_app_system_manage  ADD role_id BIGINT;
COMMENT ON COLUMN t_sync_app_system_manage.role_id  IS '应用角色ID';
ALTER TABLE t_sync_app_system_manage   ADD role_name  VARCHAR(64);
COMMENT ON COLUMN t_sync_app_system_manage.role_name  IS '应用角色名称';

-- 2025年6月6日 宋勇 修改机构表敏感字段别名字段
UPDATE cscp_org
SET alias_name = REGEXP_REPLACE(
        REGEXP_REPLACE(
                REGEXP_REPLACE(org_name, '国安', 'guoan'),
                '机要', 'jiyao'
        ),
        '保密', 'baomi'
                 )
WHERE (
    org_name LIKE '%国安%'
        OR org_name LIKE '%机要%'
        OR org_name LIKE '%保密%'
    )
  AND deleted = 0;

-- 2025年6月12日 宋勇 新增督查应用查看限制配置
INSERT INTO t_sys_config (id, code, "value", remark, create_by, create_name, create_time, update_by, update_name, update_time, deleted) VALUES (1933093305089507330, 'appName:binding:loginName', '5peg5omw552j5p+l77yaaG5zd2JndGFkbWlu', '督查应用查看限制：绑定应用和用户名（正确格式为：应用名称:用户名1,用户名2）', 2, '系统管理员', '2025-06-12 17:25:34', null, null, null, 0);

-- 2025年6月13日 宋勇 新增wrdcadmin用户和角色
INSERT INTO cscp_user_role(id, user_id, role_id, create_by, create_name, create_time, update_by, update_name, update_time, department_id, company_id, tenant_id, deleted, HMAC_ROLE_ID_NAME) VALUES (1430078438603669502, 5, 1424729081995304962, 1, 'admin', '2025-06-13 09:16:20', null, null, null, null, null, null, 0, null);
INSERT INTO cscp_user (id, login_name, real_name_start, real_name_end, password, real_name, email, mobile_end, mobile_middle, mobile_start, mobile, last_login, tenant_id, create_time, create_by, create_name, update_time, update_by, update_name, order_by, status, display, office_phone, app_version, statistics, main_tariff, surcharge, deleted, sex, is_write_sign, app_version_name, withdraw_condition, audit_sms, is_address_unit, backup_mobile, str_id, str_classified, str_id_card_no, str_unit_trust_no, user_origin, signature_image_url, sjs_str_id, security_classification_code, security_classification_code_name, examine_status, stamp_url, start_no, WESTONE_USER_ID, HMAC_MOBILE, ID_CARD_NO, person_label, is_display, ID_CARD_NO_BACKUP) VALUES (5, 'wrdcadmin', '57O7,57uf,566h,55CG,5ZGY', '57O7,57uf,566h,55CG,5ZGY', '$2a$08$AFi94kuHdaAJ6eArHbzTg.PjJ3iSm3I.AtNNuAx8zj1BZ7L6fUHgO', '57O757uf566h55CG5ZGY', '<EMAIL>', 'MQ==,OA==,OQ==,MA==,MA==,MA==,MA==,MA==,MA==,MA==,MA==', 'MQ==,OA==,OQ==,MA==,MA==,MA==,MA==,MA==,MA==,MA==,MA==', 'MQ==,OA==,OQ==,MA==,MA==,MA==,MA==,MA==,MA==,MA==,MA==', 'MTg5MDAwMDAwMDA=', '2021-08-06 16:39:03', null, '2021-08-05 18:13:05', null, null, '2025-05-25 13:26:02', null, null, 2, 1, null, null, null, null, null, null, 0, null, 0, null, 0, 0, 1, null, '2', null, null, null, null, null, null, '1', 'zy', 1, null, null, '9b4329b6cf9b45b38b04b04696521fab', null, null, '', 1, null);
-- 2025年6月13日 宋勇 修改部门管理员角色名称和角色编码
UPDATE cscp_roles t SET t.name = '部门管理员的角色', t.role_code = 'department_role' WHERE t.id = 1453911658682986499;commit;

-- 2025年6月30日 宋勇 app权限模块表新增字段
ALTER TABLE "t_app_permission_module" ADD "type" VARCHAR(50);
COMMENT ON COLUMN "t_app_permission_module"."type" IS 'app模块所属分类';
ALTER TABLE "t_app_permission_module" ADD "icon" CLOB;
COMMENT ON COLUMN "t_app_permission_module"."icon" IS '图标';
ALTER TABLE "t_app_permission_module" ADD "system_type" INT;
COMMENT ON COLUMN "t_app_permission_module"."system_type" IS '1.本系统2.内部其它系统3.第三方系统';
ALTER TABLE "t_app_permission_module" ADD "direct_url" VARCHAR(200);
COMMENT ON COLUMN "t_app_permission_module"."direct_url" IS '跳转URL';
ALTER TABLE "t_app_permission_module" ADD "add_mark" INT;
COMMENT ON COLUMN "t_app_permission_module"."add_mark" IS '是否添加角标';
ALTER TABLE "t_app_permission_module" ADD "remark" VARCHAR(200);
COMMENT ON COLUMN "t_app_permission_module"."remark" IS '备注';

ALTER TABLE "t_app_permission_module" ADD "common_flag" INT DEFAULT 0;
COMMENT ON COLUMN "t_app_permission_module"."common_flag" IS '是否通用模块：0-否，1-是';
ALTER TABLE "t_app_permission_module" ADD "department_id" BIGINT;
ALTER TABLE "t_app_permission_module" ADD "company_id" BIGINT;

-- 2025年7月3日 宋勇 通讯录表搜索字段添加全文索引
CREATE CONTEXT INDEX idx_ctx_default_phone
ON t_address_book(DEFAULT_PHONE_ENCRYPT_1)
LEXER ENGLISH_LEXER SYNC TRANSACTION;

CREATE CONTEXT INDEX idx_ctx_telephone
ON t_address_book(TELEPHONE_ENCRYPT_1)
LEXER ENGLISH_LEXER SYNC TRANSACTION;

CREATE CONTEXT INDEX idx_ctx_last_name
ON t_address_book(LAST_NAME_ENCRYPT)
LEXER ENGLISH_LEXER SYNC TRANSACTION;

-- 2025年7月4日 宋勇 新增应用系统管理表版主管理字段
ALTER TABLE "t_sync_app_system_manage" ADD  "moderator_flag" INT DEFAULT 0;
COMMENT ON COLUMN "t_sync_app_system_manage"."moderator_flag" IS '是否移交版主管理, 0-否, 1-是';
-- 2025年7月4日 宋勇 新增应用系统管理表版主管理表
CREATE TABLE "T_SYNC_APP_MODERATOR_MANAGE" (
    "ID" BIGINT NOT NULL,
    "APP_ID" BIGINT NOT NULL,
    "MODERATOR_ID" BIGINT NOT NULL,
    "MODERATOR_NAME" VARCHAR(64),
    "CREATE_TIME" TIMESTAMP,
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(64),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(64),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT DEFAULT 0,
    CLUSTER PRIMARY KEY("ID")
);
COMMENT ON TABLE "T_SYNC_APP_MODERATOR_MANAGE" IS '版主应用管理表';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."ID" IS '主键ID';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."APP_ID" IS '应用ID (关联 t_sync_app_system_manage.id)';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."MODERATOR_ID" IS '版主用户ID (关联 cscp_user.id)';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."MODERATOR_NAME" IS '版主用户姓名';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."CREATE_BY" IS '创建人ID';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."UPDATE_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."UPDATE_BY" IS '最后更新人ID';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."UPDATE_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_SYNC_APP_MODERATOR_MANAGE"."DELETED" IS '是否删除：0-未删除，1-已删除';

-- 2025年7月7日 宋勇 更新应用系统管理表是否移交版主管理字段默认值
update t_sync_app_system_manage set moderator_flag = 0 where 1=1;commit;

-- 2025年7月15日 宋勇 维护版主应用表中督查应用默认值
update t_sync_app_system_manage set moderator_flag = 1 where deleted=0 and app_name like '%督查%';commit;
INSERT INTO T_SYNC_APP_MODERATOR_MANAGE (ID, APP_ID, MODERATOR_ID, MODERATOR_NAME, CREATE_TIME, CREATE_BY, CREATE_NAME, UPDATE_TIME, UPDATE_BY, UPDATE_NAME, DEPARTMENT_ID, COMPANY_ID, TENANT_ID, DELETED)
VALUES (1942443941691994114,  (SELECT id FROM t_sync_app_system_manage WHERE deleted = 0 AND app_name LIKE '%督查%' limit 1), 5, null, '2025-07-15 15:13:00', 2, '系统管理员', null, null, null, null, null, null, 0);

-- 2025年7月15日 宋勇 app角色表新增区划机构ID字段
ALTER TABLE "t_app_role" ADD  "region_id" BIGINT;
COMMENT ON COLUMN "t_app_role"."region_id" IS '区划机构ID';

-- 2025年7月18日 宋勇 角色表新增版主管理字段
ALTER TABLE cscp_roles ADD moderator_status INT DEFAULT 0 ;
COMMENT ON COLUMN cscp_roles.moderator_status IS '是否移交版主管理, 0-否, 1-是';