package com.ctsi.personMatters.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.tree.TreeSelectService;
import com.ctsi.personMatters.entity.BizDossierOrganization;
import com.ctsi.personMatters.entity.dto.BizDossierOrganizationDTO;
import com.ctsi.personMatters.entity.dto.BizPersonDossierDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 内设机构管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface IBizDossierOrganizationService  extends TreeSelectService<BizDossierOrganizationDTO, BizDossierOrganization> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizDossierOrganizationDTO> queryListPage(BizDossierOrganizationDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizDossierOrganizationDTO> queryList(BizDossierOrganizationDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizDossierOrganizationDTO findOne(Long id);

    /**
     * 查询机构下的所有干部信息
     *
     * @param query
     * @return
     */
    PageResult<BizPersonDossierDTO> findDossierListPage(BizPersonDossierDTO query, BasePageForm basePageForm);


    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizDossierOrganizationDTO create(BizDossierOrganizationDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizDossierOrganizationDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizDossierOrganizationId
     * @param code
     * @return
     */
    boolean existByBizDossierOrganizationId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizDossierOrganizationDTO> dataList);


    List<BizDossierOrganizationDTO> queryOrgList(BizDossierOrganizationDTO organizationDTO);
}
