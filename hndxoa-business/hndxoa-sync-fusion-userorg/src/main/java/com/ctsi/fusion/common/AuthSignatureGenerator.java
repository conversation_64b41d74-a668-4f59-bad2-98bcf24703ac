package com.ctsi.fusion.common;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AuthSignatureGenerator {

    public static void main(String[] args) {
        // Step 1: 构造一个 Map
        Map<String, String> map = new HashMap<>();
        map.put("timestamp", String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        map.put("path", "/tyjg/api/foreign/cscpUser/pull");
        map.put("version", "1.0.0");

        // Step 2: 进行 Key 的自然排序，然后 Key，Value值拼接最后再拼接分配给你的 SK
        List<String> storedKeys = Arrays.stream(map.keySet().toArray(new String[]{}))
                .sorted(Comparator.naturalOrder())
                .collect(java.util.stream.Collectors.toList());

        final String appKey = "E760078364274110A86C466A08EF5DE7";
        final String sk = "1C20E98E32274CFB8D6DB1E14BA1C4B7";

        final String sign = storedKeys.stream()
                .map(key -> String.join("", key, map.get(key)))
                .collect(java.util.stream.Collectors.joining()).trim()
                .concat(sk);

        // Step 3: 进行 MD5 加密后转成大写
        String md5Sign = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sign.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02X", b));
            }
            md5Sign = sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        System.out.println("timestamp: " + map.get("timestamp"));
        System.out.println("appKey: " + appKey);
        System.out.println("sign: " + md5Sign);
        System.out.println("version: " + map.get("version"));
    }





}



