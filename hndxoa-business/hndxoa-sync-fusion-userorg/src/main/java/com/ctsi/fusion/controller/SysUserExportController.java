package com.ctsi.fusion.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.UserDcjImportDTO;
import com.ctsi.ssdc.admin.domain.dto.UserImportDTO;
import com.ctsi.ssdc.admin.domain.dto.UserKuaDanWeiImportDTO;
import com.ctsi.ssdc.admin.service.UserImportAndExportService;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@RestController
@ResponseResultVo
@RequestMapping("/api/userImportAndExport")
@Api(value = "用户导入导出接口", tags = "用户导入导出接口")
@Slf4j
public class SysUserExportController {

    @Autowired
    private UserImportAndExportService userImportAndExportService;

    @Autowired
    private ITSyncAppSystemManageService itSyncAppSystemManageService;


    @PostMapping("/uploadUser/{id}")
    @ApiOperation(value = "用户导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "用户导入")
    public ResultVO uploadUser(@PathVariable Long id, MultipartFile file) throws IOException {
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }

        // 使用同步读取避免分批处理导致的并发问题
        List<UserImportDTO> dataList = EasyExcel.read(file.getInputStream())
                .head(UserImportDTO.class).sheet().doReadSync();

        Map<String, List<Long>> map = userImportAndExportService.saveUsers(id, dataList);
        for (Map.Entry<String, List<Long>> entry : map.entrySet()) {
            this.syncExportUserBatch(entry.getKey(), entry.getValue());
        }

        return ResultVO.success(true);
    }

    @PostMapping("/qh/uploadUser/{id}")
    @ApiOperation(value = "区划用户导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "用户导入")
    public ResultVO qhUploadUser(@PathVariable Long id, MultipartFile file) throws IOException {
        // 使用同步读取避免分批处理导致的并发问题
        List<UserKuaDanWeiImportDTO> dataList = EasyExcel.read(file.getInputStream())
                .head(UserKuaDanWeiImportDTO.class).sheet().doReadSync();

        Map<String, List<Long>> map = userImportAndExportService.saveQhUsers(dataList);
        for (Map.Entry<String, List<Long>> entry : map.entrySet()) {
            this.syncExportUserBatch(entry.getKey(), entry.getValue());
        }
        return ResultVO.success(true);
    }

    @PostMapping("/uploadUserNew")
    @ApiOperation(value = "用户导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "用户导入")
    public ResultVO uploadUserNew(MultipartFile file) throws IOException {
        // 使用同步读取避免分批处理导致的并发问题
        List<UserKuaDanWeiImportDTO> dataList = EasyExcel.read(file.getInputStream())
                .head(UserKuaDanWeiImportDTO.class).sheet().doReadSync();

        Map<String, List<Long>> map = userImportAndExportService.saveUsersNew(dataList);
        for (Map.Entry<String, List<Long>> entry : map.entrySet()) {
            this.syncExportUserBatch(entry.getKey(), entry.getValue());
        }
        return ResultVO.success(true);
    }

    @PostMapping("/dcj/uploadUser/{id}")
    @ApiOperation(value = "多层级用户导入接口", notes = "传入参数")
    public ResultVO dcjUploadUser(@PathVariable Long id, MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), UserDcjImportDTO.class, new PageReadListener<UserDcjImportDTO>(dataList -> {
                Map<String, List<Long>> map = userImportAndExportService.saveDcjUsers(id, dataList);
                for (Map.Entry<String, List<Long>> entry : map.entrySet()) {
                    this.syncExportUserBatch(entry.getKey(), entry.getValue());
                }
            })).sheet().doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return ResultVO.success(true);
    }

    @GetMapping("/exportUser/{id}")
    @ApiOperation(value = "用户导出接口", notes = "传入参数")
    public ResultVO exportUser(@PathVariable Long id, HttpServletResponse response) throws IOException {
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }
        CscpUserDTO dto = new CscpUserDTO();
        dto.setId(id);
        userImportAndExportService.exportUserToExcel(dto, response);
        return ResultVO.success();
    }

    /**
     * 导出全部用户
     * */
    @GetMapping("/exportUser")
    @ApiOperation(value = "用户导出接口", notes = "传入参数")
    public ResultVO exportUserAll(CscpUserDTO cscpUserDTO, HttpServletResponse response) throws IOException {
        userImportAndExportService.exportUserToExcel(cscpUserDTO, response);
        return ResultVO.success();
    }


    private void syncExportUserBatch(String opt, List<Long> ids) {
        if (null != ids && !ids.isEmpty()) {
            SecurityContext securityContext = SecurityContextHolder.getContext();
            long startTime = System.currentTimeMillis();
            log.info("开始 >>>>>> 导入用户-推送: {}", ids);
            CompletableFuture.runAsync((() -> {
            SecurityContextHolder.setContext(securityContext);
                for (Long l : ids) {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setFlag(opt);
                    syncOrgUserDTO.setUserId(l);
                    syncOrgUserDTO.setIsAutoPushFlag(true);
                    itSyncAppSystemManageService.syncUserBusiness(syncOrgUserDTO);
                }
            }));
            log.info("结束 >>>>>> 导入用户-推送:{}", ids);
            log.info("耗时: {} ms", System.currentTimeMillis() - startTime);
        }
    }
}
