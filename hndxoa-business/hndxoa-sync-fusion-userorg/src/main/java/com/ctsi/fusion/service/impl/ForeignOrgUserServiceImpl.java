package com.ctsi.fusion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.fusion.service.IForeignOrgUserService;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.mq.producer.HistoryCountProducer;
import com.ctsi.hndxoa.pull.entity.dto.CountMessage;
import com.ctsi.hndxoa.pull.utils.PassiveSyncCountTool;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ForeignOrgUserServiceImpl implements IForeignOrgUserService {

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private ITSyncUserHistroyRecordService tSyncUserHistroyRecordService;

    @Autowired
    private ITSyncOrgHistroyRecordService tSyncOrgHistroyRecordService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private PassiveSyncCountTool passiveSyncCountTool;

    @Autowired
    private HistoryCountProducer historyCountProducer;

    private final Logger log = LoggerFactory.getLogger(ForeignOrgUserServiceImpl.class);

    /**
     * 被动模式-拉取机构信息
     * 核心逻辑
     * 1、请求参数中推送记录，只要有一个条未超限，便允许拉取；
     * 2、校验拉取的机构，是否存在越权数据，存在则拒绝拉取
     * @param appCode 应用code
     * @param list 推送记录id + 机构id
     * @return 机构信息
     */
    @Override
    public List<CscpOrgDTO> selectPullCscpOrg(String appCode, List<TYJGSyncUserOrgIdDTO> list) {
        // 1. 验证请求并获取应用配置
        TSyncAppSystemManage appSystemManage = verifyRequest(appCode, list);
        String inOwnedOrgId = appSystemManage.getInOwnedOrgId();

        // 2. 验证计数并处理超限
        Set<Long> overLimitOrgIds = verifyCountHistory(OrgUserConstants.PushType.PUSH_UNIT, list);
        // 提取所有历史ID并去重
        Set<Long> allHistoryIds = list.stream().map(TYJGSyncUserOrgIdDTO::getSyncHistoryId).collect(Collectors.toSet());
        // 若所有历史ID都超限，直接拒绝
        if (overLimitOrgIds.size() >= allHistoryIds.size()) {
            throw new BusinessException(ResultCode.PULL_FREQUENCY_LIMIT);
        }

        // 3. 查询机构原始数据
        List<Long> validOrgIds = list.stream().map(TYJGSyncUserOrgIdDTO::getOrgId).collect(Collectors.toList());
        if (validOrgIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 一次性查询机构数据
        List<CscpOrg> orgList = cscpOrgService.selectOrgHasDeleted(validOrgIds);

        // 4. 权限过滤（计算差集并返回越权机构信息）
        if (StringUtils.isNotEmpty(inOwnedOrgId)) {
            String rootCode = cscpOrgService.getById(inOwnedOrgId).getOrgCode();
            // 过滤出有权限的机构
            List<CscpOrg> filtered = orgList.stream()
                    .filter(o -> o.getOrgCodePath().contains(rootCode))
                    .collect(Collectors.toList());

            // 计算差集：原列表 - 过滤后列表 = 越权机构
            Set<Long> filteredIds = filtered.stream().map(CscpOrg::getId).collect(Collectors.toSet());
            List<CscpOrg> unauthorizedList = orgList.stream()
                    .filter(o -> !filteredIds.contains(o.getId())) // 不在过滤列表中的即为越权
                    .collect(Collectors.toList());

            // 若存在越权机构，抛出异常并携带越权信息
            if (!unauthorizedList.isEmpty()) {
                // 转换越权机构为JSON（仅包含关键信息id和name）
                String unauthorizedJson = JSONObject.toJSONString(
                        unauthorizedList.stream()
                                .map(org -> new HashMap<String, Object>() {{
                                    put("orgId", org.getId());
                                    put("orgName", org.getOrgName());
                                    put("orgCode", org.getOrgCode());
                                }})
                                .collect(Collectors.toList())
                );
                // 异常信息拼接越权数据
                throw new BusinessException(
                        ResultCode.PULL_PERMISSION_DENIED,
                        "存在越权访问的机构，详情：" + unauthorizedJson
                );
            }
        }

        // 5. 转换DTO并处理超限数据
        List<CscpOrgDTO> resultList = ListCopyUtil.copy(orgList, CscpOrgDTO.class);
        if (resultList.isEmpty()) {
            return Collections.emptyList();
        }

        // 6. 批量加载关联节点
        Map<Long, List<CscpOrgDTO>> parentMap = batchLoadParentNodes(resultList);
        Map<Long, List<CscpOrgDTO>> childMap = batchLoadChildrenNodes(resultList);

        // 7. 组装结果
        resultList.forEach(dto -> {
            dto.setParentNodes(parentMap.getOrDefault(dto.getId(), Collections.emptyList()));
            dto.setChildNodes(childMap.getOrDefault(dto.getId(), Collections.emptyList()));
        });

        return resultList;
    }

    /**
     * 批量加载指定机构DTO的父节点
     * @param orgDTOs 机构DTO列表（仅正常数据）
     * @return key:机构ID，value:父节点DTO列表
     */
    private Map<Long, List<CscpOrgDTO>> batchLoadParentNodes(List<CscpOrgDTO> orgDTOs) {
        // 收集所有父节点的OrgCode（从机构路径中提取）
        Set<String> allParentOrgCodes = new HashSet<>();
        for (CscpOrgDTO dto : orgDTOs) {
            // 如果deleted为1，则跳过当前dto
            if (dto.getDeleted() == 1) {
                continue;
            }
            String[] codePath = dto.getOrgCodePath().split("\\|"); // 路径格式：root|parent|current
            if (codePath.length > 1) { // 至少有一个父节点
                // 排除自身，取所有父节点编码
                for (int i = 0; i < codePath.length - 1; i++) {
                    allParentOrgCodes.add(codePath[i]);
                }
            }
        }

        if (allParentOrgCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询父节点原始数据
        LambdaQueryWrapper<CscpOrg> parentOrgLqw = Wrappers.lambdaQuery();
        parentOrgLqw.in(CscpOrg::getOrgCode, allParentOrgCodes);
        List<CscpOrg> allParentOrgs = cscpOrgService.selectListNoAdd(parentOrgLqw);
        Map<String, CscpOrg> parentOrgMap = allParentOrgs.stream()
                .collect(Collectors.toMap(
                        CscpOrg::getOrgCode,
                        org -> org,
                        (existing, replacement) -> existing // 遇到重复保留第一个
                ));

        // 构建父节点DTO映射（key:当前机构ID）
        Map<Long, List<CscpOrgDTO>> resultMap = new HashMap<>(orgDTOs.size());
        for (CscpOrgDTO dto : orgDTOs) {
            if (dto.getDeleted() == 1) {
                continue;
            }
            String[] codePath = dto.getOrgCodePath().split("\\|");
            if (codePath.length <= 1) {
                resultMap.put(dto.getId(), Collections.emptyList());
                continue;
            }
            // 提取父节点并转换为DTO
            List<CscpOrg> parentOrgs = new ArrayList<>();
            for (int i = 0; i < codePath.length - 1; i++) {
                CscpOrg parent = parentOrgMap.get(codePath[i]);
                if (parent != null) {
                    parentOrgs.add(parent);
                }
            }
            resultMap.put(dto.getId(), ListCopyUtil.copy(parentOrgs, CscpOrgDTO.class));
        }

        return resultMap;
    }

    /**
     * 批量加载指定机构DTO的子节点
     * @param orgDTOs 机构DTO列表（仅正常数据）
     * @return key:机构ID，value:子节点DTO列表
     */
    private Map<Long, List<CscpOrgDTO>> batchLoadChildrenNodes(List<CscpOrgDTO> orgDTOs) {
        // 收集所有需要查询子节点的机构ID
        Set<Long> parentIds = orgDTOs.stream()
                .map(CscpOrgDTO::getId)
                .collect(Collectors.toSet());

        if (parentIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询子节点原始数据
        LambdaQueryWrapper<CscpOrg> childOrgLqw = Wrappers.lambdaQuery();
        childOrgLqw.in(CscpOrg::getParentId, parentIds);
        childOrgLqw.orderByAsc(CscpOrg::getOrderBy); // 按排序字段升序
        List<CscpOrg> allChildOrgs = cscpOrgService.selectListNoAdd(childOrgLqw);

        // 按父节点ID分组
        Map<Long, List<CscpOrg>> childrenByParentId = allChildOrgs.stream()
                .collect(Collectors.groupingBy(CscpOrg::getParentId));

        // 转换为DTO并构建结果映射
        Map<Long, List<CscpOrgDTO>> resultMap = new HashMap<>(parentIds.size());
        for (Long parentId : parentIds) {
            List<CscpOrg> children = childrenByParentId.getOrDefault(parentId, Collections.emptyList());
            resultMap.put(parentId, ListCopyUtil.copy(children, CscpOrgDTO.class));
        }

        return resultMap;
    }

    @Override
    public List<CscpUserDTO> selectPullCscpUser(List<TYJGSyncUserOrgIdDTO> list) {

        // 验证计数并处理超限
        Set<Long> overLimitIds = verifyCountHistory(OrgUserConstants.PushType.PUSH_USER, list);
        // 提取所有历史ID并去重
        Set<Long> allHistoryIds = list.stream().map(TYJGSyncUserOrgIdDTO::getSyncHistoryId).collect(Collectors.toSet());
        // 若所有历史ID都超限，直接拒绝
        if (overLimitIds.size() >= allHistoryIds.size()) {
            throw new BusinessException(ResultCode.PULL_FREQUENCY_LIMIT);
        }

        return cscpUserService.selectPullCscpUser(list);
    }

    @Override
    public void callbackPullHistoryRecord(String appCode, List<TYJGSyncUserOrgIdDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // 机构
        callbackPullOrgHistoryRecord(appCode,list);
        // 用户
        callbackPullUserHistoryRecord(appCode,list);
    }

    private void callbackPullUserHistoryRecord(String appCode,List<TYJGSyncUserOrgIdDTO> list) {
        Map<Long, List<TYJGSyncUserOrgIdDTO>> map = list.stream()
                .filter(tyjgSyncUserOrgIdDTO -> tyjgSyncUserOrgIdDTO.getUserId() != null)
                .collect(Collectors.groupingBy(TYJGSyncUserOrgIdDTO::getSyncHistoryId));

        if (map.keySet().isEmpty()) {
            return; // 没有需要更新的记录
        }

        // 批量查询历史记录
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TSyncUserHistroyRecord::getId, map.keySet());
        List<TSyncUserHistroyRecord> records = tSyncUserHistroyRecordService.selectListNoAdd(queryWrapper);

        if (CollectionUtil.isEmpty(records)) {
            return; // 没有找到对应的历史记录
        }

        // 更新记录状态（示例：将状态设置为已同步）
        for (TSyncUserHistroyRecord record : records) {
            if (map.get(record.getId()).get(0).isSyncSucceedFlag()) {
                record.setSyncStatus("200");
                record.setSyncSuccess("true");
                record.setSyncMessage("同步数据完成");
            } else {
                // 失败的看需要
                // record.setSyncSuccess("false");
                // record.setSyncMessage("同步数据失败");
            }
//            record.setSyncCount((record.getSyncCount() != null ? record.getSyncCount() : 0) + 1);
        }

        // 批量更新（使用MyBatis-Plus的批量更新方法）
        tSyncUserHistroyRecordService.updateBatchById(records);

        // 更新机构中的push_app_code
//        List<Long> orgIds = list.stream().filter(TYJGSyncUserOrgIdDTO::isSyncSucceedFlag).map(TYJGSyncUserOrgIdDTO::getOrgId).collect(Collectors.toList());
//        cscpOrgService.updateLocateAppCodeBatch(orgIds, appCode);
    }

    private void callbackPullOrgHistoryRecord(String appCode,List<TYJGSyncUserOrgIdDTO> list) {

        Map<Long, List<TYJGSyncUserOrgIdDTO>> map = list.stream()
                .filter(tyjgSyncUserOrgIdDTO -> tyjgSyncUserOrgIdDTO.getOrgId() != null)
                .collect(Collectors.groupingBy(TYJGSyncUserOrgIdDTO::getSyncHistoryId));

        if (map.keySet().isEmpty()) {
            return; // 没有需要更新的记录
        }

        // 批量查询历史记录
        LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TSyncOrgHistroyRecord::getId, map.keySet());
        List<TSyncOrgHistroyRecord> records = tSyncOrgHistroyRecordService.selectListNoAdd(queryWrapper);

        if (CollectionUtil.isEmpty(records)) {
            return; // 没有找到对应的历史记录
        }

        // 更新记录状态（示例：将状态设置为已同步）
        for (TSyncOrgHistroyRecord record : records) {
            if (map.get(record.getId()).get(0).isSyncSucceedFlag()) {
                record.setSyncStatus("200");
                record.setSyncSuccess("true");
                record.setSyncMessage("同步数据完成");
            } else {
                // 失败的看需要
                // record.setSyncSuccess("false");
                // record.setSyncMessage("同步数据失败");
            }
//            record.setSyncCount((record.getSyncCount() != null ? record.getSyncCount() : 0) + 1);
        }

        // 批量更新（使用MyBatis-Plus的批量更新方法）
        tSyncOrgHistroyRecordService.updateBatchById(records);

        List<Long> orgIds = list.stream().map(TYJGSyncUserOrgIdDTO::getOrgId).collect(Collectors.toList());
        cscpOrgService.updateLocateAppCodeBatch(orgIds, appCode);

    }

    /**
     * 校验入参
     * @param appCode
     * @param list
     */
    private TSyncAppSystemManage verifyRequest(String appCode, List<TYJGSyncUserOrgIdDTO> list) {
        if (null == appCode) {
            throw new BusinessException(ResultCode.PULL_APP_CODE_INVALID);
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new BusinessException(ResultCode.PULL_PARAM_NULL);
        }

        for (TYJGSyncUserOrgIdDTO dto : list) {
            // 如果配置redis; 这里要查推送记录剩余可查询次数
            if (null == dto.getSyncHistoryId()) {
                throw new BusinessException(ResultCode.PULL_HISTORY_ID_INVALID);
            }
        }

        LambdaQueryWrapper<TSyncAppSystemManage> manageLqw = Wrappers.lambdaQuery();
        manageLqw.eq(TSyncAppSystemManage::getAppCode, appCode);
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageService.selectOneNoAdd(manageLqw);
        if (appSystemManage == null) {
            throw new BusinessException(ResultCode.PULL_APP_CODE_NOT_EXIST);
        }
        return appSystemManage;
    }

    /**
     * 校验请求是否超限,MQ发布更新操作
     * @param type 0:机构 1:用户
     * @param list 推送记录id + 机构/用户id
     * @return overLimitIds 超限id
     */
    private Set<Long> verifyCountHistory(Integer type, List<TYJGSyncUserOrgIdDTO> list) {
        // 加载同步阈值配置（简化变量名，直接解析）
        int countMax = Integer.parseInt(sysConfigService.getSysConfigValueByCode("history.count.max"));
        // 构建历史ID与业务ID映射（流式操作合并，减少中间变量）
        List<Long> historyIds = list.stream().map(TYJGSyncUserOrgIdDTO::getSyncHistoryId).collect(Collectors.toList());

        // 4. Redis计数+超限判断（合并循环逻辑，使用同步集合确保线程安全）
        Set<Long> overLimitIds = Collections.synchronizedSet(new HashSet<>());

        historyIds.forEach(id -> {
            Long syncCount = null;
            // 先尝试从Redis获取当前值（不自增，仅查询）
            if (type == OrgUserConstants.PushType.PUSH_UNIT) {
                Integer redisCount = passiveSyncCountTool.getOrgCount(id);
                syncCount = redisCount != null ? redisCount.longValue() : null;
            } else if (type == OrgUserConstants.PushType.PUSH_USER) {
                Integer redisCount = passiveSyncCountTool.getUserCount(id);
                syncCount = redisCount != null ? redisCount.longValue() : null;
            }

            // Redis中无值，从数据库查询并处理
            if (syncCount == null) {
                Integer dbCount = null;
                // 数据库查询
                if (type == OrgUserConstants.PushType.PUSH_UNIT) {
                    TSyncOrgHistroyRecord record = tSyncOrgHistroyRecordService.getById(id);
                    dbCount = record != null ? record.getSyncCount() : null;
                } else if (type == OrgUserConstants.PushType.PUSH_USER) {
                    TSyncUserHistroyRecord record = tSyncUserHistroyRecordService.getById(id);
                    dbCount = record != null ? record.getSyncCount() : null;
                }

                // 数据库默认值处理（未查询到默认为0）
                syncCount = dbCount != null ? dbCount.longValue() : 0L;

                // 若数据库值小于阈值，+1后存入Redis
                if (syncCount < countMax) {
                    long newCount = syncCount + 1;
                    if (type == OrgUserConstants.PushType.PUSH_UNIT) {
                        passiveSyncCountTool.setOrgCount(id, newCount);
                    } else {
                        passiveSyncCountTool.setUserCount(id, newCount);
                    }
                    syncCount = newCount; // 更新为+1后的值
                } else {
                    // 数据库值已超限，同步到Redis（保持原值）
                    if (type == OrgUserConstants.PushType.PUSH_UNIT) {
                        passiveSyncCountTool.setOrgCount(id, syncCount);
                    } else {
                        passiveSyncCountTool.setUserCount(id, syncCount);
                    }
                }
            } else {
                // Redis中有值，直接自增（确保原子性）
                if (type == OrgUserConstants.PushType.PUSH_UNIT) {
                    syncCount = passiveSyncCountTool.incrementOrgCount(id);
                } else {
                    syncCount = passiveSyncCountTool.incrementUserCount(id);
                }
            }

            // 最终超限判断
            if (syncCount > countMax) {
                overLimitIds.add(id); // 记录超限ID，不发送MQ
            } else {
                // 未超限，发送MQ
                CountMessage message = new CountMessage(type, id, syncCount);
                historyCountProducer.sendHistoryCountMessage(JSONObject.toJSONString(message));
            }
        });
        return overLimitIds;
    }
}
