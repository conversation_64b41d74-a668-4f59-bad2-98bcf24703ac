package com.ctsi.approvalpro.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum ProcessStatusEnums {
    FWLX("1","发文立项"),
    WJQC("2","文件起草"),
    QZSH("3","前置审核"),
    SYPZ("4","审议批准"),
    SHSF("5","审核审发"),
    YZFB("6","印制发布"),
    SBBA("7","上报备案"),
    DCLS("8","督查落实"),
    GDCD("9","归档存档"),
    ;

    @EnumValue
    private String code;

    private String name;

    ProcessStatusEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
