package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.vo.dto.TLawApprovalPreAuditDTO;
import com.ctsi.approvalpro.vo.TLawApprovalPreAudit;
import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 法规室立项前置审核 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface ITLawApprovalPreAuditService extends SysBaseServiceI<TLawApprovalPreAudit> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TLawApprovalPreAuditDTO> queryListPage(TLawApprovalPreAuditDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TLawApprovalPreAuditDTO> queryList(TLawApprovalPreAuditDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TLawApprovalPreAuditDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TLawApprovalPreAuditDTO create(TLawApprovalPreAuditDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TLawApprovalPreAuditDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTLawApprovalPreAuditId
     * @param code
     * @return
     */
    boolean existByTLawApprovalPreAuditId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TLawApprovalPreAuditDTO> dataList);


    TLawApprovalPreAuditDTO findOneByApprovalId(Long approvalId);

    void createAudit(TLawApprovalDraft vo) ;

    Boolean skipByCreate(TLawApprovalPreAuditDTO tLawApprovalPreAudit);

    PageResult<TLawApprovalPreAuditDTO> queryListPageCondition(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO, BasePageForm basePageForm);

    Boolean skipByDw(TLawApprovalPreAuditDTO tLawApprovalPreAudit);

    Boolean skipByZf(TLawApprovalPreAuditDTO tLawApprovalPreAudit);

    Boolean exaimByDw(TLawApprovalPreAuditDTO tLawApprovalPreAudit);

    Boolean exaimByZf(TLawApprovalPreAuditDTO tLawApprovalPreAudit);

    Boolean exaimByCreate(TLawApprovalPreAuditDTO dto);

    int removeByApprovalId(Long approvalId);
}
