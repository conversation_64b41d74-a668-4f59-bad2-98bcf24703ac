package com.ctsi.approvalpro.controller;


import com.ctsi.approvalpro.service.ITLawApprovalDraftService;
import com.ctsi.approvalpro.vo.dto.TLawApprovalDraftDTO;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO;
import com.ctsi.hndx.annotations.LimitSubmit;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 法规室立项文件起草 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawApprovalDraft")
@Api(value = "立项文件起草接口", tags = "文件起草接口")
public class TLawApprovalDraftController extends BaseController {

    @Autowired
    private ITLawApprovalDraftService tLawApprovalDraftService;


    @PostMapping("/createOrUpdate")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "插入或者新增集中立项")
    //@LimitSubmit(key = "draftCreateOrUpdate:%s:#id",limit = 5,needAllWait = true)
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO<TLawApprovalDraftDTO> createOrUpdate(@RequestBody TLawApprovalDraftDTO tLawApprovalDraftDTO) {

        TLawApprovalDraftDTO result= tLawApprovalDraftService.createOrUpdate(tLawApprovalDraftDTO);
        if(result==null){
            return ResultVO.error("文件起草需要上传正文。");
        }
        return ResultVO.success(result);
    }
    /**
     * 个人进来查询自己的立项
     * @param tLawApprovalDraftDTO
     * @param basePageForm
     * @return
     */

    @GetMapping("/queryPage")
    @ApiOperation(value = "查询多条数据--申报单位", notes = "传入参数")
    public ResultVO<PageResult<TLawApprovalDraftDTO>> queryPage(TLawApprovalDraftDTO tLawApprovalDraftDTO, BasePageForm basePageForm) {
        //怎加创建人的条件
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        tLawApprovalDraftDTO.setCreateBy(currentCscpUserDetail.getId());
        tLawApprovalDraftDTO.setDepartmentId(currentCscpUserDetail.getDepartmentId());
        PageResult<TLawApprovalDraftDTO> list = tLawApprovalDraftService.selectListPage(tLawApprovalDraftDTO,basePageForm);
        return ResultVO.success(list);
    }



    /**
     * 根据立项id查询起草数据
     */
    @GetMapping("/get/{approvalId}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getOneByApprovalId(@PathVariable Long approvalId) {
        TLawApprovalDraftDTO tLawApprovalDraftDTO = tLawApprovalDraftService.findOneByApprovalId(approvalId);
        return ResultVO.success(tLawApprovalDraftDTO);
    }




    @PostMapping("/uploadFileName")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "插入或者新增集中立项")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO uploadFileName(@RequestBody TLawApprovalproDTO dto) {
        if(dto.getId()==null){
            return ResultVO.error("请携带立项id进行修改");
        }
        tLawApprovalDraftService.uploadFileName(dto);
        return ResultVO.success();
    }



}
