package com.ctsi.approvalpro.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 上报备案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLawFileReportHighDTO对象", description="上报备案表")
public class TLawFileReportHighDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 文件二维码Id
     */
    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    private Long approvalId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String title;

    /**
     * 密级期限code
     */
    @ApiModelProperty(value = "密级期限code")
    private String durationClassification;

    /**
     * 密级期限名称
     */
    @ApiModelProperty(value = "密级期限名称")
    private String durationClassificationName;

    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    private String durationYear;

    /**
     * 起草单位id
     */
    @ApiModelProperty(value = "起草单位id")
    private Long draftCompanyId;

    /**
     * 起草部门id
     */
    @ApiModelProperty(value = "起草部门id")
    private Long draftDepartmentId;

    /**
     * 打印日期
     */
    @ApiModelProperty(value = "打印日期")
    private Date printDate;

    /**
     * 截止日期
     */
    @ApiModelProperty(value = "截止日期")
    private Date endDate;

    /**
     * 报备日期
     */
    @ApiModelProperty(value = "报备日期")
    private Date reportDate;

    /**
     * 流程formDateId
     */
    @ApiModelProperty(value = "流程formDateId")
    private Long formDataId;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 处室负责人
     */
    @ApiModelProperty(value = "处室负责人")
    private String departmentHeadName;

    /**
     * 报备日期
     */
    @ApiModelProperty(value = "报备开始时间")
    private Date reportDateStart;

    /**
     * 报备日期
     */
    @ApiModelProperty(value = "报备结束时间")
    private Date reportDateEnd;

    @ApiModelProperty(value = "起草单位名称")
    private String draftCompanyName;

    @ApiModelProperty(value = "备案报告文件列表")
    private List<CscpEnclosureFile> babgFileList;

    @ApiModelProperty(value = "备案说明文件列表")
    private List<CscpEnclosureFile> basmFileList;
}
