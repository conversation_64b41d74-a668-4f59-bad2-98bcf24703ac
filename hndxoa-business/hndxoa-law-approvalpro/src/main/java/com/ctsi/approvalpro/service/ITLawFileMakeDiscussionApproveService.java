package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.entity.dto.TLawFileMakeDiscussionApproveDTO;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.approvalpro.entity.vo.LawFileMakeDiscussApproveDetail;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 文件制定审议批准表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
public interface ITLawFileMakeDiscussionApproveService extends SysBaseServiceI<TLawFileMakeDiscussionApprove> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TLawFileMakeDiscussionApproveDTO> queryListPage(TLawFileMakeDiscussionApproveDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TLawFileMakeDiscussionApproveDTO> queryList(TLawFileMakeDiscussionApproveDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TLawFileMakeDiscussionApproveDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TLawFileMakeDiscussionApproveDTO create(TLawFileMakeDiscussionApproveDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TLawFileMakeDiscussionApproveDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTLawFileMakeDiscussionApproveId
     * @param code
     * @return
     */
    boolean existByTLawFileMakeDiscussionApproveId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TLawFileMakeDiscussionApproveDTO> dataList);


    LawFileMakeDiscussApproveDetail getDetail(Long id);

    void upload(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO);

    void skip(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO);

    int removeByApprovalId(Long approvalId);

    LawFileMakeDiscussApproveDetail getByApprovalId(Long approvalId);
}
