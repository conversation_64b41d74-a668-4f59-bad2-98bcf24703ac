package com.ctsi.approvalpro.vo.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "集体立项Excel数据实体", description = "集体立项Excel数据实体")
public class TLawApprovalproExcel {

    @ApiModelProperty(value = "起草单位名称")
    @ExcelProperty(value = "申报单位", index = 3)
    private String  companyName;

    @ApiModelProperty(value = "申报的文件名")
    @ExcelProperty(value = "文件名称", index = 0)
    private String  approvalFileName;


    @ApiModelProperty(value = "申报的发文形式value")
    @ExcelProperty(value = "发文规格", index = 1)
    private String  allFileModalityValue;

    @ApiModelProperty(value = "审核状态 0 未申报 1 已申报 2已驳回 3 审核通过")
    @ExcelProperty(value = "状态", index = 5)
    private String statusValue;

    @ExcelProperty(value = "联系人", index = 4)
    private String createName;


    @ApiModelProperty(value = "申报时间")
    @ExcelProperty(value = "申报时间", index = 2,format="yyyy-MM-dd")
    private String declareTimeDay;

}
