package com.ctsi.approvalpro.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Getter
@Setter
@ApiModel(value = "TLawFileSupervisionDTO对象", description = "")
public class TLawFileSupervisionDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文件二维码Id")
    private Long codeId;

    @ApiModelProperty("立项id")
    private Long approvalId;

    @ApiModelProperty("文件名称")
    private String title;

    @ApiModelProperty("督查状态 0 未督查 1督查中 2 督查成功 3 督查失败")
    private Integer supervisionStatus;

    @ApiModelProperty("归档状态 0 未归档 1已归档")
    private Integer toFileStatus;

    @ApiModelProperty("督查id")
    private Long supervisionId;

    @ApiModelProperty("密级期限code")
    private String durationClassification;

    @ApiModelProperty("密级期限名称")
    private String durationClassificationName;

    @ApiModelProperty("印发时间")
    private String printDate;
}
