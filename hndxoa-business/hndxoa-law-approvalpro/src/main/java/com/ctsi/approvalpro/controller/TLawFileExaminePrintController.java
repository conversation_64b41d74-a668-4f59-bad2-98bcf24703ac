package com.ctsi.approvalpro.controller;
import com.ctsi.approvalpro.entity.dto.TLawFileExamineApprovalEnclosureDto;
import com.ctsi.approvalpro.entity.vo.LawFileMakeDiscussApproveDetail;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintDetail;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintPageVo;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.dto.TLawFileExaminePrintDTO;
import com.ctsi.approvalpro.service.ITLawFileExaminePrintService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawFileExaminePrint")
@Api(value = "审核审发及印制发布表", tags = "审核审发及印制发布表接口")
public class TLawFileExaminePrintController extends BaseController {

    private static final String ENTITY_NAME = "tLawFileExaminePrint";

    @Autowired
    private ITLawFileExaminePrintService tLawFileExaminePrintService;



    /**
     *  新增审核审发及印制发布表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawFileExaminePrint.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增审核审发及印制发布表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileExaminePrint.add')")
    public ResultVO createBatch(@RequestBody List<TLawFileExaminePrintDTO> tLawFileExaminePrintList) {
       Boolean  result = tLawFileExaminePrintService.insertBatch(tLawFileExaminePrintList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tLawFileExaminePrint.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增审核审发及印制发布表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileExaminePrint.add')")
    public ResultVO<TLawFileExaminePrintDTO> create(@RequestBody TLawFileExaminePrintDTO tLawFileExaminePrintDTO)  {
        TLawFileExaminePrintDTO result = tLawFileExaminePrintService.create(tLawFileExaminePrintDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tLawFileExaminePrint.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新审核审发及印制发布表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileExaminePrint.update')")
    public ResultVO update(@RequestBody TLawFileExaminePrintDTO tLawFileExaminePrintDTO) {
	    Assert.notNull(tLawFileExaminePrintDTO.getId(), "general.IdNotNull");
        int count = tLawFileExaminePrintService.update(tLawFileExaminePrintDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除审核审发及印制发布表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tLawFileExaminePrint.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileExaminePrint.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLawFileExaminePrintService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TLawFileExaminePrintDTO tLawFileExaminePrintDTO = tLawFileExaminePrintService.findOne(id);
        return ResultVO.success(tLawFileExaminePrintDTO);
    }


    @GetMapping("/getByApprovalId/{approvalId}")
    @ApiOperation(value = "approvalId查询数据", notes = "传入参数")
    public ResultVO getByApprovalId(@PathVariable Long approvalId) {
        TLawFileExaminePrintDetail printDetail = tLawFileExaminePrintService.getByApprovalId(approvalId);
        return ResultVO.success(printDetail);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLawFileExaminePrintPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TLawFileExaminePrintPageVo>> queryTLawFileExaminePrintPage(TLawFileExaminePrintDTO tLawFileExaminePrintDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLawFileExaminePrintService.queryListPage(tLawFileExaminePrintDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLawFileExaminePrint")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TLawFileExaminePrintDTO>> queryTLawFileExaminePrint(TLawFileExaminePrintDTO tLawFileExaminePrintDTO) {
       List<TLawFileExaminePrintDTO> list = tLawFileExaminePrintService.queryList(tLawFileExaminePrintDTO);
       return ResultVO.success(new ResResult<TLawFileExaminePrintDTO>(list));
   }


    @PostMapping("/tranferApprovalEnclosure")
    @ApiOperation(value = "转呈批件", notes = "传入参数")
    public ResultVO<TLawFileExamineApprovalEnclosureDto> tranferApprovalEnclosure(@RequestBody TLawFileExaminePrintDTO tLawFileExaminePrintDTO) {
        TLawFileExamineApprovalEnclosureDto examineApprovalEnclosureDto = tLawFileExaminePrintService.tranferApprovalEnclosure(tLawFileExaminePrintDTO);
        return ResultVO.success(examineApprovalEnclosureDto);
    }

    @PostMapping("/reportToHigh")
    @ApiOperation(value = "上报备案", notes = "传入参数")
    public ResultVO<Boolean> reportToHigh(@RequestBody TLawFileExaminePrintDTO tLawFileExaminePrintDTO) {
        Boolean result = tLawFileExaminePrintService.reportToHigh(tLawFileExaminePrintDTO);
        return ResultVO.success(result);
    }


}
