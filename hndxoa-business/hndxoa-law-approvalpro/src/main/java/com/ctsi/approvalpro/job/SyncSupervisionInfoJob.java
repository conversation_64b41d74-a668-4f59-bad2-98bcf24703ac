package com.ctsi.approvalpro.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.approvalpro.entity.TLawFileSupervision;
import com.ctsi.approvalpro.mapper.TLawFileSupervisionMapper;
import com.ctsi.hndx.tsysconfig.entity.SysConfig;
import com.ctsi.hndx.tsysconfig.mapper.SysConfigMapper;
import com.ctsi.hndx.wps.WpsUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.StatusLine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service("syncSupervisionInfoJobTwo")
@Slf4j
public class SyncSupervisionInfoJob {

    private static final String LOCK_KEY = "oaSyncSupervisionInfoJobLock";
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String supervisionInfoCode = "job.supervision.info.code";

    @Autowired
    private TLawFileSupervisionMapper lawFileSupervisionMapper;

    @Autowired
    private SysConfigMapper tSysConfigMapper;


    @Scheduled(cron = "0 0 0/1 * * ?")
    //@Scheduled(cron = "0/1 * * * * ?")
    public void syncSupervisionInfo() {

//        String openValue = getUrl(supervisionJobOpen);
//        if(!"open".equals(openValue)){
//            log.info("==============同步督办任务开关未打开==============");
//            return;
//        }

        LocalDateTime dateTime = LocalDateTime.now();
        log.info("==============同步督办任务信息开始============== [{}]", dateTime);
        if (redisTemplate.opsForValue().setIfAbsent(LOCK_KEY, "1", 1, TimeUnit.HOURS)) {
            try {

                //查询所有未转督查数据
                LambdaQueryWrapper<TLawFileSupervision> fileSupervisionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                fileSupervisionLambdaQueryWrapper.eq(TLawFileSupervision::getSupervisionStatus,0);
                List<TLawFileSupervision> tLawFileSupervisions = lawFileSupervisionMapper.selectListNoAdd(fileSupervisionLambdaQueryWrapper);



                if(CollectionUtils.isNotEmpty(tLawFileSupervisions)){
                    List<Long> approveIds = tLawFileSupervisions.stream().map(e -> e.getApprovalId()).collect(Collectors.toList());

                    String url = getUrl(supervisionInfoCode);
                    Map<String, Object> bodyParams = new HashMap<>();
                    List<Long> ids = Lists.newArrayList();
                    ids.addAll(approveIds);
                    bodyParams.put("businessIds",ids);
                    bodyParams.put("systemResource", "1");
                    bodyParams.put("sysFun","1");
                    Pair<StatusLine, String> resp = null;
                    try {
                        resp = WpsUtil.sendPostRequest(url,null,bodyParams);
                        log.info("同步督查机构信息 响应状态码： {}",resp.getKey().getStatusCode());

                        ObjectMapper objectMapper = new ObjectMapper();
                        JSONObject jsonObject = JSONObject.parseObject(resp.getValue());
                        Object resultData = jsonObject.get("resultData");

                        // 使用 TypeReference 明确指定 Map 的值类型
                        Map<String, SupervisionInfoDto> map = objectMapper.readValue(resultData.toString(),
                                new TypeReference<Map<String, SupervisionInfoDto>>(){});

                        Map<Long,TLawFileSupervision> fileSupervisionMap = tLawFileSupervisions
                                .stream().collect(Collectors.toMap(TLawFileSupervision::getApprovalId,TLawFileSupervision ->TLawFileSupervision,(v1, v2) -> v1));

                        map.keySet().forEach(key ->{
                            TLawFileSupervision tLawFileSupervision = fileSupervisionMap.get(Long.valueOf(key));
                            if(ObjectUtil.isNotNull(tLawFileSupervision)){
                                SupervisionInfoDto supervisionInfoDto = map.get(key);
                                tLawFileSupervision.setSupervisionId(supervisionInfoDto.getSupervisionId());
                                tLawFileSupervision.setSupervisionStatus(1);
                                lawFileSupervisionMapper.updateById(tLawFileSupervision);
                            }
                        });
                    }catch (Exception ex) {
                        log.error("同步督查信息 异常：{}", ex.getMessage());
                    }
                }
            }catch (Exception e) {
                log.error("同步督办任务异常 {}",e.getMessage());
            } finally {
                redisTemplate.delete(LOCK_KEY);
            }
        }else{
            log.info("同步督办任务锁正被占用");
            return;
        }
    }

    private String getUrl(String code) {
        LambdaQueryWrapper<SysConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysConfig::getCode,code);
        List<SysConfig> listData = tSysConfigMapper.selectListNoAdd(queryWrapper);
        String url =   listData.get(0).getValue();
        return url;
    }

}
