package com.ctsi.approvalpro.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupervisionInfoDto {

    @ApiModelProperty("督办单位id")
    private Long companyId;

    @ApiModelProperty("督办单位名称")
    private String companyName;

    @ApiModelProperty("责任单位列表")
    private String orgNames;

    @ApiModelProperty("文件流")
    private List<String> fileIds;

    @ApiModelProperty("完成时间")
    private String completeTime;

    @ApiModelProperty("督办id")
    private Long supervisionId;
}
