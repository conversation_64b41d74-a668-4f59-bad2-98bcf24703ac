package com.ctsi.approvalpro.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TLawApprovalproMapper extends MybatisBaseMapper<TLawApprovalpro> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<TLawApprovalproDTO> selectListPage(IPage page, @Param("dto") TLawApprovalproDTO tLawApprovalproDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<TLawApprovalproDTO> selectListByCondition( @Param("dto")TLawApprovalproDTO tLawApprovalproDTO);
    @InterceptorIgnore(tenantLine = "true")
    IPage<TLawApprovalproDTO> selectListAllPage(IPage page, @Param("dto")TLawApprovalproDTO tLawApprovalproDTO);

    void uploadFileName(@Param("id")Long id, @Param("approvalFileName")String approvalFileName);
}
