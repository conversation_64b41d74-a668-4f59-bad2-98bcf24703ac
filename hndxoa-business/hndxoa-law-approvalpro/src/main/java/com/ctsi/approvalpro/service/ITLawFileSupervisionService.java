package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.entity.TLawFileSupervision;
import com.ctsi.approvalpro.entity.dto.TLawFileReportHighDTO;
import com.ctsi.approvalpro.entity.dto.TLawFileSupervisionDTO;
import com.ctsi.approvalpro.entity.vo.TLawFileReportHighPageVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
public interface ITLawFileSupervisionService extends SysBaseServiceI<TLawFileSupervision> {

    PageResult<TLawFileSupervisionDTO> queryListPage(TLawFileSupervisionDTO TLawFileSupervisionDTO, BasePageForm basePageForm);

    Boolean placeToFile(TLawFileSupervisionDTO tLawFileReportHighDTO);
}
