package com.ctsi.approvalpro.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 文件制定审议批准表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
public interface TLawFileMakeDiscussionApproveMapper extends MybatisBaseMapper<TLawFileMakeDiscussionApprove> {

    @InterceptorIgnore(tenantLine = "true")
    int removeByApprovalId(@Param("approvalId") Long approvalId);
}
