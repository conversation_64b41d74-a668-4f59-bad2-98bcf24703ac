package com.ctsi.approvalpro.vo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_law_file_code")
@ApiModel(value="TLawFileCode对象", description="集中立项生成的二维码地址对象")
public class TLawFileCode extends BaseEntity {
        private static final long serialVersionUID = 1L;
        @ApiModelProperty(value = "生成的二维码文件名称")
        private String fileName;
        @ApiModelProperty(value = "文件地址")
        private String fileUrl;
        @ApiModelProperty(value = "关联的集中立项表id")
        private Long formDataId;
        @ApiModelProperty(value = "文件扩展名")
        private String extName;
        @ApiModelProperty(value = "文件大小")
        private Long fileSize;
        @ApiModelProperty(value = "当前环节 后续用")
        private String currentLink;
        @ApiModelProperty(value = "当前环节状态")
        private String currentLinkStatus;



}
