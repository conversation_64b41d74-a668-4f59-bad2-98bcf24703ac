package com.ctsi.approvalpro.vo.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 法规室立项前置审核
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLawApprovalPreAuditDTO对象", description="法规室立项前置审核")
public class TLawApprovalPreAuditDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 起草部门名称
     */
    @ApiModelProperty(value = "起草部门名称")
    private String departmentName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 申报的文件名
     */
    @ApiModelProperty(value = "申报的文件名")
    private String approvalFileName;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String durationClassification;

    /**
     * 密级值
     */
    @ApiModelProperty(value = "密级值")
    private String durationClassificationName;

    /**
     * 申报的发文规格code
     */
    @ApiModelProperty(value = "申报的发文规格code")
    private String fileModalityType;

    /**
     * 申报的发规格取值
     */
    @ApiModelProperty(value = "申报的发规格取值")
    private String fileModalityValue;

    /**
     * 申报的发文规格2code
     */
    @ApiModelProperty(value = "申报的发文规格2code")
    private String fileModalityTypeSec;

    /**
     * 申报的发规格取值
     */
    @ApiModelProperty(value = "申报的发规格取值")
    private String fileModalityTypeSecValue;

    /**
     * 二维码id
     */
    @ApiModelProperty(value = "二维码id")
    private Long codeId;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    private Long approvalId;

    /**
     * 合法性检查状态 1是0否
     */
    @ApiModelProperty(value = "合法性检查状态 1是0否")
    private String hfxscStatus;

    /**
     * 改革评审状态1是0否
     */
    @ApiModelProperty(value = "改革评审状态1是0否")
    private String ggpsStatus;

    /**
     * 前置审核状态 0待审核 1审核中 2驳回 3审核通过
     */
    @ApiModelProperty(value = "前置审核状态 0待审核 1审核中 2驳回 3审核通过 4已转审议批准")
    private Integer status;

    /**
     * 是否为改革类文件0否 1 是
     */
    @ApiModelProperty(value = "是否为改革类文件0否 1 是")
    private String isGgwj;

    /**
     * 合法性和公平竞争审查文件ids
     */
    @ApiModelProperty(value = "合法性和公平竞争审查文件ids")
    private String hfxscFileId;

    /**
     * 改革文件ids
     */
    @ApiModelProperty(value = "改革文件ids")
    private String ggpsFileId;

    /**
     * 采纳情况说明
     */
    @ApiModelProperty(value = "采纳情况说明")
    private String exaimDescription;

    /**
     * 起草单位上传资料ids
     */
    @ApiModelProperty(value = "起草单位上传资料ids")
    private String zlFileId;

    /**
     * 党委办审核状态 0 未审核 1驳回 2 审核通过
     */
    @ApiModelProperty(value = "党委办审核状态 0 未审核 1驳回 2 跳过 3审核通过")
    private String dwbExaimStatus;


    @ApiModelProperty(value = "政府办审批单位名称--用于流程显示")
    private String zfbExaimCompanyName;

    @ApiModelProperty(value = "党委办审批单位名称--用于流程显示")
    private String dwbExaimCompanyName;

    /**
     * 党委办审核以及说明
     */
    @ApiModelProperty(value = "党委办审核以及说明")
    private String dwbExaimContent;

    /**
     * 党委办审核资料ids
     */
    @ApiModelProperty(value = "党委办审核资料ids")
    private String dwbZlFileId;

    /**
     * 政府办审核状态 0 未审核 1驳回 2 审核通过
     */
    @ApiModelProperty(value = "政府办审核状态 0 未审核 1驳回 2 跳过 3审核通过")
    private String zfbExaimStatus;

    /**
     * 政府办审核意见说明
     */
    @ApiModelProperty(value = "政府办审核意见说明")
    private String zfbExaimContent;

    /**
     * 政府办审核人员id
     */
    @ApiModelProperty(value = "政府办审核人员id")
    private Long zfbUserId;

    /**
     * 政府办审核资料文件ids
     */
    @ApiModelProperty(value = "政府办审核资料文件ids")
    private String zfbZlFileId;

    /**
     * 党委办审核时间
     */
    @ApiModelProperty(value = "党委办审核时间")
    private Date dwbExaimTime;

    /**
     * 政府办审核时间
     */
    @ApiModelProperty(value = "政府办审核时间")
    private Date zfbExaimTime;

    /**
     * 党委办审核人id
     */
    @ApiModelProperty(value = "党委办审核人id")
    private Long dwbUserId;

    /**
     * 政府办是否能看到数据0 否1 是
     */
    @ApiModelProperty(value = "政府办是否能看到数据0 否1 是")
    private String zfbView;

    /**
     * 党委办是否能看数据 0 1
     */
    @ApiModelProperty(value = "党委办是否能看数据 0 1")
    private String dwbView;

    @ApiModelProperty(value = "是否修改文件名 0 未修改  1 已经修改")
    private String isUpdateFileName;

    @ApiModelProperty(value = "所属序列值")
    private String fileSeqTypeValue;
    @ApiModelProperty(value = "所属序列code")
    private String fileSeqType;

    private LocalDateTime updateTime;

    private Long updateBy;
    private String updateName;

    private Long createBy;

    private String createName;

    private LocalDateTime createTime;

    private Long departmentId;

    private Long companyId;

    private Long tenantId;

    private Integer deleted;

    private LocalDateTime draftTime;

    @ApiModelProperty(value = "起草单位审核时间")
    private LocalDateTime createExaimTime;

}
