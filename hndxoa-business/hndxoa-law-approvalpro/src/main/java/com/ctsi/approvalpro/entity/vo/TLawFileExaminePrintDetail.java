package com.ctsi.approvalpro.entity.vo;

import com.ctsi.activiti.core.vo.TaskVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TLawFileExaminePrintDetail {

    @ApiModelProperty(value = "立项Id")
    private Long approvalId;

    /**
     * 文件二维码Id
     */
    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String title;

    @ApiModelProperty(value = "OA taskVO")
    private TaskVO taskVO;
}
