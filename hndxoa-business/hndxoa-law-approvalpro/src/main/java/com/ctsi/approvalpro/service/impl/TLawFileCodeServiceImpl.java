package com.ctsi.approvalpro.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.TLawFileReportHigh;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.mapper.TLawFileCodeMapper;
import com.ctsi.approvalpro.mapper.TLawFileExaminePrintMapper;
import com.ctsi.approvalpro.mapper.TLawFileReportHighMapper;
import com.ctsi.approvalpro.service.ITLawFileCodeService;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.approvalpro.vo.dto.TLawFileCodeDTO;
import com.ctsi.approvalpro.vo.dto.TLawFlowDTO;
import com.ctsi.business.domain.CscpAuditContent;
import com.ctsi.business.repository.CscpAuditContentRepository;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.utils.QRCodeUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class TLawFileCodeServiceImpl extends SysBaseServiceImpl<TLawFileCodeMapper, TLawFileCode> implements ITLawFileCodeService {

    @Autowired
    private TLawFileCodeMapper  tLawFileCodeMapper;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private TLawFileExaminePrintMapper tLawFileExaminePrintMapper;

    @Autowired
    private TLawFileReportHighMapper tLawFileReportHighMapper;

    @Autowired
    private CscpAuditContentRepository cscpAuditContentRepository;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;

    @Override
    public TLawFileCodeDTO selectByFormDataId(TLawFileCodeDTO tLawFileCodeDTO) {
        TLawFileCodeDTO  dto =tLawFileCodeMapper.selectByFormDataId(tLawFileCodeDTO.getFormDataId());
        return dto;
    }

    @Override
    public TLawFileCode createFileCode(Long id,String currentLink) {
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        TLawFileCode code = new TLawFileCode();
        String  content = id+"";

        byte[] bytes = QRCodeUtil.zxingCodeCreate(content,250);
        code.setFileSize(Long.valueOf(bytes.length));
        String filepath =fileStoreTemplateService.createFileUrl(FileBasePathName.LAWFILECODE,".jpg");
        fileStoreTemplateService.uploadFile(filepath,bytes);
        code.setFileName(id+".jpg");
        code.setFileUrl(filepath);
        code.setCurrentLink(currentLink);
        code.setCurrentLinkStatus("0");
        code.setFormDataId(id);
        code.setTenantId(currentCscpUserDetail.getTenantId());
        code.setCreateTime(LocalDateTime.now());
        code.setCreateBy(currentCscpUserDetail.getId());
        code.setCreateName(currentCscpUserDetail.getRealName());
        code.setDepartmentId(currentCscpUserDetail.getDepartmentId());
        code.setCompanyId(currentCscpUserDetail.getCompanyId());
        save(code);
        return code ;
    }

    @Override
    public TLawFileCode updateFileCode(Long codeId, String code) {
        TLawFileCode tLawFileCode=tLawFileCodeMapper.selectById(codeId);
        tLawFileCode.setCurrentLink(code);
        tLawFileCode.setCurrentLinkStatus("0");
        tLawFileCode.setUpdateTime(LocalDateTime.now());
        tLawFileCode.setUpdateBy(SecurityUtils.getCurrentUserId());
        tLawFileCode.setUpdateName(SecurityUtils.getCurrentRealName());
        updateById(tLawFileCode);
        return tLawFileCode;
    }

    @Override
    public TLawFlowDTO getFlow(Long approvalId) {
        TLawFlowDTO dto = tLawFileCodeMapper.getFlow(approvalId);
        //呈批单的数据再根据数据去匹配
        String currentLink = dto.getCurrentLink();
        if(StringUtils.isNotEmpty(currentLink)){
            Integer code = Integer.valueOf(currentLink);
            if(code >= Integer.valueOf(ProcessStatusEnums.YZFB.getCode())){
                //获取processInstanceId
                LambdaQueryWrapper<TLawFileExaminePrint> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TLawFileExaminePrint::getApprovalId, approvalId);
                TLawFileExaminePrint fileExaminePrint = tLawFileExaminePrintMapper.selectOneNoAdd(queryWrapper);
                if(ObjectUtil.isNotNull(fileExaminePrint)){
                    List<CscpAuditContent> list = getCscpAuditContents(fileExaminePrint.getProcessInstanceId());
                    dto.setShsfFlows(list);
                }
            }

            if(code >= Integer.valueOf(ProcessStatusEnums.SBBA.getCode())){
                //获取processInstanceId
                LambdaQueryWrapper<TLawFileReportHigh> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TLawFileReportHigh::getApprovalId, approvalId);
                TLawFileReportHigh TLawFileReportHigh = tLawFileReportHighMapper.selectOneNoAdd(queryWrapper);
                if(ObjectUtil.isNotNull(TLawFileReportHigh)){
                    List<CscpAuditContent> list = getCscpAuditContents(TLawFileReportHigh.getProcessInstanceId());
                    dto.setSbbaFlows(list);
                }
            }
            //由于不能实时监听OA审批是否结束，特殊处理.前端 0下表开始
            if(code == Integer.valueOf(ProcessStatusEnums.YZFB.getCode())){
                if(ObjectUtil.isNotNull(dto.getPrintProcessInstanceId())){
                    TaskVO processInfoByInstanceId = cscpProcBaseRepository.getProcessInfoByInstanceId(String.valueOf(dto.getPrintProcessInstanceId()));
                    if(processInfoByInstanceId!=null) {
                         if(processInfoByInstanceId.getBpmStatus() < 3){
                             dto.setCurrentLink(ProcessStatusEnums.SHSF.getCode());
                         }
                    }else{
                        dto.setCurrentLink(ProcessStatusEnums.SHSF.getCode());
                    }
                }else{
                    dto.setCurrentLink(ProcessStatusEnums.SHSF.getCode());
                }
            }

            //    SHSF("5","审核审发"),
            //    YZFB("6","印制发布"),  且未提交打印前，去掉这个时间【不想额外添加数据库字段】
            if(code == Integer.valueOf(ProcessStatusEnums.SHSF.getCode()) || (code == Integer.valueOf(ProcessStatusEnums.YZFB.getCode()) && StringUtils.isEmpty(dto.getPrintCompanyName())) ){
                dto.setPrintTime(null);
            }
        }

        return dto;
    }

    private List<CscpAuditContent> getCscpAuditContents(Long processInstanceId) {
        LambdaQueryWrapper<CscpAuditContent> contentQueryWrapper = new LambdaQueryWrapper<>();
        contentQueryWrapper.eq(CscpAuditContent::getProcInstId,processInstanceId);
        contentQueryWrapper.select(CscpAuditContent::getActName,CscpAuditContent::getAuditTime,CscpAuditContent::getAuditorName);
        contentQueryWrapper.orderByAsc(CscpAuditContent::getCreateTime);
        List<CscpAuditContent> list = cscpAuditContentRepository.selectListNoAdd(contentQueryWrapper);
        return list;
    }

    @Override
    public TLawFileCodeDTO getOneByApprovalId(Long approvalId) {

        return tLawFileCodeMapper.selectByFormDataId(approvalId);
    }
}
