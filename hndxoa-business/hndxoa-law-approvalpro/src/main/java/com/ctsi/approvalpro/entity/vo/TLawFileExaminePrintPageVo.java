package com.ctsi.approvalpro.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TLawFileExaminePrintPageVo {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "立项Id")
    private Long approvalId;

    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    @ApiModelProperty(value = "文件名称")
    private String title;

    @ApiModelProperty(value = "申报的发文形式code")
    private String fileModalityType;

    @ApiModelProperty(value = "申报的发文形式2code")
    private String fileModalityTypeSec;

    @ApiModelProperty(value = "申报的发文形式2value")
    private String fileModalityTypeSecValue;

    @ApiModelProperty(value = "申报的发文形式value")
    private String fileModalityValue;

    @ApiModelProperty(value = "流程当前环节")
    private String currentStage;

    @ApiModelProperty(value = "流程当前用户")
    private String currentUserName;

    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;
}
