package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO;
import com.ctsi.approvalpro.vo.dto.UTLawAppDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ITLawApprovalproService extends SysBaseServiceI<TLawApprovalpro> {

    TLawApprovalproDTO createOrUpdate(TLawApprovalproDTO tLawApprovalproDTO);

    PageResult<TLawApprovalproDTO> selectListPage(TLawApprovalproDTO tLawApprovalproDTO, BasePageForm basePageForm);

    int delete(Long id);

    int withdraw(Long id);

    boolean selectListByCondition(TLawApprovalproDTO tLawApprovalproDTO, HttpServletResponse response);

    PageResult<TLawApprovalproDTO> selectListAllPage(TLawApprovalproDTO tLawApprovalproDTO, BasePageForm basePageForm);

    void examineBatchById(UTLawAppDTO dto);

    TLawApprovalpro retrieveByApprovalId(TLawApprovalpro pro);

    void uploadFileName(Long id, String approvalFileName);
}
