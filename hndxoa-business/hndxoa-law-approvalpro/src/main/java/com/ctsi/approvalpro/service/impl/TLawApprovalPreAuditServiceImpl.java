package com.ctsi.approvalpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.service.ITLawFileCodeService;
import com.ctsi.approvalpro.service.ITLawFileMakeDiscussionApproveService;
import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.hndx.utils.*;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.approvalpro.vo.TLawApprovalPreAudit;
import com.ctsi.approvalpro.vo.dto.TLawApprovalPreAuditDTO;
import com.ctsi.approvalpro.mapper.TLawApprovalPreAuditMapper;
import com.ctsi.approvalpro.service.ITLawApprovalPreAuditService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.security.SecurityUtils;
import dm.jdbc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.beans.Beans;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.Exceptions;

/**
 * <p>
 * 法规室立项前置审核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Slf4j
@Service
public class TLawApprovalPreAuditServiceImpl extends SysBaseServiceImpl<TLawApprovalPreAuditMapper, TLawApprovalPreAudit> implements ITLawApprovalPreAuditService {

    @Autowired
    private TLawApprovalPreAuditMapper tLawApprovalPreAuditMapper;


    @Autowired
    private ITLawFileCodeService tLawFileCodeService;


    @Autowired
    private ITLawFileMakeDiscussionApproveService tLawFileMakeDiscussionApproveService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLawApprovalPreAuditDTO> queryListPage(TLawApprovalPreAuditDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TLawApprovalPreAudit> queryWrapper = new LambdaQueryWrapper();


        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getApprovalFileName()),TLawApprovalPreAudit::getApprovalFileName,entityDTO.getApprovalFileName());

        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getFileModalityType()),TLawApprovalPreAudit::getFileModalityType,entityDTO.getFileModalityType());

        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getFileModalityTypeSec()),TLawApprovalPreAudit::getFileModalityTypeSec,entityDTO.getFileModalityTypeSec());

        if(entityDTO.getStatus()!=null&&entityDTO.getStatus()==3){
            queryWrapper.ge(TLawApprovalPreAudit::getStatus,entityDTO.getStatus());
        }else{
            queryWrapper.eq(entityDTO.getStatus()!=null,TLawApprovalPreAudit::getStatus,entityDTO.getStatus());
        }

        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getHfxscStatus()),TLawApprovalPreAudit::getHfxscStatus,entityDTO.getHfxscStatus());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getGgpsStatus()),TLawApprovalPreAudit::getGgpsStatus,entityDTO.getGgpsStatus());

        queryWrapper.orderByAsc(TLawApprovalPreAudit::getStatus).orderByDesc(TLawApprovalPreAudit::getCreateTime);
        IPage<TLawApprovalPreAudit> pageData = tLawApprovalPreAuditMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TLawApprovalPreAuditDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLawApprovalPreAuditDTO.class));

        return new PageResult<TLawApprovalPreAuditDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLawApprovalPreAuditDTO> queryList(TLawApprovalPreAuditDTO entityDTO) {
        LambdaQueryWrapper<TLawApprovalPreAudit> queryWrapper = new LambdaQueryWrapper();
            List<TLawApprovalPreAudit> listData = tLawApprovalPreAuditMapper.selectList(queryWrapper);
            List<TLawApprovalPreAuditDTO> TLawApprovalPreAuditDTOList = ListCopyUtil.copy(listData, TLawApprovalPreAuditDTO.class);
        return TLawApprovalPreAuditDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLawApprovalPreAuditDTO findOne(Long id) {
        TLawApprovalPreAudit  tLawApprovalPreAudit =  tLawApprovalPreAuditMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tLawApprovalPreAudit,TLawApprovalPreAuditDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLawApprovalPreAuditDTO create(TLawApprovalPreAuditDTO entityDTO) {
       TLawApprovalPreAudit tLawApprovalPreAudit =  BeanConvertUtils.copyProperties(entityDTO,TLawApprovalPreAudit.class);
        save(tLawApprovalPreAudit);
        return  BeanConvertUtils.copyProperties(tLawApprovalPreAudit,TLawApprovalPreAuditDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLawApprovalPreAuditDTO entity) {
        TLawApprovalPreAudit tLawApprovalPreAudit = BeanConvertUtils.copyProperties(entity,TLawApprovalPreAudit.class);
        tLawApprovalPreAudit.setCreateExaimTime(LocalDateTime.now());
        return tLawApprovalPreAuditMapper.updateById(tLawApprovalPreAudit);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLawApprovalPreAuditMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLawApprovalPreAuditId
     * @return
     */
    @Override
    public boolean existByTLawApprovalPreAuditId(Long TLawApprovalPreAuditId) {
        if (TLawApprovalPreAuditId != null) {
            LambdaQueryWrapper<TLawApprovalPreAudit> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLawApprovalPreAudit::getId, TLawApprovalPreAuditId);
            List<TLawApprovalPreAudit> result = tLawApprovalPreAuditMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLawApprovalPreAuditDTO> dataList) {
        List<TLawApprovalPreAudit> result = ListCopyUtil.copy(dataList, TLawApprovalPreAudit.class);
        return saveBatch(result);
    }

    @Override
    public TLawApprovalPreAuditDTO findOneByApprovalId(Long approvalId) {
        LambdaQueryWrapper<TLawApprovalPreAudit> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLawApprovalPreAudit::getApprovalId,approvalId);
        TLawApprovalPreAudit audit=tLawApprovalPreAuditMapper.selectOneNoAdd(queryWrapper);
        if(audit!=null){
            return BeanUtil.copyProperties(audit, TLawApprovalPreAuditDTO.class);
        }
        return new TLawApprovalPreAuditDTO();
    }

    @Override
    public void createAudit(TLawApprovalDraft vo)  {

        TLawApprovalPreAuditDTO dto = findOneByApprovalId(vo.getApprovalId());
        if(dto!=null&&dto.getId()!=null){
            //如果已经存在就不在往里面插入数据
            return ;
        }

        TLawApprovalPreAudit audit = BeanConvertUtils.copyProperties(vo,TLawApprovalPreAudit.class);

        if(StringUtils.isEmpty(audit.getFileSeqType())){
            audit.setFileSeqType("dwxl");//如果没有就默认党委序列 新数据都将会有该选项
        }
        LambdaQueryWrapper<TLawApprovalPreAudit> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLawApprovalPreAudit::getApprovalId,vo.getApprovalId());
        int count=tLawApprovalPreAuditMapper.selectCountNoAdd(queryWrapper);
        if(count>0){
            log.info("该数据已经转前置审批，请不要重复提交。");
            return;
        }
        audit.setId(Long.valueOf(SnowflakeIdUtil.getSnowFlakeId()));
        audit.setStatus(0);//初始化状态
        audit.setCreateTime(LocalDateTime.now());
        audit.setUpdateTime(null);
        audit.setUpdateBy(null);
        audit.setUpdateName(null);
        //放到起草上传文件之后
       /* switch(audit.getFileSeqType()){
            case "dwxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    audit.setZfbView("1");
                    audit.setDwbView("0");
                }else{
                    audit.setDwbView("1");
                }
                break;
            case "zfxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    audit.setZfbView("0");
                    audit.setDwbView("1");
                }else{
                    audit.setZfbView("1");
                }
                break;
            default:
        }*/
        save(audit);
        //修改二维码的状态
        tLawFileCodeService.updateFileCode(audit.getCodeId(),ProcessStatusEnums.QZSH.getCode());
       /* TLawFileCode tLawFileCode =tLawFileCodeService.getById(audit.getCodeId());
        tLawFileCode.setUpdateTime(LocalDateTime.now());
        tLawFileCode.setUpdateBy(SecurityUtils.getCurrentUserId());
        tLawFileCode.setUpdateName(SecurityUtils.getCurrentRealName());
        tLawFileCode.setCurrentLink(ProcessStatusEnums.QZSH.getCode());
        tLawFileCode.setCurrentLinkStatus("0");
        tLawFileCodeService.updateById(tLawFileCode);*/
    }

    @Override
    public Boolean skipByCreate(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO) {
        //如果前置审核起草单位惦记跳过  直接将该状态改成审批中
        TLawApprovalPreAudit audit =tLawApprovalPreAuditMapper.selectById(tLawApprovalPreAuditDTO.getId());
        //TLawApprovalPreAudit audit = BeanConvertUtils.copyProperties(tLawApprovalPreAuditDTO,TLawApprovalPreAudit.class);
        audit.setStatus(1);
        audit.setUpdateBy(SecurityUtils.getCurrentUserId());
        audit.setUpdateTime(LocalDateTime.now());
        audit.setGgpsFileId(tLawApprovalPreAuditDTO.getGgpsFileId());
        audit.setGgpsStatus(StringUtils.isNotEmpty(tLawApprovalPreAuditDTO.getGgpsFileId())?"1":"0");
        audit.setIsGgwj(tLawApprovalPreAuditDTO.getIsGgwj());
        audit.setHfxscFileId(tLawApprovalPreAuditDTO.getHfxscFileId());
        audit.setCreateExaimTime(LocalDateTime.now());
        audit.setHfxscStatus(StringUtils.isNotEmpty(tLawApprovalPreAuditDTO.getHfxscFileId())?"1":"0");
        if(StringUtils.isEmpty(audit.getFileSeqType())){
            audit.setFileSeqType("dwxl");
        }
        switch(audit.getFileSeqType().trim()){
            case "dwxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    audit.setZfbView("0");
                    audit.setDwbView("1");
                }else{
                    audit.setDwbView("1");
                }
                break;
            case "zfxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    audit.setZfbView("1");
                    audit.setDwbView("0");
                }else{
                    audit.setZfbView("1");
                }
                break;
            default:
                audit.setDwbView("1");
                break;
        }
        tLawApprovalPreAuditMapper.updateById(audit);
        return true;
    }

    @Override
    public PageResult<TLawApprovalPreAuditDTO> queryListPageCondition(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO, BasePageForm basePageForm) {
        IPage<TLawApprovalPreAuditDTO> data =tLawApprovalPreAuditMapper.queryListPageCondition(tLawApprovalPreAuditDTO,PageHelperUtil.getMPlusPageByBasePage(basePageForm));

        return new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    @Override
    public Boolean skipByDw(TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        TLawApprovalPreAudit audit =tLawApprovalPreAuditMapper.selectById(tLawApprovalPreAudit.getId());
        switch(audit.getFileSeqType().trim()){
            case "dwxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    //联合发文  党委先审批  跳过就是自己跳过  政府序列能到党委的只有联合发文
                    audit.setDwbExaimStatus("2");
                    audit.setDwbExaimTime(new Date());
                    audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                    audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setZfbView("1");//处理完成将数据放到政府办
                    updateById(audit);
                } else{
                    //不是联合发文  点击跳过就是直接跳过
                    createDiscussionApproval(audit);
                    audit.setStatus(4);
                    audit.setDwbExaimStatus("2");
                    audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                    audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setDwbExaimTime(new Date());
                    updateById(audit);
                }
                break;
            case "zfxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    //如果前面政府办是跳过 那就是跳过  直接将数据发送到审议批准
                    if(StringUtil.equals("2",audit.getZfbExaimStatus())){
                        //两个都跳过  直接放入审议批准
                        createDiscussionApproval(audit);
                        audit.setStatus(4);
                        audit.setDwbExaimStatus("2");
                        audit.setDwbExaimTime(new Date());
                        audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                        audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                        updateById(audit);
                    }
                    else{
                        audit.setStatus(3);
                        audit.setDwbExaimStatus("2");
                        audit.setDwbExaimTime(new Date());
                        audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                        audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                        updateById(audit);
                    }
                }
                break;

        }

         return true;
    }

    private void createDiscussionApproval(TLawApprovalPreAudit audit) {

        TLawFileMakeDiscussionApprove vo = BeanConvertUtils.copyProperties(audit,TLawFileMakeDiscussionApprove.class);
        vo.setTitle(audit.getApprovalFileName());
        vo.setCreateTime(LocalDateTime.now());
        vo.setStatus(0);
        vo.setUpdateBy(null);
        vo.setUpdateName(null);
        vo.setUpdateTime(null);

       try{
           tLawFileMakeDiscussionApproveService.save(vo);
           tLawFileCodeService.updateFileCode(audit.getCodeId(),ProcessStatusEnums.SYPZ.getCode());
       }catch(Exception e){
           log.info("数据已经到审核，不要重复提交:"+e.getMessage());
        }


    }

    @Override
    public Boolean skipByZf(TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        TLawApprovalPreAudit audit =tLawApprovalPreAuditMapper.selectById(tLawApprovalPreAudit.getId());
        switch(audit.getFileSeqType().trim()){
            case "dwxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    //如果前面政府办是跳过 那就是跳过  直接将数据发送到审议批准
                    if(StringUtil.equals("2",audit.getZfbExaimStatus())){
                        //两个都跳过  直接放入审议批准
                        createDiscussionApproval(audit);
                        audit.setStatus(4);
                        audit.setZfbExaimStatus("2");
                        audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                        audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                        audit.setZfbExaimTime(new Date());
                        updateById(audit);
                    }
                    else{
                        audit.setStatus(3);
                        audit.setZfbExaimStatus("2");
                        audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                        audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                        audit.setZfbExaimTime(new Date());
                        updateById(audit);
                    }
                }
                break;
            case "zfxl":
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    //如果是联合发文  需要根据序列来选择发给谁
                    //联合发文  党委先审批  跳过就是自己跳过  政府序列能到党委的只有联合发文
                    audit.setZfbExaimStatus("2");
                    audit.setZfbExaimTime(new Date());
                    audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                    audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setZfbView("1");//处理完成将数据放到政府办
                    updateById(audit);
                }else{
                    //不是联合发文  点击跳过就是直接跳过
                    createDiscussionApproval(audit);
                    audit.setStatus(4);
                    audit.setZfbExaimStatus("2");
                    audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                    audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setZfbExaimTime(new Date());
                    updateById(audit);
                }
                break;
        }

        return true;
    }

    @Override
    public Boolean exaimByDw(TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        TLawApprovalPreAudit audit =tLawApprovalPreAuditMapper.selectById(tLawApprovalPreAudit.getId());
        switch(audit.getFileSeqType().trim()){
            case "dwxl":
                //dw序列走到这里下一步都将走到起草单位  审核通过
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    audit.setDwbExaimStatus(tLawApprovalPreAudit.getDwbExaimStatus());
                    audit.setDwbExaimTime(new Date());
                    audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                    audit.setDwbExaimContent(tLawApprovalPreAudit.getDwbExaimContent());
                    audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setDwbZlFileId(tLawApprovalPreAudit.getDwbZlFileId());
                    audit.setZfbView("1");//处理完成将数据放到政府办
                    updateById(audit);
                    break;
                }else{
                    audit.setStatus(3);
                    audit.setDwbExaimTime(new Date());
                    audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                    audit.setDwbExaimContent(tLawApprovalPreAudit.getDwbExaimContent());
                    audit.setDwbZlFileId(tLawApprovalPreAudit.getDwbZlFileId());
                    audit.setDwbExaimStatus(tLawApprovalPreAudit.getDwbExaimStatus());
                    audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    updateById(audit);
                    break;
                }

            case "zfxl":
                //政府序列走到这里的只有联合发文
                audit.setStatus(3);
                audit.setDwbExaimTime(new Date());
                audit.setDwbUserId(SecurityUtils.getCurrentUserId());
                audit.setDwbExaimContent(tLawApprovalPreAudit.getDwbExaimContent());
                audit.setDwbZlFileId(tLawApprovalPreAudit.getDwbZlFileId());
                audit.setDwbExaimStatus(tLawApprovalPreAudit.getDwbExaimStatus());
                audit.setDwbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                updateById(audit);
                break;
        }
        return true;
    }

    @Override
    public Boolean exaimByZf(TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        TLawApprovalPreAudit audit =tLawApprovalPreAuditMapper.selectById(tLawApprovalPreAudit.getId());
        switch(audit.getFileSeqType()){
            case "dwxl":
                audit.setStatus(3);
                audit.setZfbExaimStatus(tLawApprovalPreAudit.getZfbExaimStatus());
                audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                audit.setZfbExaimContent(tLawApprovalPreAudit.getZfbExaimContent());
                audit.setZfbZlFileId(tLawApprovalPreAudit.getZfbZlFileId());
                audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                audit.setZfbExaimTime(new Date());
                updateById(audit);
                break;
            case "zfxl":
                //政府序列的联合发文还是单独发文 走到政府审核都将通过  给起草单位
                if(StringUtil.equals("lhfw",audit.getFileModalityType())){
                    audit.setZfbExaimStatus(tLawApprovalPreAudit.getZfbExaimStatus());
                    audit.setZfbExaimTime(new Date());
                    audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                    audit.setZfbExaimContent(tLawApprovalPreAudit.getZfbExaimContent());
                    audit.setZfbZlFileId(tLawApprovalPreAudit.getZfbZlFileId());
                    audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setDwbView("1");//处理完成将数据放到政府办
                    updateById(audit);
                }else{
                    audit.setStatus(3);
                    audit.setZfbExaimStatus(tLawApprovalPreAudit.getZfbExaimStatus());
                    audit.setZfbUserId(SecurityUtils.getCurrentUserId());
                    audit.setZfbExaimContent(tLawApprovalPreAudit.getZfbExaimContent());
                    audit.setZfbZlFileId(tLawApprovalPreAudit.getZfbZlFileId());
                    audit.setZfbExaimCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
                    audit.setZfbExaimTime(new Date());
                    updateById(audit);
                }
                break;

        }

        return true;
    }

    @Override
    public Boolean exaimByCreate(TLawApprovalPreAuditDTO dto) {
        TLawApprovalPreAudit audit =tLawApprovalPreAuditMapper.selectById(dto.getId());
        audit.setStatus(4);
        audit.setZlFileId(dto.getZlFileId());
        audit.setExaimDescription(dto.getExaimDescription());
        createDiscussionApproval(audit);
        updateById(audit);
        return true;
    }

    @Override
    public int removeByApprovalId(Long approvalId) {
        return tLawApprovalPreAuditMapper.removeByApprovalId(approvalId);
    }


}
