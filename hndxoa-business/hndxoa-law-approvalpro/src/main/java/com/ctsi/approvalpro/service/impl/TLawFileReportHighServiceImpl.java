package com.ctsi.approvalpro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.approvalpro.entity.TLawFileSupervision;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintPageVo;
import com.ctsi.approvalpro.entity.vo.TLawFileReportHighPageVo;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.mapper.TLawFileCodeMapper;
import com.ctsi.approvalpro.mapper.TLawFileExaminePrintMapper;
import com.ctsi.approvalpro.mapper.TLawFileSupervisionMapper;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpDocumentFileMapper;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.approvalpro.entity.TLawFileReportHigh;
import com.ctsi.approvalpro.entity.dto.TLawFileReportHighDTO;
import com.ctsi.approvalpro.mapper.TLawFileReportHighMapper;
import com.ctsi.approvalpro.service.ITLawFileReportHighService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 上报备案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Slf4j
@Service
public class TLawFileReportHighServiceImpl extends SysBaseServiceImpl<TLawFileReportHighMapper, TLawFileReportHigh> implements ITLawFileReportHighService {

    @Autowired
    private TLawFileReportHighMapper tLawFileReportHighMapper;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;

    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private TLawFileExaminePrintMapper tLawFileExaminePrintMapper;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;


    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileMapper;

    @Autowired
    private CscpDocumentFileMapper cscpDocumentFileMapper;

    @Autowired
    private TLawFileSupervisionMapper tLawFileSupervisionMapper;

    @Autowired
    private TLawFileCodeMapper tLawFileCodeMapper;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLawFileReportHighPageVo> queryListPage(TLawFileReportHighDTO entityDTO, BasePageForm basePageForm) {
        List<TLawFileReportHighPageVo> reportHighPageVos = new ArrayList<>();
        List<TLawFileReportHighPageVo> sortedList = new ArrayList<>();

        //设置条件
        LambdaQueryWrapper<TLawFileReportHigh> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), TLawFileReportHigh::getTitle, entityDTO.getTitle());
        queryWrapper.between(entityDTO.getReportDateStart() != null && entityDTO.getReportDateEnd() != null, TLawFileReportHigh::getEndDate, entityDTO.getReportDateStart(), entityDTO.getReportDateEnd());
        queryWrapper.ne(TLawFileReportHigh::getSubmit,1);

        queryWrapper.orderByDesc(TLawFileReportHigh::getEndDate);

        IPage<TLawFileReportHigh> pageData = tLawFileReportHighMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TLawFileReportHighPageVo> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLawFileReportHighPageVo.class));


        TLawFileReportHighPageVo tmp = new TLawFileReportHighPageVo();
        List<TLawFileReportHighPageVo> records = data.getRecords();

        LocalDate now = LocalDate.now();
        for (int i = 0; i < records.size(); i++) {
            tmp = records.get(i);

            if(ObjectUtil.isNotNull(tmp.getReportDate())){
                tmp.setReportStatus(1);
            }else{
                LocalDate endDate = tmp.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                // 计算两个日期之间的天数差
                long daysBetween = ChronoUnit.DAYS.between(now, endDate);
                // 判断是否超过7天
                boolean isMoreThanSevenDays = Math.abs(daysBetween) > 7;
                if(isMoreThanSevenDays){
                    tmp.setReportStatus(0);
                }else{
                    tmp.setReportStatus(2);
                }
            }
            if(ObjectUtil.isNotNull(tmp.getProcessInstanceId())){
                LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper=new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,tmp.getProcessInstanceId());
                ProcessAssignee processAssignee = processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
                if(processAssignee!=null) {
                    tmp.setCurrentStage(processAssignee.getNodeName());
                    tmp.setCurrentUserName(processAssignee.getAssigneeName());
                }

                TaskVO processInfoByInstanceId = cscpProcBaseRepository.getProcessInfoByInstanceId(String.valueOf(tmp.getProcessInstanceId()));
                if(processInfoByInstanceId!=null) {
                    tmp.setBpmStatus(processInfoByInstanceId.getBpmStatus());
                }
            }
            reportHighPageVos.add(tmp);
        }

        if(CollectionUtils.isNotEmpty(reportHighPageVos)){
            Map<Date, List<TLawFileReportHighPageVo>> collect = reportHighPageVos.stream().
                    collect(Collectors.groupingBy(TLawFileReportHighPageVo::getEndDate));

            Comparator<TLawFileReportHighPageVo> comparing = Comparator.
                    comparing(TLawFileReportHighPageVo::getBpmStatus, Comparator.nullsFirst(Comparator.naturalOrder()));

            List<Date> dates = new ArrayList<>(collect.keySet());
            Collections.sort(dates, new Comparator<Date>() {
                @Override
                public int compare(Date o1, Date o2) {
                    return o2.compareTo(o1);
                }
            });

            dates.forEach(e ->{
                List<TLawFileReportHighPageVo> reportHighPageVos1 = collect.get(e);
                sortedList.addAll(reportHighPageVos1.stream().sorted(comparing).collect(Collectors.toList()));
            });
        }
        return new PageResult<TLawFileReportHighPageVo>(sortedList,
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLawFileReportHighDTO> queryList(TLawFileReportHighDTO entityDTO) {
        LambdaQueryWrapper<TLawFileReportHigh> queryWrapper = new LambdaQueryWrapper();
            List<TLawFileReportHigh> listData = tLawFileReportHighMapper.selectList(queryWrapper);
            List<TLawFileReportHighDTO> TLawFileReportHighDTOList = ListCopyUtil.copy(listData, TLawFileReportHighDTO.class);
        return TLawFileReportHighDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLawFileReportHighDTO findOne(Long id) {
        TLawFileReportHigh  tLawFileReportHigh =  tLawFileReportHighMapper.selectById(id);
        TLawFileReportHighDTO tLawFileReportHighDTO = BeanConvertUtils.
                copyProperties(tLawFileReportHigh, TLawFileReportHighDTO.class);

        LambdaQueryWrapper<TLawFileExaminePrint> queryWrapper =new LambdaQueryWrapper();
        queryWrapper.eq(TLawFileExaminePrint::getCodeId,tLawFileReportHighDTO.getCodeId());
        TLawFileExaminePrint tLawFileExaminePrint = tLawFileExaminePrintMapper.selectOneNoAdd(queryWrapper);
        if(tLawFileExaminePrint != null) {
            tLawFileReportHighDTO.setPrintDate(tLawFileExaminePrint.getPrintDate());
        }
        Long draftCompanyId = tLawFileReportHigh.getDraftCompanyId();
        CscpOrg cscpOrg = cscpOrgRepository.selectById(draftCompanyId);
        if(cscpOrg != null) {
            tLawFileReportHighDTO.setDraftCompanyName(cscpOrg.getOrgName());
        }
        return  tLawFileReportHighDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLawFileReportHighDTO create(TLawFileReportHighDTO entityDTO) {
       TLawFileReportHigh tLawFileReportHigh =  BeanConvertUtils.copyProperties(entityDTO,TLawFileReportHigh.class);
        save(tLawFileReportHigh);
        return  BeanConvertUtils.copyProperties(tLawFileReportHigh,TLawFileReportHighDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLawFileReportHighDTO entity) {
        TLawFileReportHigh tLawFileReportHigh = BeanConvertUtils.copyProperties(entity,TLawFileReportHigh.class);
        return tLawFileReportHighMapper.updateById(tLawFileReportHigh);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLawFileReportHighMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLawFileReportHighId
     * @return
     */
    @Override
    public boolean existByTLawFileReportHighId(Long TLawFileReportHighId) {
        if (TLawFileReportHighId != null) {
            LambdaQueryWrapper<TLawFileReportHigh> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLawFileReportHigh::getId, TLawFileReportHighId);
            List<TLawFileReportHigh> result = tLawFileReportHighMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLawFileReportHighDTO> dataList) {
        List<TLawFileReportHigh> result = ListCopyUtil.copy(dataList, TLawFileReportHigh.class);
        return saveBatch(result);
    }

    @Override
    public void registerReport(TLawFileReportHighDTO tLawFileReportHighDTO) {
        TLawFileReportHigh tLawFileReportHigh = tLawFileReportHighMapper.selectById(tLawFileReportHighDTO.getId());

        tLawFileReportHigh.setPrintDate(tLawFileReportHighDTO.getPrintDate());
        tLawFileReportHigh.setReportDate(tLawFileReportHighDTO.getReportDate());
        tLawFileReportHighMapper.updateById(tLawFileReportHigh);
    }

    @Override
    public TLawFileReportHighDTO reportApproval(TLawFileReportHighDTO tLawFileReportHighDTO) {
        TLawFileReportHighDTO dto = new TLawFileReportHighDTO();

        Long formDataId = Long.valueOf(SnowflakeIdUtil.getSnowFlakeId());
        TLawFileReportHigh tLawFileReportHigh = tLawFileReportHighMapper.selectById(tLawFileReportHighDTO.getId());

        Long approvalproId = tLawFileReportHigh.getApprovalId();
        LambdaQueryWrapper<CscpEnclosureFile> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpEnclosureFile::getFormDataId,approvalproId);
        List<CscpEnclosureFile> enclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(lambdaQueryWrapper);
        if(CollectionUtils.isNotEmpty(enclosureFiles)){
            //正文文件
            List<CscpEnclosureFile> documentFileList = enclosureFiles.stream().
                    filter(e -> "documentFileId".equals(e.getLxType())).collect(Collectors.toList());
            //复制到正文表
            if(CollectionUtils.isNotEmpty(documentFileList)){
                CscpEnclosureFile cscpEnclosureFile = documentFileList.get(0);
                CscpDocumentFile cscpDocumentFile = new CscpDocumentFile();
                BeanUtils.copyProperties(cscpEnclosureFile, cscpDocumentFile);
                cscpDocumentFile.setId(null);
                cscpDocumentFile.setFormDataId(formDataId);
                cscpDocumentFileMapper.insert(cscpDocumentFile);
            }

            //其它文件
            List<CscpEnclosureFile> otherFileList = new ArrayList<>();
            for (int i = 0; i < enclosureFiles.size(); i++) {
                CscpEnclosureFile cscpEnclosureFile = enclosureFiles.get(i);
                if(StringUtils.isNotEmpty(cscpEnclosureFile.getLxType()) && !"documentFileId".equals(cscpEnclosureFile.getLxType())
                && !"babgFileId".equals(cscpEnclosureFile.getLxType())
                && !"basmFileId".equals(cscpEnclosureFile.getLxType())
                ){
                    otherFileList.add(cscpEnclosureFile);
                }
            }

            if(CollectionUtils.isNotEmpty(otherFileList)){
                try{
                    CscpEnclosureFile zipFileMain = cscpEnclosureFileService.zip(otherFileList,"附件",formDataId);
                    log.info("zipFileMain 上传成功");
                }catch (Exception e){
                    log.error("zipFileMain 上传失败 {}",e.getMessage());
                }
            }

            //备案报告
            List<CscpEnclosureFile> babgFileList = enclosureFiles.stream().
                    filter(e -> "babgFileId".equals(e.getLxType())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(babgFileList)){
                babgFileList.forEach(e ->{
                    CscpEnclosureFile cscpEnclosureFile = new CscpEnclosureFile();
                    BeanUtils.copyProperties(e, cscpEnclosureFile);
                    cscpEnclosureFile.setFormDataId(formDataId);
                    cscpEnclosureFile.setId(null);
                    cscpEnclosureFileMapper.insert(cscpEnclosureFile);
                });
            }

            //备案说明
            List<CscpEnclosureFile> basmFileList = enclosureFiles.stream().
                    filter(e -> "basmFileId".equals(e.getLxType())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(basmFileList)){
                basmFileList.forEach(e ->{
                    CscpEnclosureFile cscpEnclosureFile = new CscpEnclosureFile();
                    BeanUtils.copyProperties(e, cscpEnclosureFile);
                    cscpEnclosureFile.setFormDataId(formDataId);
                    cscpEnclosureFile.setId(null);
                    cscpEnclosureFileMapper.insert(cscpEnclosureFile);
                });
            }
        }
        //TODO 报备/zip
        BeanUtils.copyProperties(tLawFileReportHigh,dto);
        dto.setFormDataId(formDataId);

        //同步formDataId
        tLawFileReportHigh.setFormDataId(formDataId);
        tLawFileReportHighMapper.updateById(tLawFileReportHigh);

        return dto;
    }

    @Override
    @Transactional
    public void submit(TLawFileReportHighDTO tLawFileReportHighDTO) {
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        String companyName = currentCscpUserDetail.getCompanyName();

        TLawFileReportHigh tLawFileReportHigh = tLawFileReportHighMapper.selectById(tLawFileReportHighDTO.getId());
        tLawFileReportHigh.setPrintDate(tLawFileReportHighDTO.getPrintDate());
        tLawFileReportHigh.setSubmit(1);
        tLawFileReportHighMapper.updateById(tLawFileReportHigh);


        //新增督查数据
        TLawFileSupervision tLawFileSupervision = new TLawFileSupervision();
        tLawFileSupervision.setCodeId(tLawFileReportHigh.getCodeId());
        tLawFileSupervision.setApprovalId(tLawFileReportHigh.getApprovalId());
        tLawFileSupervision.setTitle(tLawFileReportHigh.getTitle());
        tLawFileSupervision.setDurationClassification(tLawFileReportHigh.getDurationClassification());
        tLawFileSupervision.setDurationClassificationName(tLawFileReportHigh.getDurationClassificationName());
        tLawFileSupervision.setReportDate(tLawFileReportHighDTO.getPrintDate());
        tLawFileSupervision.setSupervisionCompanyName(companyName);
        tLawFileSupervisionMapper.insert(tLawFileSupervision);

        //2.更新主流程状态
        TLawFileCode tLawFileCode = tLawFileCodeMapper.selectById(tLawFileReportHigh.getCodeId());
        tLawFileCode.setCurrentLink(ProcessStatusEnums.DCLS.getCode());
        tLawFileCodeMapper.updateById(tLawFileCode);
    }

    @Override
    public TLawFileReportHighDTO getByApprovalId(Long approvalId) {

        LambdaQueryWrapper<TLawFileReportHigh> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLawFileReportHigh::getApprovalId, approvalId);
        TLawFileReportHigh tLawFileReportHigh = tLawFileReportHighMapper.selectOneNoAdd(queryWrapper);

        TLawFileReportHighDTO tLawFileReportHighDTO = BeanConvertUtils.
                copyProperties(tLawFileReportHigh, TLawFileReportHighDTO.class);

        LambdaQueryWrapper<TLawFileExaminePrint> printLambdaQueryWrapper =new LambdaQueryWrapper();
        printLambdaQueryWrapper.eq(TLawFileExaminePrint::getCodeId,tLawFileReportHighDTO.getCodeId());
        TLawFileExaminePrint tLawFileExaminePrint = tLawFileExaminePrintMapper.selectOneNoAdd(printLambdaQueryWrapper);
        if(tLawFileExaminePrint != null) {
            tLawFileReportHighDTO.setPrintDate(tLawFileExaminePrint.getPrintDate());
        }
        Long draftCompanyId = tLawFileReportHigh.getDraftCompanyId();
        CscpOrg cscpOrg = cscpOrgRepository.selectById(draftCompanyId);
        if(cscpOrg != null) {
            tLawFileReportHighDTO.setDraftCompanyName(cscpOrg.getOrgName());
        }

        if(ObjectUtil.isNotNull(tLawFileReportHigh.getPrintDate())){
            //获取文件
            Long approvalproId = tLawFileReportHigh.getApprovalId();
            LambdaQueryWrapper<CscpEnclosureFile> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpEnclosureFile::getFormDataId,approvalproId);
            List<CscpEnclosureFile> enclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(lambdaQueryWrapper);
            if(CollectionUtils.isNotEmpty(enclosureFiles)){
                //备案报告
                List<CscpEnclosureFile> babgFileList = enclosureFiles.stream().
                        filter(e -> "babgFileId".equals(e.getLxType())).collect(Collectors.toList());
                tLawFileReportHighDTO.setBabgFileList(babgFileList);

                //备案说明
                List<CscpEnclosureFile> basmFileList = enclosureFiles.stream().
                        filter(e -> "basmFileId".equals(e.getLxType())).collect(Collectors.toList());
                tLawFileReportHighDTO.setBasmFileList(basmFileList);
            }
        }
        return tLawFileReportHighDTO;
    }
}
