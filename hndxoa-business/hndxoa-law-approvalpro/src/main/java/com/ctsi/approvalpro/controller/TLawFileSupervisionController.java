package com.ctsi.approvalpro.controller;


import com.ctsi.approvalpro.entity.dto.TLawFileReportHighDTO;
import com.ctsi.approvalpro.entity.dto.TLawFileSupervisionDTO;
import com.ctsi.approvalpro.entity.vo.TLawFileReportHighPageVo;
import com.ctsi.approvalpro.service.ITLawFileSupervisionService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Slf4j
@RestController
@ResponseResultVo
@Api(value = "督查落实及归档存档", tags = "督查落实及归档存档接口")
@RequestMapping("/api/tLawFileSupervision")
public class TLawFileSupervisionController {

    @Autowired
    private ITLawFileSupervisionService fileSupervisionService;

    /**
     *  分页查询多条数据.
     */
    @PostMapping("/placeToFile")
    @ApiOperation(value = "归档", notes = "传入参数")
    public ResultVO<Boolean> placeToFile(@RequestBody TLawFileSupervisionDTO tLawFileReportHighDTO) {
        return ResultVO.success(fileSupervisionService.placeToFile(tLawFileReportHighDTO));
    }


    @GetMapping("/queryTLawFileSupervision")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TLawFileSupervisionDTO>> queryTLawFileSupervision(TLawFileSupervisionDTO tLawFileReportHighDTO, BasePageForm basePageForm) {
        return ResultVO.success(fileSupervisionService.queryListPage(tLawFileReportHighDTO, basePageForm));
    }

}
