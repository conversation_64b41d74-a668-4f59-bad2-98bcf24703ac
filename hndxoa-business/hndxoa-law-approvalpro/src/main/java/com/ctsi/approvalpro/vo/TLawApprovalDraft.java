package com.ctsi.approvalpro.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;

import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 法规室立项文件起草
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Getter
@Setter
@TableName("t_law_approval_draft")
@ApiModel(value = "TLawApprovalDraft对象", description = "法规室立项文件起草")
public class TLawApprovalDraft extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("立项审核通过时间")
    private LocalDateTime approvalTime;

    @ApiModelProperty("起草部门名称")
    private String departmentName;

    @ApiModelProperty("单位名称")
    private String companyName;

    @ApiModelProperty("申报的文件名")
    private String approvalFileName;

    @ApiModelProperty("密级")
    private String durationClassification;

    @ApiModelProperty("密级值")
    private String durationClassificationName;

    @ApiModelProperty("申报的发文形式2code")
    private String fileModalityTypeSec;

    @ApiModelProperty("申报的发文形式取值code")
    private String fileModalityTypeSecValue;

    @ApiModelProperty("立项类型")
    private String temApprovalType;

    @ApiModelProperty("立项类型值")
    private String temApprovalTypeValue;

    @ApiModelProperty("二维码id")
    private Long codeId;

    @ApiModelProperty("立项id")
    private Long approvalId;

    @ApiModelProperty("正文id")
    private Long documentFileId;

    @ApiModelProperty("调用论证文件id")
    private String dylzFileId;

    @ApiModelProperty("风险评估文件id")
    private String fxpgFileId;

    @ApiModelProperty("征求意见文件id")
    private String zqyjFileId;

    @ApiModelProperty("征求意见 数据字典")
    private String zqyjType;

    @ApiModelProperty("征求意见值")
    private String zqyjTypeValue;

    @ApiModelProperty("合法合规性审核id")
    private String hfhgshFileId;

    @ApiModelProperty("集体讨论研究id")
    private String jttlyjFileId;

    @ApiModelProperty("发文范围code")
    private String fileRangeType;

    @ApiModelProperty("发文范围值")
    private String fileRangeTypeValue;

    @ApiModelProperty("资料id")
    private String detailFileId;

    @ApiModelProperty(value = "申报的发文规格code")

    private String fileModalityType;

    @ApiModelProperty(value = "申报的发文规格value")
    private String  fileModalityValue;

    @ApiModelProperty(value = "文件起草状态 0 新增 1已经起草转审批")
    private String  status;
    @ApiModelProperty(value = "所属序列值")
    private String fileSeqTypeValue;
    @ApiModelProperty(value = "所属序列code")
    private String fileSeqType;
    @ApiModelProperty(value = "是否修改文件名 0 未修改  1 已经修改")
    private String isUpdateFileName;
    @ApiModelProperty(value = "起草时间")
    private LocalDateTime draftTime;
}
