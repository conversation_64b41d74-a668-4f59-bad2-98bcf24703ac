package com.ctsi.approvalpro.controller;


import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.service.ITLawFileCodeService;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.approvalpro.vo.dto.TLawApprovalPreAuditDTO;
import com.ctsi.approvalpro.vo.dto.TLawFileCodeDTO;
import com.ctsi.approvalpro.vo.dto.TLawFlowDTO;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.BeanConvertUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawFileCode")
@Api(value = "集中立项二维码接口", tags = "集中立项二维码接口")
public class TLawFileCodeController {


    @Autowired
    private ITLawFileCodeService tLawFileCodeService;

    /**
     * 获取二维码信息
     * @param tLawFileCodeDTO
     * @return
     */
    @GetMapping("/getErCode")
    @ApiOperation(value = "根据主表id获取二维码信息", notes = "传入参数")
    public ResultVO<TLawFileCodeDTO> selectByFormDataId(TLawFileCodeDTO tLawFileCodeDTO) {
        //怎加创建人的条件

        TLawFileCodeDTO dto = tLawFileCodeService.selectByFormDataId(tLawFileCodeDTO);
         return ResultVO.success(dto);
    }


    /**
     * 创建二维码
     * @param tLawFileCodeDTO
     * @return
     */
    @GetMapping("/createErCode")
    @ApiOperation(value = "根据主表id生成二维码信息", notes = "传入参数")
    public ResultVO<TLawFileCodeDTO> createErCode(TLawFileCodeDTO tLawFileCodeDTO) {
        //怎加创建人的条件
        TLawFileCode code =tLawFileCodeService.createFileCode(tLawFileCodeDTO.getFormDataId(), ProcessStatusEnums.FWLX.getCode());
        TLawFileCodeDTO dto = BeanConvertUtils.copyProperties(code,TLawFileCodeDTO.class);
        return ResultVO.success(dto);

    }


    /**
     * 根据立项id查询整个流程的数据
     */
    @GetMapping("/getFlow/{approvalId}")
    @ApiOperation(value = "查询立项流程数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<TLawFlowDTO> getFlow(@PathVariable Long approvalId) {
        TLawFlowDTO tLawFlowDTO = tLawFileCodeService.getFlow(approvalId);
        return ResultVO.success(tLawFlowDTO);
    }

}
