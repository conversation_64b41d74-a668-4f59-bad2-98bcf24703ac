package com.ctsi.approvalpro.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.approvalpro.vo.dto.TLawFileCodeDTO;
import com.ctsi.approvalpro.vo.dto.TLawFlowDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TLawFileCodeMapper extends MybatisBaseMapper<TLawFileCode> {
    @InterceptorIgnore(tenantLine = "true")
    TLawFileCodeDTO selectByFormDataId(@Param("formDataId") Long formDataId);

    @InterceptorIgnore(tenantLine = "true")
    TLawFlowDTO getFlow(@Param("id")Long approvalId);


}
