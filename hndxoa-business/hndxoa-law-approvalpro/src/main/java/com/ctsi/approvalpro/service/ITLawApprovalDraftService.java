package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.dto.TLawApprovalDraftDTO;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

/**
 * <p>
 * 法规室立项文件起草 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface ITLawApprovalDraftService extends SysBaseServiceI<TLawApprovalDraft> {

    void createDraft(TLawApprovalpro vo);

    PageResult<TLawApprovalDraftDTO> selectListPage(TLawApprovalDraftDTO tLawApprovalDraftDTO, BasePageForm basePageForm);

    TLawApprovalDraftDTO createOrUpdate(TLawApprovalDraftDTO tLawApprovalDraftDTO);

    /**
     * 根据发文立项id查询起草id
     * @param approvalId
     * @return
     */
    TLawApprovalDraftDTO findOneByApprovalId(Long approvalId);

    int removeByApprovalId(Long approvalId);

    void uploadFileName(TLawApprovalproDTO dto);
}
