package com.ctsi.approvalpro.controller;
import com.ctsi.approvalpro.entity.dto.TLawFileExamineApprovalEnclosureDto;
import com.ctsi.approvalpro.entity.dto.TLawFileExaminePrintDTO;
import com.ctsi.approvalpro.entity.vo.TLawFileReportHighPageVo;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.approvalpro.entity.TLawFileReportHigh;
import com.ctsi.approvalpro.entity.dto.TLawFileReportHighDTO;
import com.ctsi.approvalpro.service.ITLawFileReportHighService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawFileReportHigh")
@Api(value = "上报备案表", tags = "上报备案表接口")
public class TLawFileReportHighController extends BaseController {

    private static final String ENTITY_NAME = "tLawFileReportHigh";

    @Autowired
    private ITLawFileReportHighService tLawFileReportHighService;



    /**
     *  新增上报备案表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawFileReportHigh.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增上报备案表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileReportHigh.add')")
    public ResultVO createBatch(@RequestBody List<TLawFileReportHighDTO> tLawFileReportHighList) {
       Boolean  result = tLawFileReportHighService.insertBatch(tLawFileReportHighList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tLawFileReportHigh.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增上报备案表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileReportHigh.add')")
    public ResultVO<TLawFileReportHighDTO> create(@RequestBody TLawFileReportHighDTO tLawFileReportHighDTO)  {
        TLawFileReportHighDTO result = tLawFileReportHighService.create(tLawFileReportHighDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tLawFileReportHigh.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新上报备案表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileReportHigh.update')")
    public ResultVO update(@RequestBody TLawFileReportHighDTO tLawFileReportHighDTO) {
	    Assert.notNull(tLawFileReportHighDTO.getId(), "general.IdNotNull");
        int count = tLawFileReportHighService.update(tLawFileReportHighDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除上报备案表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tLawFileReportHigh.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawFileReportHigh.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLawFileReportHighService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TLawFileReportHighDTO tLawFileReportHighDTO = tLawFileReportHighService.findOne(id);
        return ResultVO.success(tLawFileReportHighDTO);
    }


    @GetMapping("/getByApprovalId/{approvalId}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getByApprovalId(@PathVariable Long approvalId) {
        TLawFileReportHighDTO tLawFileReportHighDTO = tLawFileReportHighService.getByApprovalId(approvalId);
        return ResultVO.success(tLawFileReportHighDTO);
    }


    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTLawFileReportHighPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TLawFileReportHighPageVo>> queryTLawFileReportHighPage(TLawFileReportHighDTO tLawFileReportHighDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLawFileReportHighService.queryListPage(tLawFileReportHighDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLawFileReportHigh")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TLawFileReportHighDTO>> queryTLawFileReportHigh(TLawFileReportHighDTO tLawFileReportHighDTO) {
       List<TLawFileReportHighDTO> list = tLawFileReportHighService.queryList(tLawFileReportHighDTO);
       return ResultVO.success(new ResResult<TLawFileReportHighDTO>(list));
   }


    @PostMapping("/registerReport")
    @ApiOperation(value = "登记备案", notes = "登记备案")
    public ResultVO registerReport(@RequestBody TLawFileReportHighDTO tLawFileReportHighDTO) {
         tLawFileReportHighService.registerReport(tLawFileReportHighDTO);
        return ResultVO.success();
    }

    @PostMapping("/reportApproval")
    @ApiOperation(value = "报备审发", notes = "传入参数")
    public ResultVO<TLawFileReportHighDTO> reportApproval(@RequestBody TLawFileReportHighDTO tLawFileReportHighDTO) {
        TLawFileReportHighDTO lawFileReportHighDTO = tLawFileReportHighService.reportApproval(tLawFileReportHighDTO);
        return ResultVO.success(lawFileReportHighDTO);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "提交确认", notes = "提交确认")
    public ResultVO<Boolean> submit(@RequestBody TLawFileReportHighDTO tLawFileReportHighDTO) {
        tLawFileReportHighService.submit(tLawFileReportHighDTO);
        return ResultVO.success(true);
    }
}
