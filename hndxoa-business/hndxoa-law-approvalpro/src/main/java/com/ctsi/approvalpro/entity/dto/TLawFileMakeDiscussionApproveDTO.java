package com.ctsi.approvalpro.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 文件制定审议批准表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TLawFileMakeDiscussionApproveDTO对象", description="文件制定审议批准表")
public class TLawFileMakeDiscussionApproveDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "立项Id")
    private Long approvalId;

    /**
     * 文件二维码Id
     */
    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String title;

    /**
     * 附件文件
     */
    @ApiModelProperty(value = "附件文件")
    private String meetingDiscussFiles;

    /**
     * 议事协调机构会议研讨
     */
    @ApiModelProperty(value = "议事协调机构会议研讨")
    private Integer orgDiscuss;

    /**
     * 政府常务会议审议
     */
    @ApiModelProperty(value = "政府常务会议审议")
    private Integer govDiscuss;

    /**
     * 党委常委会会议审议
     */
    @ApiModelProperty(value = "党委常委会会议审议")
    private Integer partyDiscuss;

    /**
     * 党委全会审议
     */
    @ApiModelProperty(value = "党委全会审议")
    private Integer partyAllDiscuss;

    /**
     * 党委全会审议
     */
    @ApiModelProperty(value = "部门单位党组（党委）审议")
    private Integer departPartyDiscuss;

    /**
     * 审议状态
     */
    @ApiModelProperty(value = "审议状态")
    private Integer status;

    /**
     * 密级期限code
     */
    @ApiModelProperty(value = "密级期限code")
    private String durationClassification;

    /**
     * 密级期限名称
     */
    @ApiModelProperty(value = "密级期限名称")
    private String durationClassificationName;


}
