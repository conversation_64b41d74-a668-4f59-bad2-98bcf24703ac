package com.ctsi.approvalpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.mapper.TLawApprovalproMapper;
import com.ctsi.approvalpro.service.ITLawApprovalPreAuditService;
import com.ctsi.approvalpro.service.ITLawApprovalproService;
import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.approvalpro.mapper.TLawApprovalDraftMapper;
import com.ctsi.approvalpro.service.ITLawApprovalDraftService;
import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.dto.TLawApprovalDraftDTO;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import dm.jdbc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * 法规室立项文件起草 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Slf4j
@Service
public class TLawApprovalDraftServiceImpl extends SysBaseServiceImpl<TLawApprovalDraftMapper, TLawApprovalDraft> implements ITLawApprovalDraftService {

    @Autowired
    private TLawApprovalDraftMapper mapper;

    @Autowired
    private ITLawApprovalPreAuditService tLawApprovalPreAuditService;

    @Autowired
    private ITLawApprovalproService tLawApprovalproService;


    @Override
    public void createDraft(TLawApprovalpro vo) {
        TLawApprovalDraftDTO dto =findOneByApprovalId(vo.getId());
        if(dto!=null){
            //如果已经存在就不在往里面插入数据
            return ;
        }
        TLawApprovalDraft draft = new TLawApprovalDraft();
        draft =BeanConvertUtils.copyProperties(vo,TLawApprovalDraft.class);
        draft.setApprovalId(vo.getId());
        draft.setId(Long.valueOf(SnowflakeIdUtil.getSnowFlakeId()));
        draft.setCreateTime(LocalDateTime.now());
        draft.setStatus("0");
        draft.setUpdateBy(null);
        draft.setUpdateName(null);
        draft.setUpdateTime(null);
        draft.setIsUpdateFileName("0");
        draft.setApprovalTime(vo.getExamineTime());
        save(draft);

    }

    @Override
    public PageResult<TLawApprovalDraftDTO> selectListPage(TLawApprovalDraftDTO tLawApprovalDraftDTO, BasePageForm basePageForm) {
        IPage page = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        IPage<TLawApprovalDraftDTO> resultpage = mapper.selectListPage(page ,tLawApprovalDraftDTO);
        return new PageResult<>(resultpage.getRecords(), resultpage.getTotal(), resultpage.getCurrent());

    }

    @Override
    @Transactional
    public TLawApprovalDraftDTO createOrUpdate(TLawApprovalDraftDTO tLawApprovalDraftDTO) {
        TLawApprovalDraft vo =new TLawApprovalDraft();
        vo= BeanUtil.copyProperties(tLawApprovalDraftDTO,TLawApprovalDraft.class);
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if(tLawApprovalDraftDTO.getId()!=null) {
            TLawApprovalDraft oldvo = mapper.selectById(tLawApprovalDraftDTO.getId());
            if(oldvo!=null){

                if(vo.getDocumentFileId()!=null){
                    vo.setUpdateBy(SecurityUtils.getCurrentUserId());
                    vo.setUpdateTime(LocalDateTime.now());
                    vo.setUpdateName(currentCscpUserDetail.getRealName());
                    vo.setStatus("1");
                    vo.setDraftTime(LocalDateTime.now());
                    //todo 转前置审批
                    /*try {*/
                    vo.setIsUpdateFileName("0");
                    //如果文件名有改变  需要修改文件名
                    if(StringUtil.equals(oldvo.getTemApprovalType(),"jzlx")){
                        //集中立项才能修改名称
                        if(!StringUtil.equals(oldvo.getApprovalFileName(),vo.getApprovalFileName())){
                            tLawApprovalproService.uploadFileName(vo.getApprovalId(),vo.getApprovalFileName());
                            vo.setIsUpdateFileName("1");
                        }
                    }
                    tLawApprovalPreAuditService.createAudit(vo);
                   /* }catch (Exception e){
                       log.info("文件起草转前置审批错误："+e.getMessage());
                    }*/
                    //将二维码状态修改
                    mapper.updateById(vo);
                }
                else{
                    log.info("未上传正文，不能提交文件起草");
                    return null;
                }

            }else{
                save(vo);
            }

        }
        return BeanUtil.copyProperties(vo,TLawApprovalDraftDTO.class);
    }

    @Override
    public TLawApprovalDraftDTO findOneByApprovalId(Long approvalId) {
        LambdaQueryWrapper<TLawApprovalDraft> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TLawApprovalDraft::getApprovalId,approvalId);
        TLawApprovalDraft drafe=mapper.selectOneNoAdd(queryWrapper);
        if(drafe!=null){
            return BeanUtil.copyProperties(drafe,TLawApprovalDraftDTO.class);
        }
        return null;
    }

    @Override
    public int removeByApprovalId(Long approvalId) {
        return mapper.removeByApprovalId(approvalId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadFileName(TLawApprovalproDTO dto) {
        tLawApprovalproService.uploadFileName(dto.getId(),dto.getApprovalFileName());
        mapper.uploadFileName(dto.getId(),dto.getApprovalFileName());
    }
}
