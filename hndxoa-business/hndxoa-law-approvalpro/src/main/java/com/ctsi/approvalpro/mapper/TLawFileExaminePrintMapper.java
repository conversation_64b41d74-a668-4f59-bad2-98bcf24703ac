package com.ctsi.approvalpro.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.dto.TLawFileExaminePrintDTO;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintPageVo;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 审核审发及印制发布表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface TLawFileExaminePrintMapper extends MybatisBaseMapper<TLawFileExaminePrint> {

    @InterceptorIgnore(tenantLine = "true")
    Integer queryListPageCount(@Param("entityDTO") TLawFileExaminePrintDTO entityDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<TLawFileExaminePrintPageVo> queryListPage(@Param("entityDTO")TLawFileExaminePrintDTO entityDTO, @Param("statrIndex") Integer statrIndex,
                                                   @Param("pageSize") Integer pageSize);
}
