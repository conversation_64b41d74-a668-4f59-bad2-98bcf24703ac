package com.ctsi.approvalpro.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_law_approvalpro_flow")
@ApiModel(value="TLawApprovalproFlow对象", description="集中立项流程记录")
public class TLawApprovalproFlow extends BaseEntity {

    @ApiModelProperty(value = "立项表的id")
    private Long formDataId;
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "类型 1 新增 2 修改 3 驳回 4 审核通过")
    private String type;
}
