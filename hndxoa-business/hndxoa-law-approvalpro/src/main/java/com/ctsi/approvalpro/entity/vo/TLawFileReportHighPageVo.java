package com.ctsi.approvalpro.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class TLawFileReportHighPageVo {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "立项Id")
    private Long approvalId;

    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    @ApiModelProperty(value = "文件名称")
    private String title;

    /**
     * 截止日期
     */
    @ApiModelProperty(value = "截止日期")
    private Date endDate;

    /**
     * 报备日期
     */
    @ApiModelProperty(value = "报备日期")
    private Date reportDate;


    /**
     * 预警状态 0:未报备，1:已报备，2：距离报备截止日期不足7天未报备
     */
    @ApiModelProperty(value = "报备状态")
    private Integer reportStatus;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;


    @ApiModelProperty(value = "流程当前环节")
    private String currentStage;

    @ApiModelProperty(value = "流程当前用户")
    private String currentUserName;

    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;
}
