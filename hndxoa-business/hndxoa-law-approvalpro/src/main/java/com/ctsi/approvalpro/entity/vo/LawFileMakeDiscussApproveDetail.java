package com.ctsi.approvalpro.entity.vo;

import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigInteger;
import java.util.List;

/**
 * 审议批准详情表
 */
@Data
public class LawFileMakeDiscussApproveDetail {

    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    private Long id;

    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    /**
     * 议事协调机构会议研讨
     */
    @ApiModelProperty(value = "议事协调机构会议研讨")
    private Integer orgDiscuss;

    /**
     * 政府常务会议审议
     */
    @ApiModelProperty(value = "政府常务会议审议")
    private Integer govDiscuss;

    /**
     * 党委常委会会议审议
     */
    @ApiModelProperty(value = "党委常委会会议审议")
    private Integer partyDiscuss;

    /**
     * 党委全会审议
     */
    @ApiModelProperty(value = "党委全会审议")
    private Integer partyAllDiscuss;

    /**
     * 审议状态
     */
    @ApiModelProperty(value = "审议状态")
    private Integer status;

    @ApiModelProperty(value = "附件")
    List<CscpEnclosureFile> cscpEnclosureFiles;
}
