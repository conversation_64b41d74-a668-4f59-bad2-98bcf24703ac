package com.ctsi.approvalpro.vo.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UTLawAppDTO对象", description = "批量审批使用")
public class UTLawAppDTO {
    private static final long serialVersionUID = 2L;
    @ApiModelProperty("批量审批的数据")
    private List<TLawApprovalproDTO> list;
    @ApiModelProperty("立项文号")
    private String fileCode;

    private Long examineCompanyId;
    private Long examineId;
    private String examineCompanyName;
    private String examineName;
    private String examinePhone;
    private Long examineDepartmentId;
    private String  examineDepartmentName;
}
