package com.ctsi.approvalpro.controller;

import com.ctsi.activiti.permission.annotation.Permission;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.service.ITLawApprovalPreAuditService;
import com.ctsi.approvalpro.service.ITLawApprovalproService;
import com.ctsi.approvalpro.service.ITLawFileCodeService;
import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.approvalpro.vo.dto.*;
import com.ctsi.hndx.annotations.LimitSubmit;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import dm.jdbc.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawApprovalpro")
@Api(value = "集中/临时立项接口", tags = "集中/临时立项接口")
public class TLawApprovalproController extends BaseController {


    @Autowired
    private ITLawApprovalproService tLawApprovalproService;

    @Autowired
    private ExportToExcelService exportToExcelService;

    @Autowired
    private ITLawFileCodeService tLawFileCodeService;




    /**
     * 新增数据.  集中立项
     */
    /*@PostMapping("/createOrUpdate")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "插入或者新增集中立项")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO<TLawApprovalproDTO> createOrUpdate(@RequestBody TLawApprovalproDTO tLawApprovalproDTO) {
        TLawApprovalproDTO result= tLawApprovalproService.createOrUpdate(tLawApprovalproDTO);
        return ResultVO.success(result);
    }*/




    @PostMapping("/createOrUpdate")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "插入或者新增集中立项")
    //@LimitSubmit(key = "proCreateOrUpdate:%s:#id",limit = 5,needAllWait = true)
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO<TLawApprovalproDTO> createOrUpdate(@RequestBody TLawApprovalproDTO tLawApprovalproDTO) {

        TLawApprovalproDTO result= tLawApprovalproService.createOrUpdate(tLawApprovalproDTO);
        return ResultVO.success(result);
    }


    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除集中立项数据", notes = "删除集中立项数据")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除集中立项数据")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLawApprovalproService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }



    /**
     * 撤回提交的数据数据.
     */
    @DeleteMapping("/withdraw/{id}")
    @ApiOperation(value = "撤回提交的数据数据.", notes = "撤回提交的数据数据.")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "撤回提交的数据数据.")
    public ResultVO withdraw(@PathVariable Long id) {
        int count = tLawApprovalproService.withdraw(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }



    /**
     * 个人进来查询自己的立项
     * @param tLawApprovalproDTO
     * @param basePageForm
     * @return
     */

    @GetMapping("/queryPage")
    @ApiOperation(value = "查询多条数据--申报单位", notes = "传入参数")
    public ResultVO<PageResult<TLawApprovalproDTO>> queryPage(TLawApprovalproDTO tLawApprovalproDTO, BasePageForm basePageForm) {
        //怎加创建人的条件
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        tLawApprovalproDTO.setCreateBy(currentCscpUserDetail.getId());
        tLawApprovalproDTO.setDepartmentId(currentCscpUserDetail.getDepartmentId());
        PageResult<TLawApprovalproDTO> list = tLawApprovalproService.selectListPage(tLawApprovalproDTO,basePageForm);
        return ResultVO.success(list);
    }


    /**
     * 法规室进来查询所有的集中立项
     * @param tLawApprovalproDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryAllPage")
    @ApiOperation(value = "查询多条数据--法规室", notes = "传入参数")
    public ResultVO<PageResult<TLawApprovalproDTO>> queryAllPage(TLawApprovalproDTO tLawApprovalproDTO, BasePageForm basePageForm) {
        PageResult<TLawApprovalproDTO> list = tLawApprovalproService.selectListAllPage(tLawApprovalproDTO,basePageForm);
        return ResultVO.success(list);
    }



    /**
     * 归档管理 导出档案
     * */
    @GetMapping("/exportFile")
    @ApiOperation(value = "归档管理 导出档案接口", notes = "传入参数")
    public void exportAllArchiveFile(  TLawApprovalproDTO tLawApprovalproDTO,HttpServletResponse response) {
        tLawApprovalproService.selectListByCondition(tLawApprovalproDTO,response);
    }


    /**
     * 选择导出接口
     * @param tLawApprovalproDTOs
     * @param response
     */

    @PostMapping("/exportChooseFile")
    @ApiOperation(value = "归档管理 导出档案接口", notes = "传入参数")
    public void exportChooseFile(@RequestBody List<TLawApprovalproDTO> tLawApprovalproDTOs, HttpServletResponse response) {
        boolean bool = false;
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        try{
            List<TLawApprovalproExcel> eList = new ArrayList<>();

            for(TLawApprovalproDTO dto :tLawApprovalproDTOs){
                TLawApprovalproExcel excel = new TLawApprovalproExcel();
                excel.setApprovalFileName(dto.getApprovalFileName());
                excel.setCompanyName(dto.getCompanyName());
                excel.setCreateName(dto.getCreateName());
                excel.setAllFileModalityValue(dto.getFileModalityValue()+(dto.getFileModalityTypeSecValue()==null?"":"-"+dto.getFileModalityTypeSecValue()));
                excel.setStatusValue(dto.getStatusValue());

                excel.setDeclareTimeDay(dto.getDeclareTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                eList.add(excel);
            }
            bool=  exportToExcelService.exportToExcel(eList,TLawApprovalproExcel.class,response);
        }catch(Exception e){
            e.printStackTrace();
        }


    }


    /**
     * 批量审批
     * */
    @PostMapping("/updateByIds")
    @ApiOperation(value = "批量审批", notes = "传入参数")
    public ResultVO<UTLawAppDTO> updateByIds( @RequestBody UTLawAppDTO dto ) {
        //tLawApprovalpro
        tLawApprovalproService.examineBatchById(dto);
        return ResultVO.success(dto);
    }



    /**
     * 根据立项id查询数据
     */
    @GetMapping("/get/{approvalId}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long approvalId) {
        TLawApprovalpro vo = tLawApprovalproService.getById(approvalId);
        TLawApprovalproDTO dto = BeanConvertUtils.copyProperties(vo,TLawApprovalproDTO.class);
        TLawFileCodeDTO code =tLawFileCodeService.getOneByApprovalId(dto.getId());
        if(code!=null){
            dto.setCurrentLink(code.getCurrentLink());
        }
        return ResultVO.success(dto);
    }


    /**
     * 取回流程数据
     * @param approvalId
     * @return
     */
    @GetMapping("/retrieveByApprovalId/{approvalId}")
    @ApiOperation(value = "取回流程数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO retrieveByApprovalId(@PathVariable Long approvalId) {
        TLawApprovalpro pro =tLawApprovalproService.getById(approvalId);
        if(StringUtil.equals(pro.getStatus(),"3")){
            TLawFileCodeDTO tLawFileCode=tLawFileCodeService.getOneByApprovalId(approvalId);
            if(Integer.valueOf(tLawFileCode.getCurrentLink())>=5){
                return ResultVO.error("立项数据已经进入审核审发，不能进行调整操作。");
            }
            TLawApprovalpro vo = tLawApprovalproService.retrieveByApprovalId(pro);
            tLawFileCode.setCurrentLink(ProcessStatusEnums.FWLX.getCode());
            tLawFileCode.setCurrentLinkStatus("0");
            TLawFileCode code = BeanConvertUtils.copyProperties(tLawFileCode,TLawFileCode.class);
            tLawFileCodeService.updateById(code);
            TLawApprovalproDTO dto = BeanConvertUtils.copyProperties(vo,TLawApprovalproDTO.class);

            return ResultVO.success(dto);
        }else{
            return ResultVO.error("审核通过的立项数据才能进行调整操作。");
        }

    }



}
