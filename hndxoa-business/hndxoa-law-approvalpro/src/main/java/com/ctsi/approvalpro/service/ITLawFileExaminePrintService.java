package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.entity.dto.TLawFileExamineApprovalEnclosureDto;
import com.ctsi.approvalpro.entity.dto.TLawFileExaminePrintDTO;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.vo.LawFileMakeDiscussApproveDetail;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintDetail;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintPageVo;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 审核审发及印制发布表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface ITLawFileExaminePrintService extends SysBaseServiceI<TLawFileExaminePrint> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TLawFileExaminePrintPageVo> queryListPage(TLawFileExaminePrintDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TLawFileExaminePrintDTO> queryList(TLawFileExaminePrintDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TLawFileExaminePrintDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TLawFileExaminePrintDTO create(TLawFileExaminePrintDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TLawFileExaminePrintDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTLawFileExaminePrintId
     * @param code
     * @return
     */
    boolean existByTLawFileExaminePrintId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TLawFileExaminePrintDTO> dataList);


    TLawFileExamineApprovalEnclosureDto tranferApprovalEnclosure(TLawFileExaminePrintDTO tLawFileExaminePrintDTO);

    Boolean reportToHigh(TLawFileExaminePrintDTO tLawFileExaminePrintDTO);

    TLawFileExaminePrintDetail getByApprovalId(Long approvalId);
}
