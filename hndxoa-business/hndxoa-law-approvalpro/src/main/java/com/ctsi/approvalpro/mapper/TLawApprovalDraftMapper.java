package com.ctsi.approvalpro.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.approvalpro.vo.dto.TLawApprovalDraftDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 法规室立项文件起草 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Mapper
public interface TLawApprovalDraftMapper extends MybatisBaseMapper<TLawApprovalDraft> {

    IPage<TLawApprovalDraftDTO> selectListPage(IPage page, @Param("dto") TLawApprovalDraftDTO tLawApprovalDraftDTO);

    int removeByApprovalId(@Param("approvalId") Long approvalId);

    void uploadFileName(@Param("approvalId") Long approvalId, @Param("approvalFileName")String approvalFileName);
}
