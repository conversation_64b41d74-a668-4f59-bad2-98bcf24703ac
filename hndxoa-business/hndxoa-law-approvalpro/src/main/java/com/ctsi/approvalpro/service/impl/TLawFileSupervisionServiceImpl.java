package com.ctsi.approvalpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.entity.TLawFileReportHigh;
import com.ctsi.approvalpro.entity.TLawFileSupervision;
import com.ctsi.approvalpro.entity.dto.TLawFileReportHighDTO;
import com.ctsi.approvalpro.entity.dto.TLawFileSupervisionDTO;
import com.ctsi.approvalpro.entity.vo.TLawFileReportHighPageVo;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.mapper.TLawFileCodeMapper;
import com.ctsi.approvalpro.mapper.TLawFileSupervisionMapper;
import com.ctsi.approvalpro.service.ITLawFileSupervisionService;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Service
public class TLawFileSupervisionServiceImpl extends SysBaseServiceImpl<TLawFileSupervisionMapper, TLawFileSupervision> implements ITLawFileSupervisionService {


    @Autowired
    private TLawFileSupervisionMapper tLawFileSupervisionMapper;

    @Autowired
    private TLawFileCodeMapper tLawFileCodeMapper;

    @Override
    public PageResult<TLawFileSupervisionDTO> queryListPage(TLawFileSupervisionDTO tLawFileReportHighDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TLawFileSupervision> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(tLawFileReportHighDTO.getTitle()), TLawFileSupervision::getTitle, tLawFileReportHighDTO.getTitle());
        queryWrapper.orderByAsc(TLawFileSupervision::getToFileStatus).orderByDesc(TLawFileSupervision::getReportDate);

        IPage<TLawFileSupervision> pageData = tLawFileSupervisionMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TLawFileSupervisionDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLawFileSupervisionDTO.class));
        return new PageResult<TLawFileSupervisionDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    @Override
    @Transactional
    public Boolean placeToFile(TLawFileSupervisionDTO tLawFileReportHighDTO) {
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        String companyName = currentCscpUserDetail.getCompanyName();

        TLawFileSupervision tLawFileSupervision = tLawFileSupervisionMapper.selectById(tLawFileReportHighDTO.getId());
        tLawFileSupervision.setToFileStatus(1);
        tLawFileSupervision.setPlaceCompanyName(companyName);
        tLawFileSupervision.setPlaceTime(new Date());
        tLawFileSupervisionMapper.updateById(tLawFileSupervision);

        //2.更新主流程状态
        TLawFileCode tLawFileCode = tLawFileCodeMapper.selectById(tLawFileSupervision.getCodeId());
        tLawFileCode.setCurrentLink(ProcessStatusEnums.GDCD.getCode());
        tLawFileCodeMapper.updateById(tLawFileCode);

        return Boolean.TRUE;
    }


}
