package com.ctsi.approvalpro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.vo.LawFileMakeDiscussApproveDetail;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.mapper.TLawApprovalproMapper;
import com.ctsi.approvalpro.mapper.TLawFileCodeMapper;
import com.ctsi.approvalpro.mapper.TLawFileExaminePrintMapper;
import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.approvalpro.entity.dto.TLawFileMakeDiscussionApproveDTO;
import com.ctsi.approvalpro.mapper.TLawFileMakeDiscussionApproveMapper;
import com.ctsi.approvalpro.service.ITLawFileMakeDiscussionApproveService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 文件制定审议批准表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Slf4j
@Service
public class TLawFileMakeDiscussionApproveServiceImpl extends SysBaseServiceImpl<TLawFileMakeDiscussionApproveMapper, TLawFileMakeDiscussionApprove> implements ITLawFileMakeDiscussionApproveService {

    @Autowired
    private TLawFileMakeDiscussionApproveMapper tLawFileMakeDiscussionApproveMapper;

    @Autowired
    private CscpEnclosureFileService fileService;

    @Autowired
    private TLawFileCodeMapper tLawFileCodeMapper;

    @Autowired
    private TLawFileExaminePrintMapper tLawFileExaminePrintMapper;

    @Autowired
    private TLawApprovalproMapper tLawApprovalproMapper;

    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileMapper;




    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLawFileMakeDiscussionApproveDTO> queryListPage(TLawFileMakeDiscussionApproveDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TLawFileMakeDiscussionApprove> queryWrapper = new LambdaQueryWrapper();

        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()),TLawFileMakeDiscussionApprove::getTitle, entityDTO.getTitle());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getOrgDiscuss()),TLawFileMakeDiscussionApprove::getOrgDiscuss, entityDTO.getOrgDiscuss());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getGovDiscuss()),TLawFileMakeDiscussionApprove::getGovDiscuss, entityDTO.getGovDiscuss());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getPartyDiscuss()),TLawFileMakeDiscussionApprove::getPartyDiscuss,entityDTO.getPartyDiscuss());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getPartyAllDiscuss()),TLawFileMakeDiscussionApprove::getPartyAllDiscuss, entityDTO.getPartyAllDiscuss());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getDepartPartyDiscuss()),TLawFileMakeDiscussionApprove::getDepartPartyDiscuss, entityDTO.getDepartPartyDiscuss());

        queryWrapper.orderByAsc(TLawFileMakeDiscussionApprove::getStatus).orderByDesc(TLawFileMakeDiscussionApprove::getDraftTime);

        IPage<TLawFileMakeDiscussionApprove> pageData = tLawFileMakeDiscussionApproveMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TLawFileMakeDiscussionApproveDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TLawFileMakeDiscussionApproveDTO.class));

        return new PageResult<TLawFileMakeDiscussionApproveDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLawFileMakeDiscussionApproveDTO> queryList(TLawFileMakeDiscussionApproveDTO entityDTO) {
        LambdaQueryWrapper<TLawFileMakeDiscussionApprove> queryWrapper = new LambdaQueryWrapper();
            List<TLawFileMakeDiscussionApprove> listData = tLawFileMakeDiscussionApproveMapper.selectList(queryWrapper);
            List<TLawFileMakeDiscussionApproveDTO> TLawFileMakeDiscussionApproveDTOList = ListCopyUtil.copy(listData, TLawFileMakeDiscussionApproveDTO.class);
        return TLawFileMakeDiscussionApproveDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLawFileMakeDiscussionApproveDTO findOne(Long id) {
        TLawFileMakeDiscussionApprove  tLawFileMakeDiscussionApprove =  tLawFileMakeDiscussionApproveMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tLawFileMakeDiscussionApprove,TLawFileMakeDiscussionApproveDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLawFileMakeDiscussionApproveDTO create(TLawFileMakeDiscussionApproveDTO entityDTO) {
       TLawFileMakeDiscussionApprove tLawFileMakeDiscussionApprove =  BeanConvertUtils.copyProperties(entityDTO,TLawFileMakeDiscussionApprove.class);
        save(tLawFileMakeDiscussionApprove);
        return  BeanConvertUtils.copyProperties(tLawFileMakeDiscussionApprove,TLawFileMakeDiscussionApproveDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLawFileMakeDiscussionApproveDTO entity) {
        TLawFileMakeDiscussionApprove tLawFileMakeDiscussionApprove = BeanConvertUtils.copyProperties(entity,TLawFileMakeDiscussionApprove.class);
        return tLawFileMakeDiscussionApproveMapper.updateById(tLawFileMakeDiscussionApprove);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLawFileMakeDiscussionApproveMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLawFileMakeDiscussionApproveId
     * @return
     */
    @Override
    public boolean existByTLawFileMakeDiscussionApproveId(Long TLawFileMakeDiscussionApproveId) {
        if (TLawFileMakeDiscussionApproveId != null) {
            LambdaQueryWrapper<TLawFileMakeDiscussionApprove> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLawFileMakeDiscussionApprove::getId, TLawFileMakeDiscussionApproveId);
            List<TLawFileMakeDiscussionApprove> result = tLawFileMakeDiscussionApproveMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLawFileMakeDiscussionApproveDTO> dataList) {
        List<TLawFileMakeDiscussionApprove> result = ListCopyUtil.copy(dataList, TLawFileMakeDiscussionApprove.class);
        return saveBatch(result);
    }

    @Override
    public LawFileMakeDiscussApproveDetail getDetail(Long id) {
        LawFileMakeDiscussApproveDetail discussApproveDetail = new LawFileMakeDiscussApproveDetail();

        TLawFileMakeDiscussionApprove discussion = tLawFileMakeDiscussionApproveMapper.selectById(id);
        BeanUtils.copyProperties(discussion, discussApproveDetail);
        if(StringUtils.isNotEmpty(discussion.getMeetingDiscussFiles())){
            List<String> fileList =  Arrays.asList(discussion.getMeetingDiscussFiles().split(","));
            LambdaQueryWrapper<CscpEnclosureFile> query = new LambdaQueryWrapper<>();
            query.in(CscpEnclosureFile::getId,fileList);
            List<CscpEnclosureFile> list = fileService.selectListNoAdd(query);
            discussApproveDetail.setCscpEnclosureFiles(list);
        }
        return discussApproveDetail;
    }

    @Override
    @Transactional
    public void upload(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO) {
        Long id = tLawFileMakeDiscussionApproveDTO.getId();
        TLawFileMakeDiscussionApprove tLawFileMakeDiscussionApprove = tLawFileMakeDiscussionApproveMapper.selectById(id);

        tLawFileMakeDiscussionApprove.setMeetingDiscussFiles(tLawFileMakeDiscussionApproveDTO.getMeetingDiscussFiles());
        if(ObjectUtil.isNotNull(tLawFileMakeDiscussionApproveDTO.getOrgDiscuss())){
            tLawFileMakeDiscussionApprove.setOrgDiscuss(tLawFileMakeDiscussionApproveDTO.getOrgDiscuss());
        }
        if(ObjectUtil.isNotNull(tLawFileMakeDiscussionApproveDTO.getGovDiscuss())){
            tLawFileMakeDiscussionApprove.setGovDiscuss(tLawFileMakeDiscussionApproveDTO.getGovDiscuss());
        }
        if(ObjectUtil.isNotNull(tLawFileMakeDiscussionApproveDTO.getPartyDiscuss())){
            tLawFileMakeDiscussionApprove.setPartyDiscuss(tLawFileMakeDiscussionApproveDTO.getPartyDiscuss());
        }
        if(ObjectUtil.isNotNull(tLawFileMakeDiscussionApproveDTO.getPartyAllDiscuss())){
            tLawFileMakeDiscussionApprove.setPartyAllDiscuss(tLawFileMakeDiscussionApproveDTO.getPartyAllDiscuss());
        }
        if(ObjectUtil.isNotNull(tLawFileMakeDiscussionApproveDTO.getDepartPartyDiscuss())){
            tLawFileMakeDiscussionApprove.setDepartPartyDiscuss(tLawFileMakeDiscussionApproveDTO.getDepartPartyDiscuss());
        }
        
        extracted(tLawFileMakeDiscussionApproveDTO, tLawFileMakeDiscussionApprove);
    }

    private void extracted(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO, TLawFileMakeDiscussionApprove tLawFileMakeDiscussionApprove) {
        
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        String companyName = currentCscpUserDetail.getCompanyName();

        if(tLawFileMakeDiscussionApprove.getStatus() == 1){
            throw new BusinessException("数据已审议,不可以重复提交");
        }

        //已审议
        tLawFileMakeDiscussionApprove.setStatus(1);
        tLawFileMakeDiscussionApprove.setApproveTime(LocalDateTime.now());
        tLawFileMakeDiscussionApprove.setCompanyName(companyName);
        tLawFileMakeDiscussionApproveMapper.updateById(tLawFileMakeDiscussionApprove);

        //更新主表状态
        TLawFileCode tLawFileCode = tLawFileCodeMapper.selectById(tLawFileMakeDiscussionApproveDTO.getCodeId());
        tLawFileCode.setCurrentLink(ProcessStatusEnums.SHSF.getCode());
        tLawFileCodeMapper.updateById(tLawFileCode);

        //生成 审核审发 数据
        TLawFileExaminePrint fileExaminePrint = new TLawFileExaminePrint();
        LambdaQueryWrapper<TLawApprovalpro> lawApprovalproLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lawApprovalproLambdaQueryWrapper.eq(TLawApprovalpro::getCodeId, tLawFileMakeDiscussionApproveDTO.getCodeId());
        TLawApprovalpro tLawApprovalpro = tLawApprovalproMapper.selectOneNoAdd(lawApprovalproLambdaQueryWrapper);

        fileExaminePrint.setCodeId(tLawFileMakeDiscussionApproveDTO.getCodeId());
        fileExaminePrint.setTitle(tLawApprovalpro.getApprovalFileName());
        fileExaminePrint.setFileModalityType(tLawApprovalpro.getFileModalityType());
        fileExaminePrint.setFileModalityValue(tLawApprovalpro.getFileModalityValue());
        fileExaminePrint.setFileModalityTypeSec(tLawApprovalpro.getFileModalityTypeSec());
        fileExaminePrint.setFileModalityTypeSecValue(tLawApprovalpro.getFileModalityTypeSecValue());
        fileExaminePrint.setDurationClassification(tLawApprovalpro.getDurationClassification());
        fileExaminePrint.setDurationClassificationName(tLawApprovalpro.getDurationClassificationName());
        fileExaminePrint.setDraftCompanyId(tLawApprovalpro.getCompanyId());
        fileExaminePrint.setDraftDepartmentId(tLawApprovalpro.getDepartmentId());
        fileExaminePrint.setApprovalId(tLawApprovalpro.getId());
        tLawFileExaminePrintMapper.insert(fileExaminePrint);
    }

    @Override
    public void skip(TLawFileMakeDiscussionApproveDTO tLawFileMakeDiscussionApproveDTO) {
        Long id = tLawFileMakeDiscussionApproveDTO.getId();
        TLawFileMakeDiscussionApprove tLawFileMakeDiscussionApprove = tLawFileMakeDiscussionApproveMapper.selectById(id);
        extracted(tLawFileMakeDiscussionApproveDTO, tLawFileMakeDiscussionApprove);
    }

    @Override
    public int removeByApprovalId(Long approvalId) {
        return tLawFileMakeDiscussionApproveMapper.removeByApprovalId(approvalId);
    }

    @Override
    public LawFileMakeDiscussApproveDetail getByApprovalId(Long approvalId) {
        LawFileMakeDiscussApproveDetail discussApproveDetail = new LawFileMakeDiscussApproveDetail();

        LambdaQueryWrapper<TLawFileMakeDiscussionApprove> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TLawFileMakeDiscussionApprove::getApprovalId,approvalId);
        TLawFileMakeDiscussionApprove discussion = tLawFileMakeDiscussionApproveMapper.selectOneNoAdd(lambdaQueryWrapper);

        BeanUtils.copyProperties(discussion, discussApproveDetail);

        LambdaQueryWrapper<CscpEnclosureFile> fileLambdaQueryWrapper =new LambdaQueryWrapper<>();
        fileLambdaQueryWrapper.eq(CscpEnclosureFile::getFormDataId,approvalId);
        List<CscpEnclosureFile> enclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(fileLambdaQueryWrapper);
        if(CollectionUtils.isNotEmpty(enclosureFiles)){
            //备案报告
            List<CscpEnclosureFile> sypzFileList = enclosureFiles.stream().
                    filter(e -> "sypzFileId".equals(e.getLxType())).collect(Collectors.toList());
            discussApproveDetail.setCscpEnclosureFiles(sypzFileList);
        }
        return discussApproveDetail;
    }


}
