package com.ctsi.approvalpro.service;


import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.approvalpro.vo.dto.TLawFileCodeDTO;
import com.ctsi.approvalpro.vo.dto.TLawFlowDTO;
import com.ctsi.hndx.common.SysBaseServiceI;

public interface ITLawFileCodeService extends SysBaseServiceI<TLawFileCode> {

    TLawFileCodeDTO selectByFormDataId(TLawFileCodeDTO tLawFileCodeDTO);


    TLawFileCode createFileCode(Long id,String status);

    TLawFileCode updateFileCode(Long codeId, String code);

    TLawFlowDTO getFlow(Long approvalId);

    TLawFileCodeDTO getOneByApprovalId(Long approvalId);
}
