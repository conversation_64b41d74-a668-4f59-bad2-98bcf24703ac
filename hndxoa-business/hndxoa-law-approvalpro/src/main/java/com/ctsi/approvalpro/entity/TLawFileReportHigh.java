package com.ctsi.approvalpro.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 上报备案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_law_file_report_high")
@ApiModel(value="TLawFileReportHigh对象", description="上报备案表")
public class TLawFileReportHigh extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 文件二维码Id
     */
    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    private Long approvalId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String title;

    @ApiModelProperty(value = "0待提交 1已提交")
    private Integer submit;

    /**
     * 密级期限code
     */
    @ApiModelProperty(value = "密级期限code")
    private String durationClassification;

    /**
     * 密级期限名称
     */
    @ApiModelProperty(value = "密级期限名称")
    private String durationClassificationName;

    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    private String durationYear;

    /**
     * 起草单位id
     */
    @ApiModelProperty(value = "起草单位id")
    private Long draftCompanyId;

    /**
     * 起草部门id
     */
    @ApiModelProperty(value = "起草部门id")
    private Long draftDepartmentId;

    /**
     * 打印日期
     */
    @ApiModelProperty(value = "打印日期")
    private Date printDate;

    /**
     * 截止日期
     */
    @ApiModelProperty(value = "截止日期")
    private Date endDate;

    /**
     * 报备日期
     */
    @ApiModelProperty(value = "报备日期")
    private Date reportDate;

    /**
     * 流程formDateId
     */
    @ApiModelProperty(value = "流程formDateId")
    private Long formDataId;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "来文单位")
    private String lwDepartmentName;

    @ApiModelProperty(value = "来文日期")
    private String lwDate;

    @ApiModelProperty(value = "来文/发文字号")
    private String lwNum;

    @ApiModelProperty(value = "序号")
    private String lwSortNum;

    @ApiModelProperty(value = "类别")
    private String lwType;

    @ApiModelProperty(value = "拟办意见")
    private String proposedOpinions;

    @ApiModelProperty(value = "厅领导意见")
    private String leaderOpinion;

    @ApiModelProperty(value = "省领导批示")
    private String provinceLeaderOpinion;

    @ApiModelProperty(value = "印发范围")
    private String printRange;

    @ApiModelProperty(value = "定密依据")
    private String secretBase;

    @ApiModelProperty(value = "紧急程度")
    private String urgentLevel;

    @ApiModelProperty(value = "处室负责人")
    private String departMan;

    @ApiModelProperty(value = "审核")
    private String auditMan;

    @ApiModelProperty(value = "经办人")
    private String corssMan;

    @ApiModelProperty(value = "处室共享")
    private String share;
}
