package com.ctsi.approvalpro.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_law_approvalpro")
@ApiModel(value="TLawApprovalpro对象", description="集中立项")
public class TLawApprovalpro extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "起草部门名称")
    private  String  departmentName;
    @ApiModelProperty(value = "起草单位名称")
    private String  companyName;
    @ApiModelProperty(value = "申报的文件名")
    private String  approvalFileName;
    @ApiModelProperty(value = "申报联系人名称")
    private String  contactPerson;
    @ApiModelProperty(value = "申报联系人电话")
    private String contactPhone;
    @ApiModelProperty(value = "申报的发文依据")
    private String  fileEvidence;
    @ApiModelProperty(value = "申报的发文规格1code")
    private String fileModalityType;
    @ApiModelProperty(value = "申报的发文规格1code")
    private String  fileModalityValue;
    @ApiModelProperty(value = "审核部门id")
    private Long examineDepartmentId;
    @ApiModelProperty(value = "审核部门名称")
    private String  examineDepartmentName;
    @ApiModelProperty(value = "审核单位id")
    private Long examineCompanyId;
    @ApiModelProperty(value = "审核单位名称")
    private String examineCompanyName;
    @ApiModelProperty(value = "审核联系人id")
    private Long  examineId;
    @ApiModelProperty(value = "审核联系人名称")
    private String examineName;
    @ApiModelProperty(value = "暂留  如果需要修改联系人")
    private String examineContactPhone;
    @ApiModelProperty(value = "暂留  如果需要修改电话")
    private String examineContactPerson;
    @ApiModelProperty(value = "审核联系人名称")
    private String  examinePhone;
    @ApiModelProperty(value = "审核状态 0 未申报 1 已申报 2已驳回 3 审核通过")
    private String status;
    @ApiModelProperty(value = "是否纳入计划的建议及理由")
    private String  adviseAndReason;

    @ApiModelProperty(value = "申报时间")
    private LocalDateTime declareTime;
    @ApiModelProperty(value = "发文类型1")
    private String fileTypeFirst;
    @ApiModelProperty(value = "发文类型2")
    private String fileTypeSec;
    @ApiModelProperty(value = "发文类型3")
    private String fileTypeThird;
    @ApiModelProperty(value = "发文类型1Value")
    private String fileTypeFirstValue;
    @ApiModelProperty(value = "发文类型2Value")
    private String fileTypeSecValue;
    @ApiModelProperty(value = "发文类型3Value")
    private String fileTypeThirdValue;
    @ApiModelProperty(value = "申报的发文规格2code")
    private String fileModalityTypeSec;
    @ApiModelProperty(value = "申报的发文规格2Value")
    private String  fileModalityTypeSecValue;
    @ApiModelProperty(value = "立项文号")
    private String  fileCode;
    @ApiModelProperty(value = "拟发文时间")
    private LocalDateTime publishTime;
    @ApiModelProperty(value = "审核时间--立项时间")
    private LocalDateTime examineTime;
    @ApiModelProperty(value = "1集中立项 2临时立项")
    private String approvalType;
    @ApiModelProperty(value = "临时立项类型ceode 数据字典 lslxlx")
    private String temApprovalType;
    @ApiModelProperty(value = "临时立项类型值 数据字典 lslxlx")
    private String temApprovalTypeValue;
    @ApiModelProperty(value = "临时立项子类型code 数据字典 临时立项类型ceode找查找字典")
    private String temApprovalTypeSec;
    @ApiModelProperty(value = "临时立项子类型code 数据字典 临时立项类型ceode找查找字典")
    private String temApprovalTypeSecValue;
    @ApiModelProperty(value = "密级")
    private String durationClassification;
    @ApiModelProperty(value = "密级值")
    private String durationClassificationName;
    @ApiModelProperty(value = "二维码id")
    private Long codeId;
    @ApiModelProperty(value = "所属序列值")
    private String fileSeqTypeValue;
    @ApiModelProperty(value = "所属序列code")
    private String fileSeqType;

    @ApiModelProperty(value = "发文目的")
    private String fileAim;
    @ApiModelProperty(value = "立项资料文件")
    private String  lxFileId;


}
