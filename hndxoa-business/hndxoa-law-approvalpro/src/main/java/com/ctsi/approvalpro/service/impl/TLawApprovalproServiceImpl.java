package com.ctsi.approvalpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.mapper.TLawApprovalproMapper;
import com.ctsi.approvalpro.service.*;
import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.approvalpro.vo.TLawApprovalpro;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproDTO;
import com.ctsi.approvalpro.vo.dto.TLawApprovalproExcel;
import com.ctsi.approvalpro.vo.dto.UTLawAppDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.leadershipEntrustment.entity.BizLeadershipEntrustment;
import com.ctsi.hndx.utils.*;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import com.ctsi.ssdc.util.RedisUtil;
import dm.jdbc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.beans.Beans;
import java.io.File;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class TLawApprovalproServiceImpl extends SysBaseServiceImpl<TLawApprovalproMapper, TLawApprovalpro> implements ITLawApprovalproService {

    @Autowired
    private TLawApprovalproMapper  mapper;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private ITLawFileCodeService tLawFileCodeService;

    @Autowired
    private ExportToExcelService exportToExcelService;

    @Autowired
    private  ITLawApprovalDraftService tLawApprovalDraftService;

    @Autowired
    private ITLawApprovalPreAuditService tLawApprovalPreAuditService;

    @Autowired
    private ITLawFileMakeDiscussionApproveService tLawFileMakeDiscussionApproveService;

    @Autowired
    private  CscpEnclosureFileService cscpEnclosureFileService;

    @Autowired
    private RedisUtil redisUtil;






    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLawApprovalproDTO createOrUpdate(TLawApprovalproDTO tLawApprovalproDTO) {
        TLawApprovalpro vo =new TLawApprovalpro();
        vo= BeanUtil.copyProperties(tLawApprovalproDTO,TLawApprovalpro.class);
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if(tLawApprovalproDTO.getId()!=null){
            TLawApprovalpro oldvo =mapper.selectById(tLawApprovalproDTO.getId());
            if(oldvo!=null){
                /*if(StringUtil.equals(oldvo.getStatus(),"3")&&!StringUtil.equals(tLawApprovalproDTO.getStatus(),"3")){
                    //如果审核通过的变成审核不通过，需要将所有的数据抖撤回 并且记录下来 调整方法另外处理
                    tLawFileCodeService.removeById(oldvo.getCodeId());
                }*/
                //如果是驳回   记录一次log是   时间问题 暂时不做  如果客户需要查询驳回记录
                 /*  if(StringUtil.equals(oldvo.getStatus(),"2")){

                }*/
                //审核通过   生成二维码  暂时二维码只放入集中立项得id喝文件名
                if(StringUtil.equals(tLawApprovalproDTO.getStatus(),"3")){
                    TLawFileCode code= tLawFileCodeService.updateFileCode(vo.getCodeId(),ProcessStatusEnums.WJQC.getCode());
                    vo.setExamineTime(LocalDateTime.now());
                    //审核通过直接推到文件起草
                    tLawApprovalDraftService.createDraft(vo);
                }
                updateById(vo);

            }else{
                if("1".equals(vo.getApprovalType())){
                    vo.setTemApprovalType("jzlx");
                    vo.setTemApprovalTypeValue("集中立项");
                }
                vo.setCreateTime(LocalDateTime.now());
                vo.setCreateBy(currentCscpUserDetail.getId());
                vo.setCreateName(currentCscpUserDetail.getRealName());
                vo.setDepartmentId(currentCscpUserDetail.getDepartmentId());
                vo.setDepartmentName(currentCscpUserDetail.getDepartmentName());
                vo.setCompanyId(currentCscpUserDetail.getCompanyId());
                vo.setCompanyName(currentCscpUserDetail.getCompanyName());
                vo.setTenantId(currentCscpUserDetail.getTenantId());
                TLawFileCode code= tLawFileCodeService.createFileCode(vo.getId(), ProcessStatusEnums.FWLX.getCode());
                vo.setCodeId(code.getId());
                save(vo);

            }
        }else{
            vo.setId(Long.valueOf(SnowflakeIdUtil.getSnowFlakeId()));
            vo.setCreateTime(LocalDateTime.now());
            vo.setCreateBy(currentCscpUserDetail.getId());
            vo.setCreateName(currentCscpUserDetail.getRealName());
            vo.setDepartmentId(currentCscpUserDetail.getDepartmentId());
            vo.setDepartmentName(currentCscpUserDetail.getDepartmentName());
            vo.setCompanyId(currentCscpUserDetail.getCompanyId());
            vo.setCompanyName(currentCscpUserDetail.getCompanyName());
            vo.setTenantId(currentCscpUserDetail.getTenantId());
            TLawFileCode code= tLawFileCodeService.createFileCode(vo.getId(), ProcessStatusEnums.FWLX.getCode());
            vo.setCodeId(code.getId());
            save(vo) ;
        }
        return BeanUtil.copyProperties(vo,TLawApprovalproDTO.class);
    }

    @Override
    public PageResult<TLawApprovalproDTO> selectListPage(TLawApprovalproDTO tLawApprovalproDTO, BasePageForm basePageForm) {
        IPage page =PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        IPage<TLawApprovalproDTO> resultpage = mapper.selectListPage(page ,tLawApprovalproDTO);
         return new PageResult<>(resultpage.getRecords(), resultpage.getTotal(), resultpage.getCurrent());

    }

    @Override
    public int delete(Long id) {
        return mapper.deleteById(id);
    }

    @Override
    public int withdraw(Long id) {
        TLawApprovalpro vo =mapper.selectById(id);
        vo.setStatus("0");
        return mapper.updateById(vo);
    }

    @Override
    public boolean selectListByCondition(TLawApprovalproDTO tLawApprovalproDTO, HttpServletResponse response) {
        List<TLawApprovalproDTO> list =mapper.selectListByCondition(tLawApprovalproDTO);
        boolean bool = false;
        try{

            List<TLawApprovalproExcel> eList = ListCopyUtil.copy(list,TLawApprovalproExcel.class);
            bool=  exportToExcelService.exportToExcel(eList,TLawApprovalproExcel.class,response);
        }catch(Exception e){
           e.printStackTrace();
        }

        return bool;

    }

    @Override
    public PageResult<TLawApprovalproDTO> selectListAllPage(TLawApprovalproDTO tLawApprovalproDTO, BasePageForm basePageForm) {
        IPage page =PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        IPage<TLawApprovalproDTO> resultpage = mapper.selectListAllPage(page ,tLawApprovalproDTO);
        return new PageResult<>(resultpage.getRecords(), resultpage.getTotal(), resultpage.getCurrent());

    }

    @Override
    public void examineBatchById(UTLawAppDTO udto) {
        for(TLawApprovalproDTO dto :udto.getList()){
            TLawApprovalpro vo =mapper.selectById(dto.getId());
            vo.setStatus(dto.getStatus());
            vo.setFileCode(udto.getFileCode());
            vo.setExamineCompanyId(udto.getExamineCompanyId());
            vo.setExamineCompanyName(udto.getExamineCompanyName());
            vo.setExamineId(udto.getExamineId());
            vo.setExamineName(udto.getExamineName());
            vo.setExaminePhone(udto.getExaminePhone());
            vo.setExamineTime(LocalDateTime.now());
            if(dto.getStatus().equals("3")){//审核通过
                //vo.setCodeId(code.getId());
                tLawApprovalDraftService.createDraft(vo);
                TLawFileCode code= tLawFileCodeService.updateFileCode(vo.getCodeId(),ProcessStatusEnums.WJQC.getCode());

            }
            updateById(vo);
        }
    }

    /**
     * 已经审核通过得立项调整为驳回   --需要删除流程中数据表的数据
     * @param pro
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLawApprovalpro retrieveByApprovalId(TLawApprovalpro pro) {

        Long approvalId = pro.getId();
        pro.setStatus("2");//将状态设置为驳回状态
        int i=tLawApprovalDraftService.removeByApprovalId(approvalId);
        if(i>0){
          int j = tLawApprovalPreAuditService.removeByApprovalId(approvalId);
            if(j>0){
               int k= tLawFileMakeDiscussionApproveService.removeByApprovalId(approvalId);
            }
        }
        //将上传的文件删除掉  除了立项的文件
        LambdaQueryWrapper<CscpEnclosureFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpEnclosureFile::getFormDataId,pro.getId());
        lambdaQueryWrapper.ne(CscpEnclosureFile::getLxType,"lxFileId");//立项本身得文件不需要删除
        List<CscpEnclosureFile> list =cscpEnclosureFileService.selectListNoAdd(lambdaQueryWrapper);

        cscpEnclosureFileService.remove(lambdaQueryWrapper);//将表数据标记为删除
        new Thread(() -> {
            for(CscpEnclosureFile cf:list){
                //删除文件用线程调用 不影响结果
                String fileName = new File(cf.getFileUrl().trim()).getName();
                redisUtil.del(RedisKeyConstant.FILE_PREVIEW_ID + fileName);
                fileStoreTemplateService.deleteFile(cf.getFileUrl());
            }
        }, "MyThread").start();
        mapper.updateById(pro);
        return pro;
    }

    @Override
    public void uploadFileName(Long id, String approvalFileName) {
        mapper.uploadFileName(id,approvalFileName);
    }
}
