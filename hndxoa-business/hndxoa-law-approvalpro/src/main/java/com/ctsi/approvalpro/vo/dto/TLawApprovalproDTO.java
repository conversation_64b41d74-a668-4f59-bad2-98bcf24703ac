package com.ctsi.approvalpro.vo.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sun.istack.NotNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "集体立项返回数据实体", description = "集体立项返回数据实体")
public class TLawApprovalproDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "起草部门名称")
    private  String  departmentName;

    @ApiModelProperty(value = "起草单位名称")
    private String  companyName;
    
    @ApiModelProperty(value = "申报的文件名")
    private String  approvalFileName;
    
    @ApiModelProperty(value = "申报联系人名称")
    
    private String  contactPerson;
    
    @ApiModelProperty(value = "申报联系人电话")
    
    private String contactPhone;
    
    @ApiModelProperty(value = "申报的发文依据")
    
    private String  fileEvidence;
    
    @ApiModelProperty(value = "申报的发文规格code")
    
    private String fileModalityType;
    
    @ApiModelProperty(value = "申报的发文规格value")
    private String  fileModalityValue;
    
    @ApiModelProperty(value = "审核部门id")
    
    private Long examineDepartmentId;
    
    @ApiModelProperty(value = "审核部门名称")
    
    private String  examineDepartmentName;
    
    @ApiModelProperty(value = "审核单位id")
    
    private Long examineCompanyId;
    
    @ApiModelProperty(value = "审核单位名称")
    
    private String examineCompanyName;
    
    @ApiModelProperty(value = "审核联系人id")
    
    private Long   examineId;
    
    @ApiModelProperty(value = "审核联系人名称")
    
    private String examineName;
    
    @ApiModelProperty(value = "暂留  如果需要修改联系人")
    
    private String examineContactPhone;
    
    @ApiModelProperty(value = "暂留  如果需要修改电话")
    
    private String examineContactPerson;
    
    @ApiModelProperty(value = "审核联系人名称")
    
    private String  examinePhone;
    
    @ApiModelProperty(value = "审核状态 0 未申报 1 已申报 2已驳回 3 审核通过")
    private String status;
    
    @ApiModelProperty(value = "是否纳入计划的建议及理由")
    
    private String  adviseAndReason;

    
    private LocalDateTime updateTime;
    
    private Long updateBy;
    private String updateName;
    
    private Long createBy;
    
    private String createName;
    
    private LocalDateTime createTime;
    
    private Long departmentId;
    
    private Long companyId;
    
    private Long tenantId;
    
    private Integer deleted;
    //查询条件
    @ApiModelProperty(value = "申报开始时间")
    
    private String startTime;
    @ApiModelProperty(value = "申报结束时间")
    
    private String  endTime;


    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "申报时间")
    private LocalDateTime declareTime;
    @ApiModelProperty(value = "发文类型1")
    
    private String fileTypeFirst;
    @ApiModelProperty(value = "发文类型2")
    
    private String fileTypeSec;
    @ApiModelProperty(value = "发文类型3")
    private String fileTypeThird;
    
    @ApiModelProperty(value = "发文类型1Value")
    private String fileTypeFirstValue;
    
    @ApiModelProperty(value = "发文类型2Value")
    private String fileTypeSecValue;
    @ApiModelProperty(value = "发文类型3Value")
    
    private String fileTypeThirdValue;
    @ApiModelProperty(value = "申报的发文规格2code")
    
    private String fileModalityTypeSec;
    @ApiModelProperty(value = "申报的发文规格2Value")
    
    private String  fileModalityTypeSecValue;
    @ApiModelProperty(value = "立项文号")
    
    private String  fileCode;

    @ApiModelProperty(value = "年度")
    private String year;

    @ApiModelProperty(value = "状态取值用于导出")
    private String statusValue;

    private String declareTimeDay;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "拟发文时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "审核时间--立项时间")
    private LocalDateTime examineTime;
    @ApiModelProperty(value = "1集中立项 2临时立项")
    private String approvalType;
    @ApiModelProperty(value = "临时立项类型ceode 数据字典 lslxlx")
    private String temApprovalType;
    @ApiModelProperty(value = "临时立项类型值 数据字典 lslxlx")
    private String temApprovalTypeValue;
    @ApiModelProperty(value = "临时立项子类型code 数据字典 临时立项类型ceode找查找字典")
    private String temApprovalTypeSec;
    @ApiModelProperty(value = "临时立项子类型code 数据字典 临时立项类型ceode找查找字典")
    private String temApprovalTypeSecValue;
    @ApiModelProperty(value = "密级")
    private String durationClassification;
    @ApiModelProperty(value = "密级值")
    private String durationClassificationName;
    @ApiModelProperty(value = "二维码id")
    private Long codeId;
    @ApiModelProperty(value = "所属序列值")
    private String fileSeqTypeValue;
    @ApiModelProperty(value = "所属序列code")
    private String fileSeqType;
    @ApiModelProperty(value = "发文目的")
    private String fileAim;

    @ApiModelProperty(value = "立项资料文件")
    private String  lxFileId;
    @ApiModelProperty(value = "立项id  ")
    private String approvalId;

    private String allFileModalityValue;

    private String currentLink;

}
