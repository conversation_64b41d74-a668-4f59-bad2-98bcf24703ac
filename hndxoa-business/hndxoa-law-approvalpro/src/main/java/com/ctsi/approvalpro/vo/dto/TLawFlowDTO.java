package com.ctsi.approvalpro.vo.dto;

import com.ctsi.business.domain.CscpAuditContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "立项流程查询返回实体", description = "立项流程返回实体")
public class TLawFlowDTO {

    //立项
    @ApiModelProperty(value = "发文立项--立项时间")
    private LocalDateTime approvalCreateTime;
    @ApiModelProperty(value = "发文立项--审核时间")
    private LocalDateTime approvalExamineTime;
    @ApiModelProperty(value = "发文立项--立项单位")
    private String approvalCompanyName;
    @ApiModelProperty(value = "发文立项--立项审核单位")
    private String approvalExamineCompanyName;
    //发文起草
    @ApiModelProperty(value = "发文起草--发文起草单位")
    private String draftCompanyName;
    @ApiModelProperty(value = "发文起草--发文起草时间")
    private LocalDateTime draftUpdateTime;
    //前置审批
    @ApiModelProperty(value = "前置审核--起草单位审核时间")
    private LocalDateTime auditTime;
    @ApiModelProperty(value = "前置审核--起草单位")
    private String auditCompanyName;
    @ApiModelProperty(value = "前置审核--党委办审核时间 根据时间大小判断 谁小谁排前面")
    private LocalDateTime dwbExaimTime;
    @ApiModelProperty(value = "前置审核--政府办审核时间")
    private LocalDateTime zfbExaimTime;
    @ApiModelProperty(value = "前置审核--党委办审核单位")
    private String dwbExaimCompanyName;
    @ApiModelProperty(value = "前置审核--政府办审核单位")
    private String zfbExaimCompanyName;


    //审议批准
    @ApiModelProperty(value = "审议批准--审议时间")
    private LocalDateTime sypzTime;
    @ApiModelProperty(value = "审议批准--审议单位")
    private String sypzCompanyName;


    //审核审发    --包括一个流程list  --包括了印刷
    @ApiModelProperty(value = "审核审发--OA单")
    private List<CscpAuditContent> shsfFlows;

    @ApiModelProperty(value = "印刷单位")
    private String printCompanyName;

    @ApiModelProperty(value = "印刷时间")
    private LocalDateTime  printTime;

    @ApiModelProperty(value = "审核审发OA 流程实例id")
    private Long printProcessInstanceId;

    //上报备案    也可能包括一个流程list
    @ApiModelProperty(value = "上报备案--OA单")
    private List<CscpAuditContent> sbbaFlows;

    //督促落实以及归档存档
    @ApiModelProperty(value = "督查发起单位")
    private String superviseCompanyName;

    @ApiModelProperty(value = "督查发起时间")
    private LocalDateTime  superviseTime;

    @ApiModelProperty(value = "归档时间")
    private LocalDateTime  placeTime;

    @ApiModelProperty(value = "归档单位")
    private String  placeCompanyName;

    //流程节点
    @ApiModelProperty(value = "流程节点")
    private String currentLink;
    @ApiModelProperty(value = "流程节点")
    private String currentLinkStatus;

    //用来做流程判断
    private LocalDateTime  printDate;

}
