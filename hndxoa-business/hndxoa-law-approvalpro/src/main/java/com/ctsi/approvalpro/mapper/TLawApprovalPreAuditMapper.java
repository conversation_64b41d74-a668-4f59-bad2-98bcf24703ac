package com.ctsi.approvalpro.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.approvalpro.vo.TLawApprovalPreAudit;
import com.ctsi.approvalpro.vo.dto.TLawApprovalPreAuditDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 法规室立项前置审核 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Mapper
public interface TLawApprovalPreAuditMapper extends MybatisBaseMapper<TLawApprovalPreAudit> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<TLawApprovalPreAuditDTO> queryListPageCondition(@Param("dto") TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO, IPage mPlusPageByBasePage);

    @InterceptorIgnore(tenantLine = "true")
    int removeByApprovalId(@Param("approvalId") Long approvalId);
}
