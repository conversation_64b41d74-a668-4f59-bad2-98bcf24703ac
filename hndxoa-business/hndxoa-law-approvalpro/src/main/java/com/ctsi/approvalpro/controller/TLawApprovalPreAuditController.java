package com.ctsi.approvalpro.controller;

import com.ctsi.approvalpro.vo.dto.TLawApprovalDraftDTO;
import com.ctsi.approvalpro.vo.dto.TLawApprovalPreAuditDTO;
import com.ctsi.approvalpro.service.ITLawApprovalPreAuditService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tLawApprovalPreAudit")
@Api(value = "法规室立项前置审核", tags = "法规室立项前置审核接口")
public class TLawApprovalPreAuditController extends BaseController {

    private static final String ENTITY_NAME = "tLawApprovalPreAudit";

    @Autowired
    private ITLawApprovalPreAuditService tLawApprovalPreAuditService;



    /**
     *  新增法规室立项前置审核批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室立项前置审核批量数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO createBatch(@RequestBody List<TLawApprovalPreAuditDTO> tLawApprovalPreAuditList) {
       Boolean  result = tLawApprovalPreAuditService.insertBatch(tLawApprovalPreAuditList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tLawApprovalPreAudit.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室立项前置审核数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO<TLawApprovalPreAuditDTO> create(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO)  {
        TLawApprovalPreAuditDTO result = tLawApprovalPreAuditService.create(tLawApprovalPreAuditDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新法规室立项前置审核数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.update')")
    public ResultVO update(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO) {
	    Assert.notNull(tLawApprovalPreAuditDTO.getId(), "general.IdNotNull");
        int count = tLawApprovalPreAuditService.update(tLawApprovalPreAuditDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除法规室立项前置审核数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tLawApprovalPreAudit.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tLawApprovalPreAuditService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO = tLawApprovalPreAuditService.findOne(id);
        return ResultVO.success(tLawApprovalPreAuditDTO);
    }

    /**
    *  分页查询多条数据.   前置审批起草单位查询数据
    */
    @GetMapping("/queryTLawApprovalPreAuditPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TLawApprovalPreAuditDTO>> queryTLawApprovalPreAuditPage(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO, BasePageForm basePageForm) {
        return ResultVO.success(tLawApprovalPreAuditService.queryListPage(tLawApprovalPreAuditDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTLawApprovalPreAudit")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TLawApprovalPreAuditDTO>> queryTLawApprovalPreAudit(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO) {
       List<TLawApprovalPreAuditDTO> list = tLawApprovalPreAuditService.queryList(tLawApprovalPreAuditDTO);
       return ResultVO.success(new ResResult<TLawApprovalPreAuditDTO>(list));
   }


    /**
     * 根据立项id查询起草数据
     */
    @GetMapping("/getByApprovalId/{approvalId}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getByApprovalId(@PathVariable Long approvalId) {
        TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO = tLawApprovalPreAuditService.findOneByApprovalId(approvalId);
        return ResultVO.success(tLawApprovalPreAuditDTO);
    }



    /**
     *  起草单位跳过操作.
     */
    @PostMapping("/skipByCreate")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过前置审核起草环节")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO skipByCreate(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        Boolean  result = tLawApprovalPreAuditService.skipByCreate(tLawApprovalPreAudit);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     *  分页查询多条数据.   前置审批起草单位查询数据
     */
    @GetMapping("/queryTLawApprovalPreAuditPageByDw")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TLawApprovalPreAuditDTO>> queryTLawApprovalPreAuditPageByDw(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO, BasePageForm basePageForm) {
        tLawApprovalPreAuditDTO.setDwbView("1");
        return ResultVO.success(tLawApprovalPreAuditService.queryListPageCondition(tLawApprovalPreAuditDTO, basePageForm));
    }


    /**
     *  分页查询多条数据.   前置审批起草单位查询数据
     */
    @GetMapping("/queryTLawApprovalPreAuditPageByZf")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TLawApprovalPreAuditDTO>> queryTLawApprovalPreAuditPageByZf(TLawApprovalPreAuditDTO tLawApprovalPreAuditDTO, BasePageForm basePageForm) {
        tLawApprovalPreAuditDTO.setZfbView("1");
        return ResultVO.success(tLawApprovalPreAuditService.queryListPageCondition(tLawApprovalPreAuditDTO, basePageForm));
    }


    /**
     *  dwb跳过操作.
     */
    @PostMapping("/skipByDw")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过前置审核起草环节")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO skipByDw(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        Boolean  result = tLawApprovalPreAuditService.skipByDw(tLawApprovalPreAudit);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     *  zfb跳过审核.
     */
    @PostMapping("/skipByZf")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过前置审核起草环节")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO skipByZf(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        Boolean  result = tLawApprovalPreAuditService.skipByZf(tLawApprovalPreAudit);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }
    /**
     * dw审核入口
     */
    @PostMapping("/exaimByDw")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过前置审核起草环节")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO exaimByDw(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        Boolean  result = tLawApprovalPreAuditService.exaimByDw(tLawApprovalPreAudit);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * dw审核入口
     */

    @PostMapping("/exaimByZf")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过前置审核起草环节")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO exaimByZf(@RequestBody TLawApprovalPreAuditDTO tLawApprovalPreAudit) {
        Boolean  result = tLawApprovalPreAuditService.exaimByZf(tLawApprovalPreAudit);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 审核通过的数据  上传资料送审议批准
     * @param dto
     * @return
     */

    @PostMapping("/exaimByCreate")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tLawApprovalPreAudit.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "跳过前置审核起草环节")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLawApprovalPreAudit.add')")
    public ResultVO exaimByCreate(@RequestBody TLawApprovalPreAuditDTO dto) {
        Boolean  result = tLawApprovalPreAuditService.exaimByCreate(dto);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }



}
