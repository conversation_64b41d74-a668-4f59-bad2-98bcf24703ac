package com.ctsi.approvalpro.service;

import com.ctsi.approvalpro.entity.dto.TLawFileReportHighDTO;
import com.ctsi.approvalpro.entity.TLawFileReportHigh;
import com.ctsi.approvalpro.entity.vo.TLawFileReportHighPageVo;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 上报备案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface ITLawFileReportHighService extends SysBaseServiceI<TLawFileReportHigh> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TLawFileReportHighPageVo> queryListPage(TLawFileReportHighDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TLawFileReportHighDTO> queryList(TLawFileReportHighDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TLawFileReportHighDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TLawFileReportHighDTO create(TLawFileReportHighDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TLawFileReportHighDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTLawFileReportHighId
     * @param code
     * @return
     */
    boolean existByTLawFileReportHighId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TLawFileReportHighDTO> dataList);


    void registerReport(TLawFileReportHighDTO tLawFileReportHighDTO);

    TLawFileReportHighDTO reportApproval(TLawFileReportHighDTO tLawFileReportHighDTO);

    void submit(TLawFileReportHighDTO tLawFileReportHighDTO);

    TLawFileReportHighDTO getByApprovalId(Long approvalId);
}
