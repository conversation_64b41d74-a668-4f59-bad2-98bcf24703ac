package com.ctsi.approvalpro.vo.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@ApiModel(value = "集体立项返回二维码数据实体", description = "集体立项返回二维码数据实体")
public class TLawFileCodeDTO extends BaseDtoEntity {

    @ApiModelProperty(value = "生成的二维码文件名称")
    private String fileName;
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;
    @ApiModelProperty(value = "关联的集中立项id")
    private Long formDataId;
    @ApiModelProperty(value = "文件扩展名")
    private String extName;
    @ApiModelProperty(value = "文件大小")
    private Integer fileSize;
    @ApiModelProperty(value = "当前环节 后续用")
    private String currentLink;
    @ApiModelProperty(value = "当前环节状态")
    private String currentLinkStatus;

    private LocalDateTime updateTime;
    private Long updateBy;
    private String updateName;
    private Long createBy;
    private String createName;
    private LocalDateTime createTime;
    private Long departmentId;
    private Long companyId;
    private Long tenantId;
    private Integer deleted;


}
