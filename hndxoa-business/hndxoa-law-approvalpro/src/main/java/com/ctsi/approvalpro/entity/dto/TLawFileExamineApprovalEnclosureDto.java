package com.ctsi.approvalpro.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TLawFileExamineApprovalEnclosureDto {
    private Long id;

    @ApiModelProperty(value = "立项Id")
    private Long approvalId;

    /**
     * 文件二维码Id
     */
    @ApiModelProperty(value = "文件二维码Id")
    private Long codeId;

    /**
     * formDataId
     */
    @ApiModelProperty(value = "formDataId")
    private Long formDataId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String title;

    /**
     * 申报的发文形式code
     */
    @ApiModelProperty(value = "申报的发文形式code")
    private String fileModalityType;

    /**
     * 申报的发文形式2code
     */
    @ApiModelProperty(value = "申报的发文形式2code")
    private String fileModalityTypeSec;

    /**
     * 申报的发文形式2value
     */
    @ApiModelProperty(value = "申报的发文形式2value")
    private String fileModalityTypeSecValue;

    /**
     * 申报的发文形式value
     */
    @ApiModelProperty(value = "申报的发文形式value")
    private String fileModalityValue;

    /**
     * 打印日期
     */
    @ApiModelProperty(value = "打印日期")
    private Date printDate;

    /**
     * 上报备案
     */
    @ApiModelProperty(value = "上报备案")
    private Integer filling;

    /**
     * 密级期限code
     */
    @ApiModelProperty(value = "密级期限code")
    private String durationClassification;

    /**
     * 密级期限名称
     */
    @ApiModelProperty(value = "密级期限名称")
    private String durationClassificationName;

    @ApiModelProperty(value = "正文id")
    private Long fileId;

    @ApiModelProperty(value = "正文名称")
    private String fileName;

    @ApiModelProperty(value = "正文路径")
    private String filePath;

    @ApiModelProperty(value = "压缩文件id")
    private Long zipFileId;

    @ApiModelProperty(value = "压缩文件名称")
    private String zipFileName;

    @ApiModelProperty(value = "压缩文件路径")
    private String zipFilePath;
}
