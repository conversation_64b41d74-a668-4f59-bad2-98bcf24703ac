package com.ctsi.approvalpro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.approvalpro.entity.TLawFileMakeDiscussionApprove;
import com.ctsi.approvalpro.entity.TLawFileReportHigh;
import com.ctsi.approvalpro.entity.TLawFileSupervision;
import com.ctsi.approvalpro.entity.dto.TLawFileExamineApprovalEnclosureDto;
import com.ctsi.approvalpro.entity.vo.LawFileMakeDiscussApproveDetail;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintDetail;
import com.ctsi.approvalpro.entity.vo.TLawFileExaminePrintPageVo;
import com.ctsi.approvalpro.enums.ProcessStatusEnums;
import com.ctsi.approvalpro.mapper.*;
import com.ctsi.approvalpro.vo.TLawApprovalDraft;
import com.ctsi.approvalpro.vo.TLawFileCode;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.common.utils.BeanUtil;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpDocumentFileMapper;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.approvalpro.entity.TLawFileExaminePrint;
import com.ctsi.approvalpro.entity.dto.TLawFileExaminePrintDTO;
import com.ctsi.approvalpro.service.ITLawFileExaminePrintService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <p>
 * 审核审发及印制发布表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Slf4j
@Service
public class TLawFileExaminePrintServiceImpl extends SysBaseServiceImpl<TLawFileExaminePrintMapper, TLawFileExaminePrint> implements ITLawFileExaminePrintService {

    @Autowired
    private TLawFileExaminePrintMapper tLawFileExaminePrintMapper;

    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;

    @Autowired
    private TLawApprovalDraftMapper approvalDraftMapper;

    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileMapper;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;


    @Autowired
    private CscpDocumentFileMapper cscpDocumentFileMapper;

    @Autowired
    private TLawFileCodeMapper tLawFileCodeMapper;

    @Autowired
    private TLawFileReportHighMapper tLawFileReportHighMapper;

    @Autowired
    private TLawFileSupervisionMapper tLawFileSupervisionMapper;



    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TLawFileExaminePrintPageVo> queryListPage(TLawFileExaminePrintDTO entityDTO, BasePageForm basePageForm) {
        List<TLawFileExaminePrintPageVo> pageVoList = new ArrayList<>();

        Integer count = tLawFileExaminePrintMapper.queryListPageCount(entityDTO);
        Integer currentPage = basePageForm.getCurrentPage();
        Integer pageSize = basePageForm.getPageSize();
        Integer statrIndex = (currentPage - 1)*pageSize;

        int loop = 0;
        while(statrIndex > count){
            statrIndex = (currentPage - 1 - loop)*pageSize;
            loop++;
            if(statrIndex <= 0){
                break;
            }
        }

        List<TLawFileExaminePrintPageVo> fileExaminePrintList = tLawFileExaminePrintMapper.queryListPage(entityDTO,statrIndex,pageSize);

        List<TLawFileExaminePrintPageVo> sortedList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fileExaminePrintList)){

            for (int i = 0; i < fileExaminePrintList.size(); i++) {
                TLawFileExaminePrintPageVo printPageVo1 = fileExaminePrintList.get(i);
                if(ObjectUtil.isNotNull(printPageVo1.getProcessInstanceId())){
                    LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper=new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,printPageVo1.getProcessInstanceId());
                    ProcessAssignee processAssignee = processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
                    if(processAssignee!=null) {
                        printPageVo1.setCurrentStage(processAssignee.getNodeName());
                        printPageVo1.setCurrentUserName(processAssignee.getAssigneeName());
                    }
                }
                //pageVoList.add(printPageVo);
            }

//            Comparator<TLawFileExaminePrintPageVo> comparing = Comparator.
//                    comparing(TLawFileExaminePrintPageVo::getBpmStatus, Comparator.nullsFirst(Comparator.naturalOrder()));
//
//            sortedList = pageVoList.stream().sorted(comparing).collect(Collectors.toList());
        }
        return new PageResult<TLawFileExaminePrintPageVo>(fileExaminePrintList,
                count, currentPage);
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TLawFileExaminePrintDTO> queryList(TLawFileExaminePrintDTO entityDTO) {
        LambdaQueryWrapper<TLawFileExaminePrint> queryWrapper = new LambdaQueryWrapper();
            List<TLawFileExaminePrint> listData = tLawFileExaminePrintMapper.selectList(queryWrapper);
            List<TLawFileExaminePrintDTO> TLawFileExaminePrintDTOList = ListCopyUtil.copy(listData, TLawFileExaminePrintDTO.class);
        return TLawFileExaminePrintDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TLawFileExaminePrintDTO findOne(Long id) {
        TLawFileExaminePrint  tLawFileExaminePrint =  tLawFileExaminePrintMapper.selectById(id);
        TLawFileExaminePrintDTO tLawFileExaminePrintDTO = BeanConvertUtils.copyProperties(tLawFileExaminePrint, TLawFileExaminePrintDTO.class);
//        if(ObjectUtil.isNotNull(tLawFileExaminePrint.getProcessInstanceId())){
//            TaskVO processInfoByInstanceId = cscpProcBaseRepository.getProcessInfoByInstanceId(tLawFileExaminePrint.getProcessInstanceId().toString());
//            tLawFileExaminePrintDTO.setTaskVO(processInfoByInstanceId);
//        }
        return tLawFileExaminePrintDTO;
    }

    /**
     * 查询单条数据.



    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TLawFileExaminePrintDTO create(TLawFileExaminePrintDTO entityDTO) {
       TLawFileExaminePrint tLawFileExaminePrint =  BeanConvertUtils.copyProperties(entityDTO,TLawFileExaminePrint.class);
        save(tLawFileExaminePrint);
        return  BeanConvertUtils.copyProperties(tLawFileExaminePrint,TLawFileExaminePrintDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TLawFileExaminePrintDTO entity) {
        TLawFileExaminePrint tLawFileExaminePrint = BeanConvertUtils.copyProperties(entity,TLawFileExaminePrint.class);
        return tLawFileExaminePrintMapper.updateById(tLawFileExaminePrint);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tLawFileExaminePrintMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TLawFileExaminePrintId
     * @return
     */
    @Override
    public boolean existByTLawFileExaminePrintId(Long TLawFileExaminePrintId) {
        if (TLawFileExaminePrintId != null) {
            LambdaQueryWrapper<TLawFileExaminePrint> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TLawFileExaminePrint::getId, TLawFileExaminePrintId);
            List<TLawFileExaminePrint> result = tLawFileExaminePrintMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TLawFileExaminePrintDTO> dataList) {
        List<TLawFileExaminePrint> result = ListCopyUtil.copy(dataList, TLawFileExaminePrint.class);
        return saveBatch(result);
    }

    @Override
    @Transactional
    public TLawFileExamineApprovalEnclosureDto tranferApprovalEnclosure(TLawFileExaminePrintDTO tLawFileExaminePrintDTO) {
        TLawFileExamineApprovalEnclosureDto examineApprovalEnclosureDto = new TLawFileExamineApprovalEnclosureDto();

        TLawFileExaminePrint fileExaminePrint = tLawFileExaminePrintMapper.selectById(tLawFileExaminePrintDTO.getId());
        BeanUtils.copyProperties(fileExaminePrint,examineApprovalEnclosureDto);

        Long formDataId = Long.valueOf(SnowflakeIdUtil.getSnowFlakeId());


        Long approvalproId = tLawFileExaminePrintDTO.getApprovalId();
        LambdaQueryWrapper<CscpEnclosureFile> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpEnclosureFile::getFormDataId,approvalproId);
        List<CscpEnclosureFile> enclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(lambdaQueryWrapper);
        if(CollectionUtils.isNotEmpty(enclosureFiles)){
        //正文文件
        List<CscpEnclosureFile> documentFileList = enclosureFiles.stream().
                    filter(e -> "documentFileId".equals(e.getLxType())).collect(Collectors.toList());
            //复制到正文表
            if(CollectionUtils.isNotEmpty(documentFileList)){
                CscpEnclosureFile cscpEnclosureFile = documentFileList.get(0);
                CscpDocumentFile cscpDocumentFile = new CscpDocumentFile();
                BeanUtils.copyProperties(cscpEnclosureFile, cscpDocumentFile);
                cscpDocumentFile.setId(null);
                cscpDocumentFile.setFormDataId(formDataId);
                cscpDocumentFileMapper.insert(cscpDocumentFile);
            }
        }

        //其它文件
        List<CscpEnclosureFile> otherFileList = enclosureFiles.stream().
                    filter(e -> ObjectUtil.isNotNull(e.getLxType()) && !"documentFileId".equals(e.getLxType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(otherFileList)){
            try{
                CscpEnclosureFile zipFileMain = cscpEnclosureFileService.zip(otherFileList,"附件",formDataId);
                log.info("zipFileMain 上传成功");
            }catch (Exception e){
                log.error("zipFileMain 上传失败 {}",e.getMessage());
            }
        }

        examineApprovalEnclosureDto.setFormDataId(formDataId);

        //formDataId设置到审批流程中
        fileExaminePrint.setFormDataId(formDataId);
        tLawFileExaminePrintMapper.updateById(fileExaminePrint);


        //修改主流程状态
        TLawFileCode tLawFileCode = tLawFileCodeMapper.selectById(fileExaminePrint.getCodeId());
        tLawFileCode.setCurrentLink(ProcessStatusEnums.YZFB.getCode());
        tLawFileCodeMapper.updateById(tLawFileCode);


        return examineApprovalEnclosureDto;
    }

    @Override
    @Transactional
    public Boolean reportToHigh(TLawFileExaminePrintDTO tLawFileExaminePrintDTO) {

        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        String companyName = currentCscpUserDetail.getCompanyName();


        TLawFileExaminePrint fileExaminePrint = tLawFileExaminePrintMapper.selectById(tLawFileExaminePrintDTO.getId());
        fileExaminePrint.setFilling(tLawFileExaminePrintDTO.getFilling());
        fileExaminePrint.setPrintDate(tLawFileExaminePrintDTO.getPrintDate());
        fileExaminePrint.setCompanyName(companyName);
        tLawFileExaminePrintMapper.updateById(fileExaminePrint);

        Boolean result = Boolean.TRUE;

        if(ObjectUtil.isNotNull(tLawFileExaminePrintDTO.getFilling()) && tLawFileExaminePrintDTO.getFilling()  == 1){
            //上报备案
            //1.生成上报备案数据
            TLawFileReportHigh reportHigh = new TLawFileReportHigh();
            reportHigh.setCodeId(fileExaminePrint.getCodeId());
            reportHigh.setApprovalId(fileExaminePrint.getApprovalId());
            reportHigh.setTitle(fileExaminePrint.getTitle());
            reportHigh.setDraftDepartmentId(fileExaminePrint.getDraftDepartmentId());
            reportHigh.setDraftCompanyId(fileExaminePrint.getDraftCompanyId());
            reportHigh.setDurationClassification(fileExaminePrint.getDurationClassification());
            reportHigh.setDurationClassificationName(fileExaminePrint.getDurationClassificationName());
            reportHigh.setPrintDate(tLawFileExaminePrintDTO.getPrintDate());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(tLawFileExaminePrintDTO.getPrintDate());
            // 在当前日期基础上加30天
            calendar.add(Calendar.DAY_OF_MONTH, 30);
            reportHigh.setEndDate(calendar.getTime());

            tLawFileReportHighMapper.insert(reportHigh);
            //2.更新主流程状态
            TLawFileCode tLawFileCode = tLawFileCodeMapper.selectById(fileExaminePrint.getCodeId());
            tLawFileCode.setCurrentLink(ProcessStatusEnums.SBBA.getCode());
            tLawFileCodeMapper.updateById(tLawFileCode);

        }else{
           //直接转督查
            TLawFileSupervision tLawFileSupervision = new TLawFileSupervision();
            tLawFileSupervision.setCodeId(fileExaminePrint.getCodeId());
            tLawFileSupervision.setApprovalId(fileExaminePrint.getApprovalId());
            tLawFileSupervision.setTitle(fileExaminePrint.getTitle());
            tLawFileSupervision.setDurationClassification(fileExaminePrint.getDurationClassification());
            tLawFileSupervision.setDurationClassificationName(fileExaminePrint.getDurationClassificationName());
            tLawFileSupervision.setReportDate(new Date());
            tLawFileSupervision.setSupervisionCompanyName(companyName);
            tLawFileSupervisionMapper.insert(tLawFileSupervision);

            //2.更新主流程状态
            TLawFileCode tLawFileCode = tLawFileCodeMapper.selectById(fileExaminePrint.getCodeId());
            tLawFileCode.setCurrentLink(ProcessStatusEnums.DCLS.getCode());
            tLawFileCodeMapper.updateById(tLawFileCode);
        }
        return Boolean.TRUE;
    }

    @Override
    public TLawFileExaminePrintDetail getByApprovalId(Long approvalId) {
        TLawFileExaminePrintDetail detail = new TLawFileExaminePrintDetail();

        LambdaQueryWrapper<TLawFileExaminePrint> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(TLawFileExaminePrint::getApprovalId, approvalId);
        TLawFileExaminePrint fileExaminePrint = tLawFileExaminePrintMapper.selectOneNoAdd(lambdaQueryWrapper);

        BeanUtils.copyProperties(fileExaminePrint,detail);

        if(ObjectUtil.isNotNull(fileExaminePrint.getProcessInstanceId())){
            TaskVO processInfoByInstanceId = cscpProcBaseRepository.getProcessInfoByInstanceId(fileExaminePrint.getProcessInstanceId().toString());
            detail.setTaskVO(processInfoByInstanceId);
        }
        return detail;
    }

}
