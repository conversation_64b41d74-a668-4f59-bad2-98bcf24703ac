package com.ctsi.regulations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsExaminationProcess;
import com.ctsi.regulations.entity.TRegulationsSolicitOpinions;
import com.ctsi.regulations.entity.dto.TRegulationsSolicitOpinionsDTO;
import com.ctsi.regulations.mapper.TRegulationsExaminationMapper;
import com.ctsi.regulations.mapper.TRegulationsSolicitOpinionsMapper;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.regulations.service.ITRegulationsSolicitOpinionsService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 法规室征求意见记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
public class TRegulationsSolicitOpinionsServiceImpl extends SysBaseServiceImpl<TRegulationsSolicitOpinionsMapper, TRegulationsSolicitOpinions> implements ITRegulationsSolicitOpinionsService {

    @Autowired
    private TRegulationsSolicitOpinionsMapper tRegulationsSolicitOpinionsMapper;

    @Autowired
    private TRegulationsExaminationMapper regulationsExaminationMapper;

    @Autowired
    private ITRegulationsExaminationProcessService regulationsExaminationProcessService;
    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TRegulationsSolicitOpinionsDTO> queryListPage(TRegulationsSolicitOpinionsDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TRegulationsSolicitOpinions> queryWrapper = new LambdaQueryWrapper();
        //只能看自己单位的
        Long companyId = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();

        if(StringUtils.isNotEmpty(entityDTO.getFileName())) {
            queryWrapper.like(TRegulationsSolicitOpinions::getFileName, entityDTO.getFileName());
        }
        queryWrapper.orderByDesc(TRegulationsSolicitOpinions::getReportingTime);
        if(entityDTO.getStartTimeBB()!=null&&entityDTO.getEndTimeBB()!=null){
            queryWrapper.ge(TRegulationsSolicitOpinions::getReportingTime,entityDTO.getStartTimeBB());
            queryWrapper.le(TRegulationsSolicitOpinions::getReportingTime,entityDTO.getEndTimeBB());
            queryWrapper.orderByDesc(TRegulationsSolicitOpinions::getReportingTime);
        }

        if(entityDTO.getStartTimeFK()!=null&&entityDTO.getEndTimeFK()!=null){
            queryWrapper.ge(TRegulationsSolicitOpinions::getFeedbackTime,entityDTO.getStartTimeFK());
            queryWrapper.le(TRegulationsSolicitOpinions::getFeedbackTime,entityDTO.getEndTimeFK());
            queryWrapper.orderByDesc(TRegulationsSolicitOpinions::getFeedbackTime);
        }
        queryWrapper.and(qw -> qw.eq(TRegulationsSolicitOpinions::getSolicitCompanyId, SecurityUtils.getCurrentCscpUserDetail().getDepartmentId())
                .or()
                .eq(TRegulationsSolicitOpinions::getSolicitCompanyId, companyId));
        queryWrapper.orderByDesc(TRegulationsSolicitOpinions::getFeedbackTime);
        IPage<TRegulationsSolicitOpinions> pageData = tRegulationsSolicitOpinionsMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TRegulationsSolicitOpinionsDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsSolicitOpinionsDTO.class));

        return new PageResult<TRegulationsSolicitOpinionsDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TRegulationsSolicitOpinionsDTO> queryList(TRegulationsSolicitOpinionsDTO entityDTO) {
        LambdaQueryWrapper<TRegulationsSolicitOpinions> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TRegulationsSolicitOpinions::getExaminationId,entityDTO.getExaminationId());
        queryWrapper.orderByDesc(TRegulationsSolicitOpinions::getCreateTime);
            List<TRegulationsSolicitOpinions> listData = tRegulationsSolicitOpinionsMapper.selectListNoAdd(queryWrapper);
            List<TRegulationsSolicitOpinionsDTO> TRegulationsSolicitOpinionsDTOList = ListCopyUtil.copy(listData, TRegulationsSolicitOpinionsDTO.class);
        return TRegulationsSolicitOpinionsDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TRegulationsSolicitOpinionsDTO findOne(Long id) {
        TRegulationsSolicitOpinions  tRegulationsSolicitOpinions =  tRegulationsSolicitOpinionsMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tRegulationsSolicitOpinions,TRegulationsSolicitOpinionsDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TRegulationsSolicitOpinionsDTO create(TRegulationsSolicitOpinionsDTO entityDTO) {
       TRegulationsSolicitOpinions tRegulationsSolicitOpinions =  BeanConvertUtils.copyProperties(entityDTO,TRegulationsSolicitOpinions.class);
        save(tRegulationsSolicitOpinions);
        //更新到法规室审查表
        TRegulationsExamination tRegulationsExamination = regulationsExaminationMapper.selectById(tRegulationsSolicitOpinions.getExaminationId());
        tRegulationsExamination.setCurrentStage("E");
        if(StringUtils.isNotBlank(tRegulationsExamination.getSolicitOpinionsId())){
            List<String> list = Arrays.asList(tRegulationsExamination.getProblemDescriptionId().split(","));
            list.add(tRegulationsExamination.getProblemDescriptionId());
            tRegulationsExamination.setSolicitOpinionsId(org.apache.commons.lang3.StringUtils.strip(list.toString(),"[]").replaceAll(" ",""));
        }else{
            tRegulationsExamination.setSolicitOpinionsId(String.valueOf(tRegulationsSolicitOpinions.getId()));
        }
        regulationsExaminationMapper.updateById(tRegulationsExamination);
        return  BeanConvertUtils.copyProperties(tRegulationsSolicitOpinions,TRegulationsSolicitOpinionsDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TRegulationsSolicitOpinionsDTO entity) {
        TRegulationsSolicitOpinions tRegulationsSolicitOpinions = BeanConvertUtils.copyProperties(entity,TRegulationsSolicitOpinions.class);
        tRegulationsSolicitOpinions.setIsRead(0);
        TRegulationsExamination tRegulationsExamination = regulationsExaminationMapper.selectById(tRegulationsSolicitOpinions.getExaminationId());
        int indexOf = tRegulationsSolicitOpinionsMapper.updateById(tRegulationsSolicitOpinions);
        //检查征求意见是否都回复了
        TRegulationsSolicitOpinionsDTO query=new TRegulationsSolicitOpinionsDTO();
        query.setExaminationId(entity.getExaminationId());
        List<TRegulationsSolicitOpinionsDTO> regulationsSolicitOpinions = this.queryList(query);
        boolean allEnd=true;
        for (TRegulationsSolicitOpinionsDTO regulationsSolicitOpinionsDTO : regulationsSolicitOpinions) {
            if(regulationsSolicitOpinionsDTO.getFeedbackTime()==null){
                allEnd=false;
                break;
            }
        }
        if(allEnd==true){
            regulationsExaminationProcessService.updateStatus(tRegulationsExamination, "E", 3, null);
        }else {
            regulationsExaminationProcessService.updateStatus(tRegulationsExamination, "E", 2, null, tRegulationsSolicitOpinions.getId(), "2");
        }

        return indexOf;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tRegulationsSolicitOpinionsMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TRegulationsSolicitOpinionsId
     * @return
     */
    @Override
    public boolean existByTRegulationsSolicitOpinionsId(Long TRegulationsSolicitOpinionsId) {
        if (TRegulationsSolicitOpinionsId != null) {
            LambdaQueryWrapper<TRegulationsSolicitOpinions> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TRegulationsSolicitOpinions::getId, TRegulationsSolicitOpinionsId);
            List<TRegulationsSolicitOpinions> result = tRegulationsSolicitOpinionsMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TRegulationsSolicitOpinionsDTO> dataList) {
        List<TRegulationsSolicitOpinions> result = ListCopyUtil.copy(dataList, TRegulationsSolicitOpinions.class);
        return saveBatch(result);
    }

    @Override
    public Integer updateIsRead(Long examinationId) {
        LambdaQueryWrapper<TRegulationsSolicitOpinions> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TRegulationsSolicitOpinions::getExaminationId,examinationId);
        lambdaQueryWrapper.eq(TRegulationsSolicitOpinions::getIsRead,0);
        List<TRegulationsSolicitOpinions> regulationsSolicitOpinions = this.baseMapper.selectListNoAdd(lambdaQueryWrapper);
        for (TRegulationsSolicitOpinions regulationsSolicitOpinion : regulationsSolicitOpinions) {
            regulationsSolicitOpinion.setIsRead(1);
        }
        updateBatchById(regulationsSolicitOpinions);
        if(!regulationsSolicitOpinions.isEmpty()) {
            return regulationsSolicitOpinions.size();
        }else {
            return 0;
        }
    }

    @Override
    public Boolean createBatchFromOneData(TRegulationsSolicitOpinionsDTO regulationsSolicitOpinionsDTO) {
        List<TRegulationsSolicitOpinionsDTO> list=new ArrayList<>();

        //下载呈批件的正文
        if(regulationsSolicitOpinionsDTO.getTaskVO()!=null){
            CscpDocumentFile cscpDocumentFile = cscpDocumentFileService.GetCscpDocumentFile(Long.valueOf(regulationsSolicitOpinionsDTO.getTaskVO().getFormDataId()));
            if(cscpDocumentFile!=null) {
                regulationsSolicitOpinionsDTO.setCommentLetterFileId(cscpDocumentFile.getFormDataId());
                regulationsSolicitOpinionsDTO.setCommentLetterFileName(cscpDocumentFile.getFileName());
                regulationsSolicitOpinionsDTO.setCommentLetterFilePath(cscpDocumentFile.getFileUrl());
            }
        }

        for (Long id : regulationsSolicitOpinionsDTO.getSolicitCompanyIdList()) {
            TRegulationsSolicitOpinionsDTO tRegulationsSolicitOpinionsDTO = BeanConvertUtils.copyProperties(regulationsSolicitOpinionsDTO, TRegulationsSolicitOpinionsDTO.class);
            tRegulationsSolicitOpinionsDTO.setSolicitCompanyId(id);
            int indexOf = tRegulationsSolicitOpinionsDTO.getSolicitCompanyIdList().indexOf(id);
            tRegulationsSolicitOpinionsDTO.setSolicitCompanyName(tRegulationsSolicitOpinionsDTO.getSolicitCompanyNameList().get(indexOf));
            tRegulationsSolicitOpinionsDTO.setIsRead(1);
            list.add(tRegulationsSolicitOpinionsDTO);
        }


        List<TRegulationsSolicitOpinions> result = ListCopyUtil.copy(list, TRegulationsSolicitOpinions.class);
        boolean saveBatch = saveBatch(result);
        //新增同样数量的流程节点，标识创建时间
        List<TRegulationsExaminationProcess> regulationsExaminationProcess = regulationsExaminationProcessService.selectBtExaminationIdNode(result.get(0).getExaminationId(), "E",null);
        LocalDateTime now = LocalDateTime.now();
        List<TRegulationsExaminationProcess> saveList=new ArrayList<>();
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        for (TRegulationsSolicitOpinions tRegulationsSolicitOpinions : result) {
            TRegulationsExaminationProcess regulationsExaminationProcess1 = BeanConvertUtils.copyProperties(tRegulationsSolicitOpinions, TRegulationsExaminationProcess.class);
            regulationsExaminationProcess1.setId(null);
            regulationsExaminationProcess1.setNodeCompanyName(companyName);
            regulationsExaminationProcess1.setNodeUpdateTime(now);
            regulationsExaminationProcess1.setNodeStatus(2);
            regulationsExaminationProcess1.setBusinessId(tRegulationsSolicitOpinions.getId());
            regulationsExaminationProcess1.setNode("E");
            regulationsExaminationProcess1.setRemark("1");
            saveList.add(regulationsExaminationProcess1);
            break;
        }
        //新增同样数量的流程节点，带BusinessId，标识回复时间
        for (TRegulationsSolicitOpinions tRegulationsSolicitOpinions : result) {
            TRegulationsExaminationProcess regulationsExaminationProcess1 = BeanConvertUtils.copyProperties(tRegulationsSolicitOpinions, TRegulationsExaminationProcess.class);
            regulationsExaminationProcess1.setId(null);
            regulationsExaminationProcess1.setNodeCompanyName(tRegulationsSolicitOpinions.getSolicitCompanyName());
//            regulationsExaminationProcess1.setNodeUpdateTime();
            regulationsExaminationProcess1.setNodeStatus(2);
            regulationsExaminationProcess1.setNode("E");
            regulationsExaminationProcess1.setBusinessId(tRegulationsSolicitOpinions.getId());
            regulationsExaminationProcess1.setRemark("2");
            saveList.add(regulationsExaminationProcess1);
        }

        for (TRegulationsExaminationProcess examinationProcess : regulationsExaminationProcess) {
            regulationsExaminationProcessService.removeById(examinationProcess);
        }
        regulationsExaminationProcessService.saveBatch(saveList);


        List<Long> idList=new ArrayList<>();
        for (TRegulationsSolicitOpinions tRegulationsSolicitOpinions : result) {
            idList.add(tRegulationsSolicitOpinions.getId());
        }
        //更新到法规室审查表
        TRegulationsExamination tRegulationsExamination = regulationsExaminationMapper.selectById(regulationsSolicitOpinionsDTO.getExaminationId());
        tRegulationsExamination.setCurrentStage("E");
        tRegulationsExamination.setSolicitOpinionsId(org.apache.commons.lang3.StringUtils.strip(idList.toString(),"[]").replaceAll(" ",""));
        regulationsExaminationMapper.updateById(tRegulationsExamination);

        return saveBatch;
    }


}
