package com.ctsi.regulations.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.regulations.entity.dto.TRegulationsSolicitOpinionsDTO;
import com.ctsi.regulations.service.ITRegulationsSolicitOpinionsService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tRegulationsSolicitOpinions")
@Api(value = "法规室征求意见记录表", tags = "法规室征求意见记录表接口")
public class TRegulationsSolicitOpinionsController extends BaseController {

    private static final String ENTITY_NAME = "tRegulationsSolicitOpinions";

    @Autowired
    private ITRegulationsSolicitOpinionsService tRegulationsSolicitOpinionsService;



    /**
     *  新增法规室征求意见记录表批量数据.
     */
    @PostMapping("/createBatchFromOneData")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsSolicitOpinions.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室征求意见记录表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsSolicitOpinions.add')")
    public ResultVO createBatchFromOneData(@RequestBody TRegulationsSolicitOpinionsDTO regulationsSolicitOpinionsDTO) {
        Boolean  result = tRegulationsSolicitOpinionsService.createBatchFromOneData(regulationsSolicitOpinionsDTO);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     *  新增法规室征求意见记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsSolicitOpinions.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室征求意见记录表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsSolicitOpinions.add')")
    public ResultVO createBatch(@RequestBody List<TRegulationsSolicitOpinionsDTO> tRegulationsSolicitOpinionsList) {
       Boolean  result = tRegulationsSolicitOpinionsService.insertBatch(tRegulationsSolicitOpinionsList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tRegulationsSolicitOpinions.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室征求意见记录表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsSolicitOpinions.add')")
    public ResultVO<TRegulationsSolicitOpinionsDTO> create(@RequestBody TRegulationsSolicitOpinionsDTO tRegulationsSolicitOpinionsDTO)  {
        TRegulationsSolicitOpinionsDTO result = tRegulationsSolicitOpinionsService.create(tRegulationsSolicitOpinionsDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsSolicitOpinions.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新法规室征求意见记录表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsSolicitOpinions.update')")
    public ResultVO update(@RequestBody TRegulationsSolicitOpinionsDTO tRegulationsSolicitOpinionsDTO) {
	    Assert.notNull(tRegulationsSolicitOpinionsDTO.getId(), "general.IdNotNull");
        int count = tRegulationsSolicitOpinionsService.update(tRegulationsSolicitOpinionsDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除法规室征求意见记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tRegulationsSolicitOpinions.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsSolicitOpinions.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tRegulationsSolicitOpinionsService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TRegulationsSolicitOpinionsDTO tRegulationsSolicitOpinionsDTO = tRegulationsSolicitOpinionsService.findOne(id);
        return ResultVO.success(tRegulationsSolicitOpinionsDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @PostMapping("/queryTRegulationsSolicitOpinionsPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsSolicitOpinionsDTO>> queryTRegulationsSolicitOpinionsPage(@RequestBody TRegulationsSolicitOpinionsDTO tRegulationsSolicitOpinionsDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsSolicitOpinionsService.queryListPage(tRegulationsSolicitOpinionsDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @PostMapping("/queryTRegulationsSolicitOpinions")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TRegulationsSolicitOpinionsDTO>> queryTRegulationsSolicitOpinions(@RequestBody TRegulationsSolicitOpinionsDTO tRegulationsSolicitOpinionsDTO) {
       List<TRegulationsSolicitOpinionsDTO> list = tRegulationsSolicitOpinionsService.queryList(tRegulationsSolicitOpinionsDTO);
       return ResultVO.success(new ResResult<TRegulationsSolicitOpinionsDTO>(list));
   }


    /**
     * 更新为已读
     */
    @GetMapping("/updateIsRead")
    @ApiOperation(value = "更新为已读", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Integer> updateIsRead(Long examinationId) {
        Integer count = tRegulationsSolicitOpinionsService.updateIsRead(examinationId);
        return ResultVO.success(count);
    }
}
