package com.ctsi.regulations.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import com.ctsi.ssdc.model.PageResult;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 法规室审查表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface ITRegulationsExaminationService extends SysBaseServiceI<TRegulationsExamination> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsExaminationDTO> queryListPage(TRegulationsExaminationDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsExaminationDTO> queryList(TRegulationsExaminationDTO entity);


    /**
     * 获取List不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsExaminationDTO> queryListByIdList(List<Long> idList);


    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TRegulationsExaminationDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TRegulationsExaminationDTO create(TRegulationsExaminationDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TRegulationsExaminationDTO entity);

    int updateNoProcess(TRegulationsExaminationDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTRegulationsExaminationId
     * @param code
     * @return
     */
    boolean existByTRegulationsExaminationId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TRegulationsExaminationDTO> dataList);


    /**
     * 下载模板
     */
    public void downloadTemplate(HttpServletResponse response,Integer type, Map<String, String> placeholders,String filePath,String filename)throws IOException;


    /**
     * 科创推送数据,创建备案信息
     * @param babgFile     备案报告
     * @param basmFile     备案说明
     * @param zswb         正式文本
     * @param paramJsonStr TRegulationsExamination  json
     */
	void create(String paramJsonStr , MultipartFile[] babgFile , MultipartFile[] basmFile , MultipartFile[] zswb);



    public XWPFDocument generateDocx(InputStream templateInputStream, Map<String, String> placeholders);


	// 附件绑定
    List<CscpEnclosureFile> annexHandler(MultipartFile[] zswb, Long formDataId,Integer secrecyGrade);
}
