package com.ctsi.regulations.service;

import com.ctsi.regulations.entity.dto.TRegulationsProblemDescriptionDTO;
import com.ctsi.regulations.entity.TRegulationsProblemDescription;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 问题说明表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
public interface ITRegulationsProblemDescriptionService extends SysBaseServiceI<TRegulationsProblemDescription> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsProblemDescriptionDTO> queryListPage(TRegulationsProblemDescriptionDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsProblemDescriptionDTO> queryList(TRegulationsProblemDescriptionDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TRegulationsProblemDescriptionDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TRegulationsProblemDescriptionDTO create(TRegulationsProblemDescriptionDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TRegulationsProblemDescriptionDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTRegulationsProblemDescriptionId
     * @param code
     * @return
     */
    boolean existByTRegulationsProblemDescriptionId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TRegulationsProblemDescriptionDTO> dataList);


    /**
     * 更新为已读
     * @param examinationId
     * @return
     */
    Integer updateIsRead(Long examinationId);

    /**
     * 科创推送数据,问题说明接收
     * @param paramJsonStr 内容
     * @param files 附件资料
     */
    void recProblemFeed(String paramJsonStr , MultipartFile[] files);
}
