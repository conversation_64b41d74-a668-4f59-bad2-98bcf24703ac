package com.ctsi.regulations.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * 法规室审查表流程记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TRegulationsExaminationProcessDTO对象", description="法规室审查表流程记录表")
public class TRegulationsExaminationProcessDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "命名：起草单位+文件名称+文件制定")
    private String name;

    /**
     * 法规室审查表ID
     */
    @ApiModelProperty(value = "法规室审查表ID")
    private Long examinationId;

    /**
     * 节点
     */
    @ApiModelProperty(value = "节点")
    private String node;

    /**
     * 节点状态
     */
    @ApiModelProperty(value = "节点状态，1未完成，2进行中，3已完成")
    private Integer nodeStatus;

    /**
     * 节点单位名称
     */
    @ApiModelProperty(value = "节点单位名称")
    private String nodeCompanyName;

    /**
     * 节点单位ID
     */
    @ApiModelProperty(value = "节点单位ID")
    private Long nodeCompanyId;

    /**
     * 节点更细你时间
     */
    @ApiModelProperty(value = "节点更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nodeUpdateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     *
     */
    @ApiModelProperty(value = "对应消息类型的主键ID，例如node为E，则为TRegulationsProblemDescription的主键ID")
    private Long businessId;
}
