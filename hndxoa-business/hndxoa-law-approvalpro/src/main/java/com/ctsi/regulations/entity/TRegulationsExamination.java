package com.ctsi.regulations.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 法规室审查表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_regulations_examination")
@ApiModel(value="TRegulationsExamination对象", description="法规室审查表")
public class TRegulationsExamination extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 外系统唯一id,通讯识别id
     */
    @ApiModelProperty(value = "外系统唯一id")
    private String recordId;

    /**
     * 只入库,暂时不参与任何 业务
     * (文件密级,默认是1未标注)可选固定值1.未标注 2.公开 3.内部 4.秘密 5.机密 6.涉密 7.明电;传数字
     */
    @ApiModelProperty(value = "文件密级")
    private  Integer secrecyGrade;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 报备单位名称
     */
    @ApiModelProperty(value = "报备单位名称")
    private String reportingUnitName;

    /**
     * 报备单位ID
     */
    @ApiModelProperty(value = "报备单位ID")
    private Long reportingUnitId;

    /**
     * 报备时间
     */
    @ApiModelProperty(value = "报备时间")
    private LocalDateTime reportingTime;


    @ApiModelProperty(value = "登记单位名称")
    private String registerUnitName;

    @ApiModelProperty(value = "登记时间")
    private LocalDateTime registerTime;


    @ApiModelProperty(value = "登记确认单位名称")
    private String registerComfirmUnitName;

    @ApiModelProperty(value = "登记确认时间")
    private LocalDateTime registerComfirmTime;



    /**
     * 审查意见
     * 0 初审中
     */
    @ApiModelProperty(value = "审查意见 1直接予以备案通过、2予以备案通过并提出建议、3予以备案通过并告知、4予以备案通过并出面提醒、5纠正、6对主动纠正后重报件予以备案通过，7其他")
    private Integer reviewOpinions;

    /**
     * 其他审查结果备注
     */
    @ApiModelProperty(value = "其他审查结果备注")
    private String otherReviewResults;



    /**
     * 当前状态
     */
    @ApiModelProperty(value = "流程当前状态")
    private String currentStage;

    /**
     * 需要跳过的状态
     */
    @ApiModelProperty(value = "流程需要跳过的状态")
    private String skipStatus;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime releaseTime;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactsName;

    /**
     * 联系人ID
     */
    @ApiModelProperty(value = "联系人ID")
    private Long contactsId;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String telephone;

    /**
     * 文件内容JSON格式
     */
    @ApiModelProperty(value = "文件内容JSON格式")
    private String fileContent;

    /**
     * 初审结果文件
     */
    @ApiModelProperty(value = "初审结果文件")
    private String preliminaryReviewResults;

    /**
     * 征求意见ID（，号分割，一个单位一条数据）
     */
    @ApiModelProperty(value = "征求意见ID（，号分割，一个单位一条数据）")
    private String solicitOpinionsId;

    /**
     * 问题说明ID（，号分割一个问题一条数据）
     */
    @ApiModelProperty(value = "问题说明ID（，号分割一个问题一条数据）")
    private String problemDescriptionId;


    /**
     * 是否及时报备，0不及时，1及时
     */
    @ApiModelProperty(value = "是否及时报备，0不及时，1及时")
    private Integer isInTime;


    /**
     * 审查意见文件ID
     */
    @ApiModelProperty(value = "审查意见文件ID")
    private String reviewOpinionsFileId;

    /**
     * 审查意见文件名
     */
    @ApiModelProperty(value = "审查意见文件名")
    private String reviewOpinionsName;


    /**
     * 审查意见文件路径
     */
    @ApiModelProperty(value = "审查意见文件路径")
    private String reviewOpinionsFilePath;


    /**
     * 呈批单ID
     */
    @ApiModelProperty(value = "呈批单ID")
    private String cpbId;

    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;
}
