package com.ctsi.regulations.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 法规室审查表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface TRegulationsExaminationMapper extends MybatisBaseMapper<TRegulationsExamination> {


    @InterceptorIgnore(tenantLine="true")
    IPage<TRegulationsExamination> selectPageNoHandel(IPage<TRegulationsExamination> page, @Param("entityDTO") TRegulationsExaminationDTO entityDTO);
}
