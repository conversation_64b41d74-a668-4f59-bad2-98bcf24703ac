package com.ctsi.regulations.entity.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RectificationHistory {

    @ApiModelProperty(value = "序号，从1开始")
    private Integer index;

    @ApiModelProperty(value = "整改内容")
    private String rectificationContent;

    @ApiModelProperty(value = "整改内容文件ID")
    private Long rectificationFileId;

    @ApiModelProperty(value = "整改内容文件名称")
    private String rectificationFileName;

    @ApiModelProperty(value = "整改内容文件路径")
    private String rectificationFilePath;

    /**
     * 回复类别 10 通过 20 拒绝
     */
    @ApiModelProperty(value = "回复类别")
    private String replyType;

    @ApiModelProperty(value = "拒绝原因/回复内容")
    private String rejectReason;

    @ApiModelProperty(value = "整改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rectificationTime;

    @ApiModelProperty(value = "驳回时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rejectTime;



    public static List<RectificationHistory> generateRectificationHistoryList() {
        List<RectificationHistory> rectificationHistoryList = new ArrayList<>();

        RectificationHistory rectificationHistory1 = RectificationHistory.builder()
                .index(1)
                .rectificationContent("整改内容1")
                .rectificationFileId(1816413847545049089L)
                .rectificationFileName("媒体报道.docx")
                .rectificationFilePath("2024/upload/0/0/7/25/1816413847515688963.docx")
                .replyType("20")
                .rejectReason("123")
                .rectificationTime(LocalDateTime.now())
                .rejectTime(LocalDateTime.now())
                .build();

        RectificationHistory rectificationHistory2 = RectificationHistory.builder()
                .index(2)
                .rectificationContent("整改内容2")
                .rectificationFileId(1816413847431802881L)
                .rectificationFileName("科创对接对接文档v13.docx")
                .rectificationFilePath("2024/upload/0/0/7/25/1816413847389859841.docx")
                .rejectReason("")
                .rectificationTime(LocalDateTime.now())
                .build();

        rectificationHistoryList.add(rectificationHistory1);
        rectificationHistoryList.add(rectificationHistory2);

        return rectificationHistoryList;
    }




//    public static void main(String[] args) {
//        List<RectificationHistory> rectificationHistoryList = generateRectificationHistoryList();
//        System.out.println(JSON.toJSONString(rectificationHistoryList));
//    }

}
