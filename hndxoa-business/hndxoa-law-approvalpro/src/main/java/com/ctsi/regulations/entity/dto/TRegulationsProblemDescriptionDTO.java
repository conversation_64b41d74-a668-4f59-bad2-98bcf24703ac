package com.ctsi.regulations.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * 问题说明表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TRegulationsProblemDescriptionDTO对象", description="问题说明表")
public class TRegulationsProblemDescriptionDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 法规室审查表ID
     */
    @ApiModelProperty(value = "法规室审查表ID")
    private Long examinationId;

    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID")
    private Long processId;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 报备时间
     */
    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportingTime;

    /**
     * 问题描述
     */
    @ApiModelProperty(value = "问题描述")
    private String problemContent;

    /**
     * 附件文件ID
     */
    @ApiModelProperty(value = "附件文件ID")
    private Long fileId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 附件地址
     */
    @ApiModelProperty(value = "附件地址")
    private String filePath;

    /**
     * 问题说明返回结果
     */
    @ApiModelProperty(value = "问题说明返回结果")
    private String descriptionResult;

    /**
     * 问题说明返回结果附件ID
     */
    @ApiModelProperty(value = "问题说明返回结果附件ID")
    private Long descriptionFileId;

    /**
     * 问题说明返回结果附件名
     */
    @ApiModelProperty(value = "问题说明返回结果附件名")
    private String descriptionFileName;

    /**
     * 问题说明返回结果附件路径
     */
    @ApiModelProperty(value = "问题说明返回结果附件路径")
    private String descriptionFilePath;

    /**
     *已读未读
     */
    @ApiModelProperty(value = "已读未读,0未读，1已读")
    private Integer isRead;
    /**
     * 问题推送状态:  10 推送成功: 20 问题反馈成功
     *          -1 推送失败
     */
    private Integer wtStatus;


    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;
}
