package com.ctsi.regulations.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <p>
 * 法规室审查处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_regulations_handle")
@ApiModel(value="TRegulationsHandle对象", description="法规室审查处理")
public class TRegulationsHandle extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 法规室审查表ID
     */
    @ApiModelProperty(value = "法规室审查表ID")
    private String  examinationId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 审查意见文本
     */
    @ApiModelProperty(value = "审查意见文本")
    private String reviewOpinionsText;

    /**
     * 报备单位ID
     */
    @ApiModelProperty(value = "报备单位ID")
    private String reportCompanyId;

    /**
     * 报备单位名称
     */
    @ApiModelProperty(value = "报备单位名称")
    private String reportCompanyName;

    /**
     * 送审日期
     */
    @ApiModelProperty(value = "送审日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportingTime;

    /**
     * 正文文件ID
     */
    @ApiModelProperty(value = "正文文件ID")
    private Long mainTestFileId;

    /**
     * 正文文件名
     */
    @ApiModelProperty(value = "正文文件名")
    private String mainTestFileName;

    /**
     * 正文文件路径
     */
    @ApiModelProperty(value = "正文文件路径")
    private String mainTestFilePath;

    /**
     * 文件目录文件ID
     */
    @ApiModelProperty(value = "文件目录文件ID")
    private Long fileDirectoryFileId;

    /**
     * 文件目录文件名
     */
    @ApiModelProperty(value = "文件目录文件名")
    private String fileDirectoryFileName;

    /**
     * 文件目录文件路径
     */
    @ApiModelProperty(value = "文件目录文件路径")
    private String fileDirectoryFilePath;

    /**
     * 附件文件ID
     */
    @ApiModelProperty(value = "附件文件ID")
    private Long annexFileId;

    /**
     * 附件文件名
     */
    @ApiModelProperty(value = "附件文件名")
    private String annexFileName;

    /**
     * 附件文件路径
     */
    @ApiModelProperty(value = "附件文件路径")
    private String annexFilePath;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;


    /**
     * 0未提交，1已提交确认
     */
    @ApiModelProperty(value = "0未提交，1已提交确认")
    private Integer isOfficialDocuments;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处室负责人")
    private String departmentHeadName;

    @ApiModelProperty(value = "状态")
    private String bpmStatus;

    @ApiModelProperty(value = "领导意见")
    private String opinion;

    @ApiModelProperty(value = "文件密级:数据字典中文值")
    private String securityClassified;
    @ApiModelProperty(value = "文件密级:数据字CODE")
    private Integer securityClassifiedCode;

    /**
     * 定密依据
     */
    @ApiModelProperty(value = "定密依据")
    private String classificationBasis;


}
