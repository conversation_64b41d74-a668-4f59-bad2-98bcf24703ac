package com.ctsi.regulations.service;

import com.ctsi.regulations.entity.dto.TRegulationsConfirmSaveDTO;
import com.ctsi.regulations.entity.TRegulationsConfirmSave;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 法规室整改确认驳回和存档 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface ITRegulationsConfirmSaveService extends SysBaseServiceI<TRegulationsConfirmSave> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsConfirmSaveDTO> queryListPage(TRegulationsConfirmSaveDTO entityDTO, BasePageForm page);

    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsConfirmSaveDTO> queryListPageArchive(TRegulationsConfirmSaveDTO entityDTO, BasePageForm page);


    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsConfirmSaveDTO> queryList(TRegulationsConfirmSaveDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TRegulationsConfirmSaveDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TRegulationsConfirmSaveDTO create(TRegulationsConfirmSaveDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TRegulationsConfirmSaveDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTRegulationsConfirmSaveId
     * @param code
     * @return
     */
    boolean existByTRegulationsConfirmSaveId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TRegulationsConfirmSaveDTO> dataList);


    /**
     * 审查结果<回复>/整改回复
     * @param paramJsonStr
     * @param files
     */
    void recCheckReplyFgs(String paramJsonStr , MultipartFile[] files);


    /**
     * 更新为已读
     * @param id
     * @return
     */
    Boolean updateIsRead(Long id);
}
