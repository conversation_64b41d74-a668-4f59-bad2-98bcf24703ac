package com.ctsi.regulations.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsExaminationProcess;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationProcessDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 法规室审查表流程记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface ITRegulationsExaminationProcessService extends SysBaseServiceI<TRegulationsExaminationProcess> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsExaminationProcessDTO> queryListPage(TRegulationsExaminationProcessDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsExaminationProcessDTO> queryList(TRegulationsExaminationProcessDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TRegulationsExaminationProcessDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TRegulationsExaminationProcessDTO create(TRegulationsExaminationProcessDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TRegulationsExaminationProcessDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTRegulationsExaminationProcessId
     * @param code
     * @return
     */
    boolean existByTRegulationsExaminationProcessId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TRegulationsExaminationProcessDTO> dataList);


    /**
     * 初始化流程记录
     * @param regulationsExamination
     * @return
     */
    Integer initProcess(TRegulationsExamination regulationsExamination);


    /**更新状态
     * @return
     */
    boolean updateStatus(TRegulationsExamination regulationsExamination,String node, Integer status, String orgName);


    /**更新状态,没有启动的不更新
     * @return
     */
    boolean updateStatus(TRegulationsExamination regulationsExamination,String node, Integer status, String orgName,boolean isStart);

    /**更新状态，多条数据时更新指定关联业务ID的数据
     * @return
     */
    boolean updateStatus(TRegulationsExamination regulationsExamination,String node, Integer status, String orgName,Long businessId,String remark);


    /**更新状态
     * @return
     */
    boolean updateStatusAllEnd(TRegulationsExamination regulationsExamination,String node, Integer status, String orgName);


    List<TRegulationsExaminationProcess> selectBtExaminationIdNode(Long examinationId,String node,Long businessId);
}
