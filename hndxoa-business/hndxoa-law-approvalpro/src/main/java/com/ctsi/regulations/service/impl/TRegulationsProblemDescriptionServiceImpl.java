package com.ctsi.regulations.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsExaminationProcess;
import com.ctsi.regulations.entity.TRegulationsProblemDescription;
import com.ctsi.regulations.entity.dto.TRegulationsProblemDescriptionDTO;
import com.ctsi.regulations.mapper.TRegulationsExaminationMapper;
import com.ctsi.regulations.mapper.TRegulationsProblemDescriptionMapper;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.regulations.service.ITRegulationsProblemDescriptionService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 问题说明表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Slf4j
@Service
public class TRegulationsProblemDescriptionServiceImpl extends SysBaseServiceImpl<TRegulationsProblemDescriptionMapper, TRegulationsProblemDescription> implements ITRegulationsProblemDescriptionService {

    @Autowired
    private TRegulationsProblemDescriptionMapper tRegulationsProblemDescriptionMapper;

    @Autowired
    private com.ctsi.regulations.service.ITRegulationsExaminationService regulationsExaminationService;

    @Autowired
    private TRegulationsExaminationMapper regulationsExaminationMapper;

    @Autowired
    private com.ctsi.ssdc.service.BizService bizService;

    @Autowired
    private ITRegulationsExaminationProcessService regulationsExaminationProcessService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TRegulationsProblemDescriptionDTO> queryListPage(TRegulationsProblemDescriptionDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TRegulationsProblemDescription> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByDesc(TRegulationsProblemDescription::getReportingTime);
        IPage<TRegulationsProblemDescription> pageData = tRegulationsProblemDescriptionMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TRegulationsProblemDescriptionDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsProblemDescriptionDTO.class));

        return new PageResult<TRegulationsProblemDescriptionDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TRegulationsProblemDescriptionDTO> queryList(TRegulationsProblemDescriptionDTO entityDTO) {
        LambdaQueryWrapper<TRegulationsProblemDescription> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TRegulationsProblemDescription::getExaminationId,entityDTO.getExaminationId());
        queryWrapper.orderByDesc(TRegulationsProblemDescription::getCreateTime);
            List<TRegulationsProblemDescription> listData = tRegulationsProblemDescriptionMapper.selectListNoAdd(queryWrapper);
            List<TRegulationsProblemDescriptionDTO> TRegulationsProblemDescriptionDTOList = ListCopyUtil.copy(listData, TRegulationsProblemDescriptionDTO.class);
        return TRegulationsProblemDescriptionDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TRegulationsProblemDescriptionDTO findOne(Long id) {
        TRegulationsProblemDescription  tRegulationsProblemDescription =  tRegulationsProblemDescriptionMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tRegulationsProblemDescription,TRegulationsProblemDescriptionDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TRegulationsProblemDescriptionDTO create(TRegulationsProblemDescriptionDTO entityDTO) {
       TRegulationsProblemDescription tRegulationsProblemDescription =  BeanConvertUtils.copyProperties(entityDTO,TRegulationsProblemDescription.class);
        tRegulationsProblemDescription.setIsRead(1);
        save(tRegulationsProblemDescription);
        //更新到法规室审查表
        TRegulationsExamination tRegulationsExamination = regulationsExaminationMapper.selectById(tRegulationsProblemDescription.getExaminationId());
//        tRegulationsExamination.setCurrentStage("F");
        if(StringUtils.isNotBlank(tRegulationsExamination.getProblemDescriptionId())){
            Set<String> list = new HashSet<>(Arrays.asList(tRegulationsExamination.getProblemDescriptionId().split(",")));
            list.add(tRegulationsExamination.getProblemDescriptionId());
            tRegulationsExamination.setProblemDescriptionId(org.apache.commons.lang3.StringUtils.strip(list.toString(),"[]").replaceAll(" ",""));
        }else{
            tRegulationsExamination.setProblemDescriptionId(String.valueOf(tRegulationsProblemDescription.getId()));
        }
        regulationsExaminationMapper.updateById(tRegulationsExamination);
        //更新流程节点
        List<TRegulationsExaminationProcess> regulationsExaminationProcess = regulationsExaminationProcessService.selectBtExaminationIdNode(tRegulationsProblemDescription.getExaminationId(), "F",null);
        LocalDateTime now = LocalDateTime.now();
        TRegulationsExaminationProcess regulationsExaminationProcess1 = BeanConvertUtils.copyProperties(regulationsExaminationProcess.get(0), TRegulationsExaminationProcess.class);
        regulationsExaminationProcess1.setId(null);
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        regulationsExaminationProcess1.setNodeCompanyName(companyName);
        regulationsExaminationProcess1.setNodeUpdateTime(now);
        regulationsExaminationProcess1.setNodeStatus(2);
        regulationsExaminationProcess1.setNode("F");
        regulationsExaminationProcess1.setBusinessId(tRegulationsProblemDescription.getId());
        regulationsExaminationProcessService.save(regulationsExaminationProcess1);
        //删除空的初始化
        for (TRegulationsExaminationProcess examinationProcess : regulationsExaminationProcess) {
            if(examinationProcess.getBusinessId()==null){
                regulationsExaminationProcessService.removeById(examinationProcess);
                break;
            }
        }

        bizService.pushProblemFgs(tRegulationsProblemDescription.getId());
        return  BeanConvertUtils.copyProperties(tRegulationsProblemDescription,TRegulationsProblemDescriptionDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TRegulationsProblemDescriptionDTO entity) {
        TRegulationsProblemDescription tRegulationsProblemDescription = BeanConvertUtils.copyProperties(entity,TRegulationsProblemDescription.class);
        tRegulationsProblemDescription.setIsRead(0);
        int indexOf = tRegulationsProblemDescriptionMapper.updateById(tRegulationsProblemDescription);
        //检查问题说明是否都结束了
        TRegulationsProblemDescriptionDTO query=new TRegulationsProblemDescriptionDTO();
        query.setExaminationId(entity.getExaminationId());
        List<TRegulationsProblemDescriptionDTO> tRegulationsProblemDescriptionDTOS = this.queryList(query);

        boolean allEnd=true;
        for (TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO : tRegulationsProblemDescriptionDTOS) {
            if(tRegulationsProblemDescriptionDTO.getDescriptionResult()==null&&tRegulationsProblemDescriptionDTO.getDescriptionFileId()==null){
                allEnd=false;
                break;
            }
        }
        TRegulationsExamination regulationsExamination = regulationsExaminationService.getById(entity.getExaminationId());
        if(allEnd){
            regulationsExaminationProcessService.updateStatus(regulationsExamination, "F", 3, null);
        }else{
            regulationsExaminationProcessService.updateStatus(regulationsExamination, "F", 2, null);
        }

        return indexOf;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tRegulationsProblemDescriptionMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TRegulationsProblemDescriptionId
     * @return
     */
    @Override
    public boolean existByTRegulationsProblemDescriptionId(Long TRegulationsProblemDescriptionId) {
        if (TRegulationsProblemDescriptionId != null) {
            LambdaQueryWrapper<TRegulationsProblemDescription> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TRegulationsProblemDescription::getId, TRegulationsProblemDescriptionId);
            List<TRegulationsProblemDescription> result = tRegulationsProblemDescriptionMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TRegulationsProblemDescriptionDTO> dataList) {
        List<TRegulationsProblemDescription> result = ListCopyUtil.copy(dataList, TRegulationsProblemDescription.class);
        return saveBatch(result);
    }

    @Override
    public Integer updateIsRead(Long examinationId) {
        LambdaQueryWrapper<TRegulationsProblemDescription> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TRegulationsProblemDescription::getExaminationId,examinationId);
        lambdaQueryWrapper.eq(TRegulationsProblemDescription::getIsRead,0);
        List<TRegulationsProblemDescription> tRegulationsProblemDescriptions = this.baseMapper.selectListNoAdd(lambdaQueryWrapper);
        for (TRegulationsProblemDescription tRegulationsProblemDescription : tRegulationsProblemDescriptions) {
            tRegulationsProblemDescription.setIsRead(1);
        }
        updateBatchById(tRegulationsProblemDescriptions);
        if(!tRegulationsProblemDescriptions.isEmpty()) {
            return tRegulationsProblemDescriptions.size();
        }else {
            return 0;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void recProblemFeed(String paramJsonStr , MultipartFile[] files) {
        if (files == null || files.length < 1) {
            throw new BusinessException("90002:无法获取附件");
        }
        JSONObject jsonObject = JSON.parseObject(paramJsonStr);
        // String recordId = (String)jsonObject.get("recordId");
        String content = (String)jsonObject.get("content");
        String contentId = (String)jsonObject.get("contentId");
        String orgName = (String)jsonObject.get("orgName");

        TRegulationsProblemDescription tRegulationsProblemDescription = tRegulationsProblemDescriptionMapper.selectById(contentId);
        TRegulationsExamination regulationsExamination = regulationsExaminationMapper.selectById(tRegulationsProblemDescription.getExaminationId());

        Long formDataId = SnowflakeIdUtil.getSnowFlakeLongId();
        // 附件处理
        List<CscpEnclosureFile> fileList = regulationsExaminationService.annexHandler(files , formDataId,
                regulationsExamination.getSecrecyGrade());
        tRegulationsProblemDescription.setDescriptionResult(content);
        tRegulationsProblemDescription.setDescriptionFileId(formDataId);
        tRegulationsProblemDescription.setWtStatus(20);

        // 支持一个附件回复
        CscpEnclosureFile file = fileList.get(0);
        tRegulationsProblemDescription.setDescriptionFilePath(file.getFileUrl());
        tRegulationsProblemDescription.setDescriptionFileName(file.getFileName());
        // 更新问题   todo 一个问题回复之后 节点是已完成吗?
        tRegulationsProblemDescriptionMapper.updateById(tRegulationsProblemDescription);

        //检查问题说明是否都结束了
        TRegulationsProblemDescriptionDTO query=new TRegulationsProblemDescriptionDTO();
        query.setExaminationId(tRegulationsProblemDescription.getExaminationId());
        List<TRegulationsProblemDescriptionDTO> tRegulationsProblemDescriptionDTOS = this.queryList(query);
        boolean allEnd=true;
        for (TRegulationsProblemDescriptionDTO tRegulationsProblemDescriptionDTO : tRegulationsProblemDescriptionDTOS) {
            if(tRegulationsProblemDescriptionDTO.getDescriptionResult()==null&&tRegulationsProblemDescriptionDTO.getDescriptionFileId()==null){
                allEnd=false;
                break;
            }
        }
        if(allEnd){
            regulationsExaminationProcessService.updateStatus(regulationsExamination, "F", 3, orgName);
        }else{
            regulationsExaminationProcessService.updateStatus(regulationsExamination, "F", 2, orgName);
        }


    }


}
