package com.ctsi.regulations.entity.dto;

import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 法规室审查表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TRegulationsExaminationDTO对象", description="法规室审查表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TRegulationsExaminationDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 外系统唯一id,通讯识别id
     */
    @ApiModelProperty(value = "外系统唯一id")
    private String recordId;

    /**
     * 只入库,暂时不参与任何 业务
     * (文件密级,默认是1未标注)可选固定值1.未标注 2.公开 3.内部 4.秘密 5.机密 6.涉密 7.明电;传数字
     */
    @ApiModelProperty(value = "文件密级")
    private  Integer secrecyGrade;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 审查意见文本
     */
    @ApiModelProperty(value = "审查意见文本")
    private String reviewOpinionsText;


    /**
     * 报备单位名称
     */
    @ApiModelProperty(value = "报备单位名称")
    private String reportingUnitName;

    /**
     * 报备单位ID
     */
    @ApiModelProperty(value = "报备单位ID")
    private Long reportingUnitId;

    /**
     * 报备时间
     */
    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportingTime;


    @ApiModelProperty(value = "登记单位名称")
    private String registerUnitName;

    @ApiModelProperty(value = "登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;


    @ApiModelProperty(value = "登记确认单位名称")
    private String registerComfirmUnitName;

    @ApiModelProperty(value = "登记确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerComfirmTime;



    /**
     * 审查意见
     */
    @ApiModelProperty(value = "审查意见 0初审中， 1直接予以备案通过、2予以备案通过并提出建议、3予以备案通过并告知、4予以备案通过并出面提醒、5纠正、6对主动纠正后重报件予以备案通过，7其他")
    private Integer reviewOpinions;

    /**
     * 当前状态
     */
    @ApiModelProperty(value = "流程当前状态")
    private String currentStage;

    /**
     * 需要跳过的状态
     */
    @ApiModelProperty(value = "流程需要跳过的状态")
    private String skipStatus;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime releaseTime;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactsName;

    /**
     * 联系人ID
     */
    @ApiModelProperty(value = "联系人ID")
    private Long contactsId;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String telephone;

    /**
     * 文件内容JSON格式
     */
    @ApiModelProperty(value = "文件内容JSON格式")
    private String fileContent;

    /**
     *
     */
    @ApiModelProperty(value = "附件List")
    private List<ExaminationFile> examinationFileList;

    /**
     * 其他审查结果备注
     */
    @ApiModelProperty(value = "其他审查结果备注")
    private String otherReviewResults;

    /**
     * 初审结果文件
     */
    @ApiModelProperty(value = "初审结果文件")
    private String preliminaryReviewResults;

    /**
     * 征求意见ID（，号分割，一个单位一条数据）
     */
    @ApiModelProperty(value = "征求意见ID（，号分割，一个单位一条数据）")
    private String solicitOpinionsId;

    /**
     * 问题说明ID（，号分割一个问题一条数据）
     */
    @ApiModelProperty(value = "问题说明ID（，号分割一个问题一条数据）")
    private String problemDescriptionId;


    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 是否及时报备，0不及时，1及时
     */
    @ApiModelProperty(value = "是否及时报备，0不及时，1及时")
    private Integer isInTime;


    /**
     * 审查意见文件ID
     */
    @ApiModelProperty(value = "审查意见文件ID")
    private String reviewOpinionsFileId;

    /**
     * 审查意见文件名
     */
    @ApiModelProperty(value = "审查意见文件名")
    private String reviewOpinionsName;


    /**
     * 审查意见文件路径
     */
    @ApiModelProperty(value = "审查意见文件路径")
    private String reviewOpinionsFilePath;


    /**
     *w问题说明已读未读
     */
    @ApiModelProperty(value = "问题说明已读未读,0未读，1已读")
    private Integer isReadProblem;

    /**
     *w征求意见已读未读
     */
    @ApiModelProperty(value = "征求意见已读未读,0未读，1已读")
    private Integer isReadSolicit;


    /**
     * 呈批单ID
     */
    @ApiModelProperty(value = "呈批单ID")
    private String cpbId;

    @ApiModelProperty(value = "呈批单当前环节")
    private String cpbCurrentNode;

    @ApiModelProperty(value = "呈批单当前状态")
    private String cpbBpmStatus;

    @ApiModelProperty(value = "呈批单当前用户")
    private String cpbCurrentUser;

    @ApiModelProperty("表单流程信息")
    private TaskVO taskVO;

    @ApiModelProperty("征求意见是否完成，0未完成，1已完成或无需")
    private Integer isSolicitOpinionsEnd;

    @ApiModelProperty("问题说明是否完成，0未完成，1已完成或无需")
    private Integer isProblemDescriptionEnd;

    @ApiModelProperty("是否已送处理，0未处理，1已处理")
    private Integer isHandle;

    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;


    // public static void main(String[] args) {
    //     TRegulationsExaminationDTO build = TRegulationsExaminationDTO.builder()
    //             .recordId("recordId")
    //             .fileName("fileName")
    //             .fileNumber("11111")
    //             .reportingUnitName("报备单位名称")
    //             .reportingTime(LocalDateTime.now())
    //             .releaseTime(LocalDateTime.now())
    //             .contactsName("张三")
    //             .telephone("12345678912")
    //             .isInTime(1)
    //             .secrecyGrade(3)
    //             .build();
    //     System.out.println(JSON.toJSONString(build));
    // }

}
