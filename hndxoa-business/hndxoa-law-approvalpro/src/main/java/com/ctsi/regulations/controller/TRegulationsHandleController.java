package com.ctsi.regulations.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.regulations.entity.TRegulationsHandle;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import com.ctsi.regulations.entity.dto.TRegulationsHandleDTO;
import com.ctsi.regulations.service.ITRegulationsHandleService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tRegulationsHandle")
@Api(value = "法规室审查处理", tags = "法规室审查处理接口")
public class TRegulationsHandleController extends BaseController {

    private static final String ENTITY_NAME = "tRegulationsHandle";

    @Autowired
    private ITRegulationsHandleService tRegulationsHandleService;



    /**
     *  新增法规室审查处理批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsHandle.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室审查处理批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsHandle.add')")
    public ResultVO createBatch(@RequestBody List<TRegulationsHandleDTO> tRegulationsHandleList) {
       Boolean  result = tRegulationsHandleService.insertBatch(tRegulationsHandleList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tRegulationsHandle.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增法规室审查处理数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsHandle.add')")
    public ResultVO<TRegulationsHandleDTO> create(@RequestBody TRegulationsHandleDTO tRegulationsHandleDTO)  {
        TRegulationsHandleDTO result = tRegulationsHandleService.create(tRegulationsHandleDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tRegulationsHandle.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新法规室审查处理数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsHandle.update')")
    public ResultVO update(@RequestBody TRegulationsHandleDTO tRegulationsHandleDTO) {
	    Assert.notNull(tRegulationsHandleDTO.getId(), "general.IdNotNull");
        int count = tRegulationsHandleService.update(tRegulationsHandleDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除法规室审查处理数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tRegulationsHandle.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsHandle.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tRegulationsHandleService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TRegulationsHandleDTO tRegulationsHandleDTO = tRegulationsHandleService.findOne(id);
        return ResultVO.success(tRegulationsHandleDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @PostMapping("/queryTRegulationsHandlePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TRegulationsHandleDTO>> queryTRegulationsHandlePage(@RequestBody TRegulationsHandleDTO tRegulationsHandleDTO, BasePageForm basePageForm) {
        return ResultVO.success(tRegulationsHandleService.queryListPage(tRegulationsHandleDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @PostMapping("/queryTRegulationsHandle")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TRegulationsHandleDTO>> queryTRegulationsHandle(@RequestBody TRegulationsHandleDTO tRegulationsHandleDTO) {
       List<TRegulationsHandleDTO> list = tRegulationsHandleService.queryList(tRegulationsHandleDTO);
       return ResultVO.success(new ResResult<TRegulationsHandleDTO>(list));
   }


    /**
     *  批量送审.
     */
    @PostMapping("/batchSubmittals")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tRegulationsHandle.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "批量送审")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tRegulationsHandle.add')")
    public ResultVO batchSubmittals(@RequestBody List<TRegulationsExaminationDTO> regulationsExaminationDTOList) {
        TRegulationsHandle result = tRegulationsHandleService.batchSubmittals(regulationsExaminationDTOList);
        return ResultVO.success(result);
    }
}
