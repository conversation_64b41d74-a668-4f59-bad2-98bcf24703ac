package com.ctsi.regulations.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * 法规室征求意见记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_regulations_solicit_opinions")
@ApiModel(value="TRegulationsSolicitOpinions对象", description="法规室征求意见记录表")
public class TRegulationsSolicitOpinions extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 法规室审查表ID
     */
    @ApiModelProperty(value = "法规室审查表ID")
    private Long examinationId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 报备单位名称
     */
    @ApiModelProperty(value = "报备单位名称")
    private String reportingUnitName;

    /**
     * 报备时间
     */
    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportingTime;

    /**
     * 被征求意见单位ID
     */
    @ApiModelProperty(value = "被征求意见单位ID")
    private Long solicitCompanyId;

    /**
     * 被征求意见单位名称
     */
    @ApiModelProperty(value = "被征求意见单位名称")
    private String solicitCompanyName;

    /**
     * 被征求意见内容
     */
    @ApiModelProperty(value = "被征求意见内容")
    private String solicitCompanyContent;

    /**
     * 征求意见呈批单文件ID
     */
    @ApiModelProperty(value = "征求意见呈批单文件ID")
    private Long approvalFormFileId;

    /**
     * 征求意见呈批单文件名
     */
    @ApiModelProperty(value = "征求意见呈批单文件名")
    private String approvalFormFileName;

    /**
     * 征求意见呈批单文件路径
     */
    @ApiModelProperty(value = "征求意见呈批单文件路径")
    private String approvalFormFilePath;

    /**
     * 征求意见函文件ID
     */
    @ApiModelProperty(value = "征求意见函文件ID")
    private Long commentLetterFileId;

    /**
     * 征求意见函文件名
     */
    @ApiModelProperty(value = "征求意见函文件名")
    private String commentLetterFileName;

    /**
     * 征求意见函文件路径
     */
    @ApiModelProperty(value = "征求意见函文件路径")
    private String commentLetterFilePath;

    /**
     * 反馈意见内容
     */
    @ApiModelProperty(value = "反馈意见内容")
    private String feedbackOpinionContent;

    /**
     * 反馈文件ID
     */
    @ApiModelProperty(value = "反馈文件ID")
    private Long feedbackFileId;

    /**
     * 反馈文件名
     */
    @ApiModelProperty(value = "反馈文件名")
    private String feedbackFileName;

    /**
     * 反馈文件路径
     */
    @ApiModelProperty(value = "反馈文件路径")
    private String feedbackFilePath;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime feedbackTime;

    /**
     * 征求意见当前状态
     */
    @ApiModelProperty(value = "征求意见当前状态，0未完成，1已完成")
    private Integer opinionStatus;


    /**
     * 是否及时报备
     */
    @ApiModelProperty(value = "是否及时报备，0不及时，1及时")
    private Integer isTimelyReporting;


    /**
     *整改反馈已读未读
     */
    @ApiModelProperty(value = "改反馈已读未读,0未读，1已读")
    private Integer isRead;

    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;
}
