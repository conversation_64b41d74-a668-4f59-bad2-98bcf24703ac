package com.ctsi.regulations.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.regulations.entity.TRegulationsConfirmSave;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsExaminationProcess;
import com.ctsi.regulations.entity.dto.RectificationHistory;
import com.ctsi.regulations.entity.dto.TRegulationsConfirmSaveDTO;
import com.ctsi.regulations.mapper.TRegulationsConfirmSaveMapper;
import com.ctsi.regulations.service.ITRegulationsConfirmSaveService;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.regulations.service.ITRegulationsExaminationService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <p>
 * 法规室整改确认驳回和存档 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Service
public class TRegulationsConfirmSaveServiceImpl extends SysBaseServiceImpl<TRegulationsConfirmSaveMapper, TRegulationsConfirmSave> implements ITRegulationsConfirmSaveService {

    @Autowired
    private TRegulationsConfirmSaveMapper tRegulationsConfirmSaveMapper;
    @Autowired
    private ITRegulationsExaminationService regulationsExaminationService;

    @Autowired
    private ITRegulationsConfirmSaveService regulationsConfirmSaveService;
    @Autowired
    private com.ctsi.ssdc.service.BizService bizService;

    @Autowired
    ITRegulationsExaminationProcessService regulationsExaminationProcessService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TRegulationsConfirmSaveDTO> queryListPage(TRegulationsConfirmSaveDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TRegulationsConfirmSave> queryWrapper = new LambdaQueryWrapper();
        if(StringUtils.isNotEmpty(entityDTO.getFileName())) {
            queryWrapper.like(TRegulationsConfirmSave::getFileName, entityDTO.getFileName());
        }
        if(entityDTO.getStartTimeBB()!=null&&entityDTO.getEndTimeBB()!=null){
            queryWrapper.ge(TRegulationsConfirmSave::getReportingTime,entityDTO.getStartTimeBB());
            queryWrapper.le(TRegulationsConfirmSave::getReportingTime,entityDTO.getEndTimeBB());
            queryWrapper.orderByDesc(TRegulationsConfirmSave::getReportingTime);
        }

        if(entityDTO.getStartTimeZG()!=null&&entityDTO.getEndTimeZG()!=null){
            queryWrapper.ge(TRegulationsConfirmSave::getRectificationTime,entityDTO.getStartTimeZG());
            queryWrapper.le(TRegulationsConfirmSave::getRectificationTime,entityDTO.getEndTimeZG());
            queryWrapper.orderByDesc(TRegulationsConfirmSave::getArchiveTime);
        }
        queryWrapper.eq(TRegulationsConfirmSave::getIsArchive,0);
        queryWrapper.orderByDesc(TRegulationsConfirmSave::getCreateTime);
        if(entityDTO.getReviewOpinions()!=null){
            queryWrapper.eq(TRegulationsConfirmSave::getReviewOpinions,entityDTO.getReviewOpinions());
        }
        IPage<TRegulationsConfirmSave> pageData = tRegulationsConfirmSaveMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TRegulationsConfirmSaveDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsConfirmSaveDTO.class));
        for (TRegulationsConfirmSaveDTO record : data.getRecords()) {
            if(record.getRectificationHistory()!=null) {
                List<RectificationHistory> rectificationHistoryList = JSON.parseObject(record.getRectificationHistory(), new TypeReference<List<RectificationHistory>>() {
                });
                // 按照index字段从大到小降序排序
                Collections.sort(rectificationHistoryList, (r1, r2) -> r2.getIndex() - r1.getIndex());
                record.setRectificationHistoryList(rectificationHistoryList);
            }

            //查询报备时间，审查意见
//            TRegulationsExamination byId = regulationsExaminationService.getById(record.getExaminationId());
//            record.setReportingTime(byId.getReportingTime());
//            record.setReviewOpinions(byId.getReviewOpinions());
        }


        return new PageResult<TRegulationsConfirmSaveDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    @Override
    public PageResult<TRegulationsConfirmSaveDTO> queryListPageArchive(TRegulationsConfirmSaveDTO entityDTO, BasePageForm page) {
        //设置条件
        LambdaQueryWrapper<TRegulationsConfirmSave> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TRegulationsConfirmSave::getIsArchive,1);
        if(StringUtils.isNotEmpty(entityDTO.getFileName())) {
            queryWrapper.like(TRegulationsConfirmSave::getFileName, entityDTO.getFileName());
        }
//        queryWrapper.orderByDesc(TRegulationsConfirmSave::getReportingTime);
        if(entityDTO.getStartTimeBB()!=null&&entityDTO.getEndTimeBB()!=null){
            queryWrapper.ge(TRegulationsConfirmSave::getReportingTime,entityDTO.getStartTimeBB());
            queryWrapper.le(TRegulationsConfirmSave::getReportingTime,entityDTO.getEndTimeBB());
        }

        if(entityDTO.getStartTimeCD()!=null&&entityDTO.getEndTimeCD()!=null){
            queryWrapper.ge(TRegulationsConfirmSave::getArchiveTime,entityDTO.getStartTimeCD());
            queryWrapper.le(TRegulationsConfirmSave::getArchiveTime,entityDTO.getEndTimeCD());
        }
        if(entityDTO.getReviewOpinions()!=null){
            queryWrapper.eq(TRegulationsConfirmSave::getReviewOpinions,entityDTO.getReviewOpinions());
        }
        queryWrapper.orderByDesc(TRegulationsConfirmSave::getArchiveTime);
        IPage<TRegulationsConfirmSave> pageData = tRegulationsConfirmSaveMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        //返回
        IPage<TRegulationsConfirmSaveDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsConfirmSaveDTO.class));
        for (TRegulationsConfirmSaveDTO record : data.getRecords()) {
            if(record.getRectificationHistory()!=null) {
                List<RectificationHistory> rectificationHistoryList = JSON.parseObject(record.getRectificationHistory(), new TypeReference<List<RectificationHistory>>() {
                });
                record.setRectificationHistoryList(rectificationHistoryList);
            }

            //查询报备时间，审查意见，报备单位
//            TRegulationsExamination byId = regulationsExaminationService.getById(record.getExaminationId());
//            record.setReportingTime(byId.getReportingTime());
//            record.setReviewOpinions(byId.getReviewOpinions());
//            record.setReportingUnitName(byId.getReportingUnitName());
        }


        return new PageResult<TRegulationsConfirmSaveDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TRegulationsConfirmSaveDTO> queryList(TRegulationsConfirmSaveDTO entityDTO) {
        LambdaQueryWrapper<TRegulationsConfirmSave> queryWrapper = new LambdaQueryWrapper();
            List<TRegulationsConfirmSave> listData = tRegulationsConfirmSaveMapper.selectList(queryWrapper);
            List<TRegulationsConfirmSaveDTO> TRegulationsConfirmSaveDTOList = ListCopyUtil.copy(listData, TRegulationsConfirmSaveDTO.class);
        for (TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO : TRegulationsConfirmSaveDTOList) {
            if(tRegulationsConfirmSaveDTO.getRectificationHistory()!=null) {
                List<RectificationHistory> rectificationHistoryList = JSON.parseObject(tRegulationsConfirmSaveDTO.getRectificationHistory(), new TypeReference<List<RectificationHistory>>() {
                });
                tRegulationsConfirmSaveDTO.setRectificationHistoryList(rectificationHistoryList);
            }
        }

        return TRegulationsConfirmSaveDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TRegulationsConfirmSaveDTO findOne(Long id) {
        TRegulationsConfirmSave  tRegulationsConfirmSave =  tRegulationsConfirmSaveMapper.selectById(id);
        TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO = BeanConvertUtils.copyProperties(tRegulationsConfirmSave, TRegulationsConfirmSaveDTO.class);
        if(tRegulationsConfirmSaveDTO.getRectificationHistory()!=null) {
            List<RectificationHistory> rectificationHistoryList = JSON.parseObject(tRegulationsConfirmSaveDTO.getRectificationHistory(), new TypeReference<List<RectificationHistory>>() {
            });
            tRegulationsConfirmSaveDTO.setRectificationHistoryList(rectificationHistoryList);
        }
        return tRegulationsConfirmSaveDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TRegulationsConfirmSaveDTO create(TRegulationsConfirmSaveDTO entityDTO) {
       TRegulationsConfirmSave tRegulationsConfirmSave =  BeanConvertUtils.copyProperties(entityDTO,TRegulationsConfirmSave.class);
        tRegulationsConfirmSave.setRectificationHistory(JSON.toJSONString(entityDTO.getRectificationHistoryList()));
        TRegulationsExamination regulationsExamination = regulationsExaminationService.getById(entityDTO.getExaminationId());
        tRegulationsConfirmSave.setReviewOpinions(regulationsExamination.getReviewOpinions());
        save(tRegulationsConfirmSave);
        regulationsExaminationProcessService.updateStatus(regulationsExamination,"I",2,null);
        return  BeanConvertUtils.copyProperties(tRegulationsConfirmSave,TRegulationsConfirmSaveDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TRegulationsConfirmSaveDTO entity) {
        // 按照index字段从小到大升序排序
        Collections.sort(entity.getRectificationHistoryList(), Comparator.comparingInt(RectificationHistory::getIndex));
        TRegulationsConfirmSave tRegulationsConfirmSave = BeanConvertUtils.copyProperties(entity,TRegulationsConfirmSave.class);
        TRegulationsExamination regulationsExamination = regulationsExaminationService.getById(entity.getExaminationId());
        if(entity.getRectificationHistoryList()!=null&&!entity.getRectificationHistoryList().isEmpty()) {
            List<RectificationHistory> rectificationHistoryList = entity.getRectificationHistoryList();
            RectificationHistory rectificationHistory = rectificationHistoryList.get(rectificationHistoryList.size() - 1);
            rectificationHistory.setRejectTime(LocalDateTime.now());
            rectificationHistory.setIndex(rectificationHistoryList.size());
            tRegulationsConfirmSave.setRectificationHistory(JSON.toJSONString(rectificationHistoryList));
        }
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        if(entity.getIsArchive()==1){
            regulationsExaminationProcessService.updateStatus(regulationsExamination,"I",3,companyName);
            //存档
            regulationsExaminationProcessService.updateStatusAllEnd(regulationsExamination,"J",3,null);
            // 推送给科创 需要放开
            bizService.pushCheckResultReplyFgs(tRegulationsConfirmSave.getExaminationId(),
                    "10",null);
            List<RectificationHistory> rectificationHistoryList = entity.getRectificationHistoryList();
            RectificationHistory rectificationHistory = rectificationHistoryList.get(rectificationHistoryList.size() - 1);
            rectificationHistory.setReplyType("10");
            rectificationHistory.setRejectTime(LocalDateTime.now());
            entity.setRectificationHistory(JSON.toJSONString(rectificationHistoryList));

        }else{ // 驳回/确认
            List<RectificationHistory> rectificationHistoryList = entity.getRectificationHistoryList();
            RectificationHistory rectificationHistory = rectificationHistoryList.get(rectificationHistoryList.size() - 1);
            if(StrUtil.equals(rectificationHistory.getReplyType(),"10")){
                // // 整改回复 完成
                regulationsExaminationProcessService.updateStatus(regulationsExamination,"I",3,companyName);
            }else{
                regulationsExaminationProcessService.updateStatus(regulationsExamination,"I",2,companyName);
            }
            // 推送给科创 需要放开
            bizService.pushCheckResultReplyFgs(tRegulationsConfirmSave.getExaminationId(),
               rectificationHistory.getReplyType(),rectificationHistory.getRejectReason());
        }

        return tRegulationsConfirmSaveMapper.updateById(tRegulationsConfirmSave);

    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tRegulationsConfirmSaveMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TRegulationsConfirmSaveId
     * @return
     */
    @Override
    public boolean existByTRegulationsConfirmSaveId(Long TRegulationsConfirmSaveId) {
        if (TRegulationsConfirmSaveId != null) {
            LambdaQueryWrapper<TRegulationsConfirmSave> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TRegulationsConfirmSave::getId, TRegulationsConfirmSaveId);
            List<TRegulationsConfirmSave> result = tRegulationsConfirmSaveMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TRegulationsConfirmSaveDTO> dataList) {
        for (TRegulationsConfirmSaveDTO tRegulationsConfirmSaveDTO : dataList) {
            tRegulationsConfirmSaveDTO.setRectificationHistory(JSON.toJSONString(tRegulationsConfirmSaveDTO.getRectificationHistoryList()));
        }
        List<TRegulationsConfirmSave> result = ListCopyUtil.copy(dataList, TRegulationsConfirmSave.class);
        return saveBatch(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recCheckReplyFgs(String paramJsonStr , MultipartFile[] files) {
        if (files == null || files.length < 1) {
            throw new BusinessException("90002:无法获取附件");
        }
        JSONObject jsonObject = JSON.parseObject(paramJsonStr);
        String recordId = (String) jsonObject.get("recordId");
        String content = (String) jsonObject.get("content");
        String orgName = (String) jsonObject.get("orgName");

        Long formDataId = SnowflakeIdUtil.getSnowFlakeLongId();
        List<TRegulationsExamination> tRegulationsExaminations = regulationsExaminationService.selectListNoAdd(new LambdaQueryWrapper<TRegulationsExamination>().eq(TRegulationsExamination::getRecordId , recordId));
        if (!tRegulationsExaminations.isEmpty()) {
            TRegulationsExamination tRegulationsExamination = tRegulationsExaminations.get(0);
            // 附件处理
            List<CscpEnclosureFile> fileList = regulationsExaminationService.annexHandler(files , formDataId,
                    tRegulationsExamination.getSecrecyGrade());
            CscpEnclosureFile file = fileList.get(0);

            List<TRegulationsConfirmSave> tRegulationsConfirmSaves = regulationsConfirmSaveService.selectListNoAdd(new LambdaQueryWrapper<TRegulationsConfirmSave>().eq(
                    TRegulationsConfirmSave::getExaminationId , tRegulationsExamination.getId()
            ));
            TRegulationsConfirmSave data =BeanConvertUtils.copyProperties(tRegulationsExamination,
                    TRegulationsConfirmSave.class);
            data.setExaminationId(tRegulationsExamination.getId());
            data.setId(null);
            if(tRegulationsConfirmSaves != null &&  !tRegulationsConfirmSaves.isEmpty() ){
                 data =tRegulationsConfirmSaves.get(0);
            }
            List<RectificationHistory> rectificationHistoryList = JSON.parseObject(data.getRectificationHistory(), new TypeReference<List<RectificationHistory>>() {});
            if(rectificationHistoryList == null){
                rectificationHistoryList = new ArrayList<>();
            }
            LocalDateTime now1 = LocalDateTime.now();
            RectificationHistory build = RectificationHistory.builder()
                    .index(rectificationHistoryList.size() + 1)
                    .rectificationContent(content)
                    .rectificationFileId(formDataId)
                    .rectificationFileName(file.getFileName())
                    .rectificationFilePath(file.getFileUrl())
                    .rectificationTime(now1)
                    .build();
            rectificationHistoryList.add(build);
            data.setRectificationHistory(JSON.toJSONString(rectificationHistoryList));
            data.setRectificationTime(now1);
            data.setIsRead(0);
            regulationsConfirmSaveService.saveOrUpdate(data);



            //更新流程节点 整改 增加流程节点
            List<TRegulationsExaminationProcess> regulationsExaminationProcess =
                    regulationsExaminationProcessService.selectBtExaminationIdNode(tRegulationsExamination.getId(), "I",
                            null );
            TRegulationsExaminationProcess regulationsExaminationProcess1 = BeanConvertUtils.copyProperties(regulationsExaminationProcess.get(0), TRegulationsExaminationProcess.class);
            regulationsExaminationProcess1.setId(null);
            // 需要 整改单位和时间
            regulationsExaminationProcess1.setNodeCompanyName(orgName);
            regulationsExaminationProcess1.setNodeUpdateTime(now1);
            regulationsExaminationProcess1.setNodeStatus(2);
            regulationsExaminationProcess1.setBusinessId(Long.valueOf(rectificationHistoryList.size() + 1));
            regulationsExaminationProcessService.save(regulationsExaminationProcess1);
            //删除空的初始化
            for (TRegulationsExaminationProcess examinationProcess : regulationsExaminationProcess) {
                if(examinationProcess.getBusinessId()==null){
                    regulationsExaminationProcessService.delete(examinationProcess.getId());
                    break;
                }
            }
        }


    }

    @Override
    public Boolean updateIsRead(Long id) {
        TRegulationsConfirmSave byId = this.getById(id);
        byId.setIsRead(1);
        return this.updateById(byId);
    }


}
