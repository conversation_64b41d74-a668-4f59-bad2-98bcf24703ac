package com.ctsi.regulations.service;

import com.ctsi.regulations.entity.dto.TRegulationsSolicitOpinionsDTO;
import com.ctsi.regulations.entity.TRegulationsSolicitOpinions;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 法规室征求意见记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface ITRegulationsSolicitOpinionsService extends SysBaseServiceI<TRegulationsSolicitOpinions> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsSolicitOpinionsDTO> queryListPage(TRegulationsSolicitOpinionsDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsSolicitOpinionsDTO> queryList(TRegulationsSolicitOpinionsDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TRegulationsSolicitOpinionsDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TRegulationsSolicitOpinionsDTO create(TRegulationsSolicitOpinionsDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TRegulationsSolicitOpinionsDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTRegulationsSolicitOpinionsId
     * @param code
     * @return
     */
    boolean existByTRegulationsSolicitOpinionsId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TRegulationsSolicitOpinionsDTO> dataList);


    /**
     * 更新为已读
     * @param examinationId
     * @return
     */
    Integer updateIsRead(Long examinationId);


    Boolean createBatchFromOneData(TRegulationsSolicitOpinionsDTO regulationsSolicitOpinionsDTO);
}
