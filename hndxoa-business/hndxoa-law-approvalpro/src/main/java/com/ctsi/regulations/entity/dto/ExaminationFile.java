package com.ctsi.regulations.entity.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExaminationFile {

    private Long fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件类型
     * 参考: FileTypeEnum
     *  "10" 备案报告;  "20" 备案说明 ; "30" 正式文本
     */
    private String fileType;
    /**
     * 文件URL
     */
    private String fileUrl;


    public enum FileTypeEnum {

        BABG("10","备案报告"),
        BASM("20","备案说明"),
        ZSWB("30","正式文本"),


        ;

        String code;

        String message;

        FileTypeEnum(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public String getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }
    }

//    public static void main(String[] args) {
//        List<ExaminationFile> fileList = new ArrayList<>();
//
//        ExaminationFile file1 = new ExaminationFile();
//        file1.setFileName("任务一览表.xlsx");
//        file1.setFileUrl("2024/upload/1697072420466016257/1697170157779361794/3/30/1773908116245606401.xlsx");
//
//        ExaminationFile file2 = new ExaminationFile();
//        file2.setFileName("任务一览表2.xlsx");
//        file2.setFileUrl("2024/upload/1697072420466016257/1697170157779361794/3/30/1773908116245606401.xlsx");
//
//        fileList.add(file1);
//        fileList.add(file2);
//
//        String jsonString = JSON.toJSONString(fileList);
//        System.out.println(jsonString);
//    }


}
