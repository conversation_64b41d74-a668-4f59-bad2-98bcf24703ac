package com.ctsi.regulations.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * 法规室整改确认驳回和存档
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_regulations_confirm_save")
@ApiModel(value="TRegulationsConfirmSave对象", description="法规室整改确认驳回和存档")
public class TRegulationsConfirmSave extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 法规室审查表ID
     */
    @ApiModelProperty(value = "法规室审查表ID")
    private Long examinationId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String fileNumber;

    /**
     * 整改时间
     */
    @ApiModelProperty(value = "整改时间")
    private LocalDateTime rectificationTime;

    /**
     * 整改历史
     */
    @ApiModelProperty(value = "整改历史(JSONString)")
    private String rectificationHistory;

    /**
     * 存档时间
     */
    @ApiModelProperty(value = "存档时间")
    private LocalDateTime archiveTime;

    /**
     * 是否已存档
     */
    @ApiModelProperty(value = "是否已存档")
    private Integer isArchive;

    /**
     * 报备时间
     */
    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportingTime;


    /**
     * 报备单位名称
     */
    @ApiModelProperty(value = "报备单位名称")
    private String reportingUnitName;

    /**
     * 审查意见
     */
    @ApiModelProperty(value = "审查意见 0初审中， 1直接予以备案通过、2予以备案通过并提出建议、3予以备案通过并告知、4予以备案通过并出面提醒、5纠正、6对主动纠正后重报件予以备案通过，7其他")
    private Integer reviewOpinions;

    /**
     *整改反馈已读未读
     */
    @ApiModelProperty(value = "整改反馈已读未读,0未读，1已读")
    private Integer isRead;

    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;
}
