package com.ctsi.regulations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsHandle;
import com.ctsi.regulations.entity.TRegulationsProblemDescription;
import com.ctsi.regulations.entity.TRegulationsSolicitOpinions;
import com.ctsi.regulations.entity.dto.ExaminationFile;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import com.ctsi.regulations.entity.dto.TRegulationsHandleDTO;
import com.ctsi.regulations.mapper.TRegulationsHandleMapper;
import com.ctsi.regulations.service.*;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 法规室审查处理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Slf4j
@Service
public class TRegulationsHandleServiceImpl extends SysBaseServiceImpl<TRegulationsHandleMapper, TRegulationsHandle> implements ITRegulationsHandleService {

    @Autowired
    private TRegulationsHandleMapper tRegulationsHandleMapper;

    @Autowired
    private ITRegulationsExaminationService regulationsExaminationService;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private ITRegulationsProblemDescriptionService regulationsProblemDescriptionService;

    @Autowired
    private ITRegulationsSolicitOpinionsService regulationsSolicitOpinionsService;

    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private ITRegulationsExaminationProcessService regulationsExaminationProcessService;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;
    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TRegulationsHandleDTO> queryListPage(TRegulationsHandleDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TRegulationsHandle> queryWrapper = new LambdaQueryWrapper();
        if(com.ctsi.hndx.utils.StringUtils.isNotEmpty(entityDTO.getFileName())){
            queryWrapper.like(TRegulationsHandle::getFileName,entityDTO.getFileName());
        }
        if(entityDTO.getStartTime()!=null&&entityDTO.getEndTime()!=null){
            queryWrapper.between(TRegulationsHandle::getReportingTime,entityDTO.getStartTime(),entityDTO.getEndTime());
        }
        if(entityDTO.getBpmStatus()!=null){
            queryWrapper.eq(TRegulationsHandle::getBpmStatus,entityDTO.getBpmStatus());
        }
        queryWrapper.isNotNull(TRegulationsHandle::getProcessInstanceId);
        queryWrapper.orderByAsc(TRegulationsHandle::getBpmStatus);
        queryWrapper.orderByAsc(TRegulationsHandle::getIsOfficialDocuments);
        queryWrapper.orderByDesc(TRegulationsHandle::getCreateTime);
//        queryWrapper.eq(TRegulationsHandle::getIsOfficialDocuments,1);

        IPage<TRegulationsHandle> pageData = tRegulationsHandleMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TRegulationsHandleDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsHandleDTO.class));
        for (TRegulationsHandleDTO record : data.getRecords()) {
            LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,record.getProcessInstanceId());
            ProcessAssignee processAssignee = processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
            if(processAssignee!=null) {
                record.setCurrentStage(processAssignee.getNodeName());
                record.setCurrentUserName(processAssignee.getAssigneeName());
            }

            TaskVO processInfoByInstanceId = cscpProcBaseRepository.getProcessInfoByInstanceId(String.valueOf(record.getProcessInstanceId()));
            if(processInfoByInstanceId!=null) {
                record.setBpmStatus(processInfoByInstanceId.getBpmStatus());
            }
            record.setTaskVO(processInfoByInstanceId);
        }


        return new PageResult<TRegulationsHandleDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TRegulationsHandleDTO> queryList(TRegulationsHandleDTO entityDTO) {
        LambdaQueryWrapper<TRegulationsHandle> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(TRegulationsHandle::getIsOfficialDocuments,1);
            List<TRegulationsHandle> listData = tRegulationsHandleMapper.selectList(queryWrapper);
            List<TRegulationsHandleDTO> TRegulationsHandleDTOList = ListCopyUtil.copy(listData, TRegulationsHandleDTO.class);
        return TRegulationsHandleDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TRegulationsHandleDTO findOne(Long id) {
        TRegulationsHandle  tRegulationsHandle =  tRegulationsHandleMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tRegulationsHandle,TRegulationsHandleDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TRegulationsHandleDTO create(TRegulationsHandleDTO entityDTO) {
       TRegulationsHandle tRegulationsHandle =  BeanConvertUtils.copyProperties(entityDTO,TRegulationsHandle.class);
        save(tRegulationsHandle);
        return  BeanConvertUtils.copyProperties(tRegulationsHandle,TRegulationsHandleDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TRegulationsHandleDTO entity) {
        TRegulationsHandle tRegulationsHandle = BeanConvertUtils.copyProperties(entity,TRegulationsHandle.class);
        return tRegulationsHandleMapper.updateById(tRegulationsHandle);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tRegulationsHandleMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TRegulationsHandleId
     * @return
     */
    @Override
    public boolean existByTRegulationsHandleId(Long TRegulationsHandleId) {
        if (TRegulationsHandleId != null) {
            LambdaQueryWrapper<TRegulationsHandle> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TRegulationsHandle::getId, TRegulationsHandleId);
            List<TRegulationsHandle> result = tRegulationsHandleMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TRegulationsHandleDTO> dataList) {
        List<TRegulationsHandle> result = ListCopyUtil.copy(dataList, TRegulationsHandle.class);
        return saveBatch(result);
    }

    @Override
    public TRegulationsHandle batchSubmittals(List<TRegulationsExaminationDTO> regulationsExaminationDTOList) {
        Long id = Long.valueOf(SnowflakeIdUtil.getSnowFlakeId());
        TRegulationsHandle result=new TRegulationsHandle();
        result.setId(id);
        LocalDate now = LocalDate.now();
        result.setReportingTime(now);
        String companyName1 = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();

        List<Long> examinationIdList=new ArrayList<>();
        //报备单位ID
        List<Long> companyIdList=new ArrayList<>();
        //【正文】规范性文件备案审查文件
        List<String> fileNameList=new ArrayList<>();
        //报备单位名称
        List<String> companyNameList=new ArrayList<>();
        List<CscpEnclosureFile> mainFileList=new ArrayList<>();
        for (TRegulationsExaminationDTO tRegulationsExaminationDTO : regulationsExaminationDTOList) {
            examinationIdList.add(tRegulationsExaminationDTO.getId());
            companyIdList.add(tRegulationsExaminationDTO.getReportingUnitId());
            companyNameList.add(tRegulationsExaminationDTO.getReportingUnitName());
            for (ExaminationFile examinationFile : tRegulationsExaminationDTO.getExaminationFileList()) {
                if("30".equals(examinationFile.getFileType())) {
                    //第一个压缩包只存正文
                    int indexOf = regulationsExaminationDTOList.indexOf(tRegulationsExaminationDTO);
                    fileNameList.add(indexOf+1+"、"+examinationFile.getFileName());
                    CscpEnclosureFile mainFile = cscpEnclosureFileService.getById(examinationFile.getFileId());
                    mainFileList.add(mainFile);
                }
            }
            tRegulationsExaminationDTO.setCurrentStage("G");
            TRegulationsExamination regulationsExamination = regulationsExaminationService.getById(tRegulationsExaminationDTO.getId());
            regulationsExaminationProcessService.updateStatus(regulationsExamination,"D",3,null);
            if(regulationsExamination.getSolicitOpinionsId()==null) {
                if(!StringUtils.isEmpty(regulationsExamination.getSkipStatus())) {
                    List<String> list = Arrays.asList(regulationsExamination.getSkipStatus().split(","));
                    Set<String> skipSet = new HashSet<>(list);
                    skipSet.add("E");
                    String skipString = String.join(",", skipSet);
                    regulationsExamination.setSkipStatus(skipString);
                }else{
                    regulationsExamination.setSkipStatus("E");
                }
                regulationsExaminationProcessService.updateStatus(regulationsExamination, "E", 3, null);

            }
            if(regulationsExamination.getProblemDescriptionId()==null) {
                if(!StringUtils.isEmpty(regulationsExamination.getSkipStatus())) {
                    List<String> list = Arrays.asList(regulationsExamination.getSkipStatus().split(","));
                    Set<String> skipSet = new HashSet<>(list);
                    skipSet.add("F");
                    String skipString = String.join(",", skipSet);
                    regulationsExamination.setSkipStatus(skipString);
                }else{
                    regulationsExamination.setSkipStatus("F");
                }
                regulationsExaminationProcessService.updateStatus(regulationsExamination, "F", 3, null);
            }
            regulationsExaminationProcessService.updateStatus(regulationsExamination,"G",3,companyName1);
        }
        StringBuilder reviewOpinionsText=new StringBuilder("根据《中国共产党党内法规和规范性文件备案审查规定》，我室对");
        for (String s : companyNameList) {
            reviewOpinionsText.append(s);
            if(companyNameList.size()>1){
                reviewOpinionsText.append("、");
            }
        }
        reviewOpinionsText.append("等").append(companyNameList.size()).append("个市委报省委备案审查的").append(fileNameList.size())
                .append("个党内规范性文件，从内容和形式上进行了认真审查，提出了直接予以备案通过，对主动纠正后重报件予以备案通过（审查情况说明附后）的处理意见，备案结果拟通过省党内法规业务平台及时反馈。妥否？");
        result.setReviewOpinionsText(reviewOpinionsText.toString());
        result.setReportCompanyName(companyNameList.get(0)+"等"+companyNameList.size()+"个市委");
        if(companyIdList!=null&&!companyIdList.isEmpty()) {
            result.setReportCompanyId(org.apache.commons.lang3.StringUtils.strip(companyIdList.toString(), "[]").replaceAll(" ", ""));
        }
        result.setExaminationId(org.apache.commons.lang3.StringUtils.strip(examinationIdList.toString(),"[]").replaceAll(" ",""));
        result.setFileName("报省委备案审查的"+regulationsExaminationDTOList.size()+"个党内规范性文件（详见文件目录）");
        CscpEnclosureFile zipFileMain = cscpEnclosureFileService.zip(mainFileList,"规范性文件备案审查文件",id);
        result.setMainTestFileId(zipFileMain.getFormDataId());
        result.setMainTestFileName(zipFileMain.getFileName());
        result.setMainTestFilePath(zipFileMain.getFileUrl());
        //报省委备案审查的文件目录
        if(!fileNameList.isEmpty()) {
            Map<String, String> placeholders=new HashMap<>();
            String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
            if(StringUtils.isEmpty(companyName)){
                companyName="";
            }
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
            String nowDate = currentDate.format(formatter);
            placeholders.put("{{日期}}", nowDate);
            placeholders.put("{{单位}}", companyName);
            String wjlb="";
            for (String s : fileNameList) {
                wjlb=wjlb+s+"\n";
            }
            placeholders.put("{{文件列表}}", wjlb);
            // 读取模板文件作为资源流
            Resource resource = new ClassPathResource("/templates/wjml.docx");
            InputStream inputStream = null;
            try {
                inputStream = resource.getInputStream();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            // 生成文档
            XWPFDocument document = regulationsExaminationService.generateDocx(inputStream, placeholders);
            byte[] bytes;
            try {
                 bytes = convertDocumentToByteArray(document);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            String fileFolderUrl = fileStoreTemplateService.createFileUrl(FileBasePathName.UPLOAD, ".docx");
            boolean uploaded = fileStoreTemplateService.uploadFile(fileFolderUrl, bytes);
            if(uploaded){
                CscpDocumentFile fileFolder = new CscpDocumentFile();
                fileFolder.setFormDataId(id);
                fileFolder.setFileName("报省委备案审查的文件目录.docx");
                fileFolder.setFileUrl(fileFolderUrl);
                fileFolder.setExtName("docx");
                fileFolder.setFileSize((long) bytes.length);
                fileFolder.setSecurityClassified("公开");
                fileFolder.setSecurityClassifiedCode(0);
                cscpDocumentFileService.save(fileFolder);
                result.setFileDirectoryFileId(fileFolder.getFormDataId());
                result.setFileDirectoryFileName("报省委备案审查的文件目录.docx");
                result.setFileDirectoryFilePath(fileFolderUrl);
            }
        }
        //【附件】文件的建议单、告知单、纠正函等.zip
        List<CscpEnclosureFile> annexFileList=new ArrayList<>();
        boolean allPass=true;
        for (TRegulationsExaminationDTO tRegulationsExaminationDTO : regulationsExaminationDTOList) {
            if(!tRegulationsExaminationDTO.getReviewOpinions().toString().equals("1")){
                allPass=false;
            }
            //查询征求意见的附件
            if(tRegulationsExaminationDTO.getSolicitOpinionsId()!=null) {
                List<String> solicitOpinionsIdList = Arrays.asList(tRegulationsExaminationDTO.getSolicitOpinionsId().split(","));
                List<TRegulationsSolicitOpinions> regulationsSolicitOpinions = regulationsSolicitOpinionsService.getBaseMapper().selectBatchIds(solicitOpinionsIdList);
                for (TRegulationsSolicitOpinions regulationsSolicitOpinion : regulationsSolicitOpinions) {
                    List<CscpEnclosureFile> approvalFiles = cscpEnclosureFileService.getFormFiles(regulationsSolicitOpinion.getApprovalFormFileId());
                    if (!approvalFiles.isEmpty()) {
                        annexFileList.addAll(approvalFiles);
                    }
                    List<CscpEnclosureFile> commentLetterFiles = cscpEnclosureFileService.getFormFiles(regulationsSolicitOpinion.getCommentLetterFileId());
                    if (!commentLetterFiles.isEmpty()) {
                        annexFileList.addAll(commentLetterFiles);
                    }
                    List<CscpEnclosureFile> feedbackFiles = cscpEnclosureFileService.getFormFiles(regulationsSolicitOpinion.getFeedbackFileId());
                    if (!feedbackFiles.isEmpty()) {
                        annexFileList.addAll(feedbackFiles);
                    }
                }
            }
            //查询问题说明
            if (tRegulationsExaminationDTO.getProblemDescriptionId() != null) {
                List<String> problemDescriptionList = Arrays.asList(tRegulationsExaminationDTO.getProblemDescriptionId().split(","));
                List<TRegulationsProblemDescription> tRegulationsProblemDescriptions = regulationsProblemDescriptionService.getBaseMapper().selectBatchIds(problemDescriptionList);
                for (TRegulationsProblemDescription tRegulationsProblemDescription : tRegulationsProblemDescriptions) {
                    List<CscpEnclosureFile> annexFiles = cscpEnclosureFileService.getFormFiles(tRegulationsProblemDescription.getFileId());
                    if(!annexFiles.isEmpty()){
                        annexFileList.addAll(annexFiles);
                    }
                    List<CscpEnclosureFile> descriptionFiles = cscpEnclosureFileService.getFormFiles(tRegulationsProblemDescription.getDescriptionFileId());
                    if(!descriptionFiles.isEmpty()){
                        annexFileList.addAll(descriptionFiles);
                    }
                }
            }
        }
        if(allPass==false) {
            CscpEnclosureFile zipFileAnnex = cscpEnclosureFileService.zip(mainFileList, "文件的建议单、告知单、纠正函等", id);
            result.setAnnexFileId(zipFileAnnex.getFormDataId());
            result.setAnnexFileName(zipFileAnnex.getFileName());
            result.setAnnexFilePath(zipFileAnnex.getFileUrl());
        }
//        this.save(result);
        return result;


    }

    public static byte[] convertDocumentToByteArray(XWPFDocument document) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            // 将XWPFDocument写入ByteArrayOutputStream
            document.write(byteArrayOutputStream);
            // 将ByteArrayOutputStream转换为byte数组
            return byteArrayOutputStream.toByteArray();
        } finally {
            // 关闭ByteArrayOutputStream
            byteArrayOutputStream.close();
        }
    }
}
