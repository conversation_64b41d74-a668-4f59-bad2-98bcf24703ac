package com.ctsi.regulations.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.business.domain.CscpAuditContent;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.service.CscpAuditContentService;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsExaminationProcess;
import com.ctsi.regulations.entity.TRegulationsHandle;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationProcessDTO;
import com.ctsi.regulations.mapper.TRegulationsExaminationMapper;
import com.ctsi.regulations.mapper.TRegulationsExaminationProcessMapper;
import com.ctsi.regulations.mapper.TRegulationsHandleMapper;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 法规室审查表流程记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Slf4j
@Service
public class TRegulationsExaminationProcessServiceImpl extends SysBaseServiceImpl<TRegulationsExaminationProcessMapper, TRegulationsExaminationProcess> implements ITRegulationsExaminationProcessService {

    @Autowired
    private TRegulationsExaminationProcessMapper tRegulationsExaminationProcessMapper;

    @Autowired
    private TRegulationsExaminationMapper regulationsExaminationMapper;

    @Autowired
    TRegulationsHandleMapper regulationsHandleMapper;

    @Autowired
    CscpAuditContentService cscpAuditContentService;

    @Autowired
    CscpProcBaseService cscpProcBaseService;

    private List<String> stringList;

    TRegulationsExaminationProcessServiceImpl(){
        stringList = new ArrayList<>();
//        stringList.add("A");
        stringList.add("B");
        stringList.add("C");
        stringList.add("D");
        stringList.add("E");
        stringList.add("F");
        stringList.add("G");
        stringList.add("H");
        stringList.add("I");
        stringList.add("J");


        //A备案审查规则
        //B报备
        //C登记
        //D审查
        //E征求意见
        //F问题说明
        //G处理
        //H报批
        //I反馈整改情况
        //J归档
    }


    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TRegulationsExaminationProcessDTO> queryListPage(TRegulationsExaminationProcessDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TRegulationsExaminationProcess> queryWrapper = new LambdaQueryWrapper();

        IPage<TRegulationsExaminationProcess> pageData = tRegulationsExaminationProcessMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TRegulationsExaminationProcessDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsExaminationProcessDTO.class));

        return new PageResult<TRegulationsExaminationProcessDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TRegulationsExaminationProcessDTO> queryList(TRegulationsExaminationProcessDTO entityDTO) {
        LambdaQueryWrapper<TRegulationsExaminationProcess> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TRegulationsExaminationProcess::getExaminationId,entityDTO.getExaminationId());
        queryWrapper.orderByAsc(TRegulationsExaminationProcess::getNode,TRegulationsExaminationProcess::getBusinessId,TRegulationsExaminationProcess::getRemark);
        List<TRegulationsExaminationProcess> listData = tRegulationsExaminationProcessMapper.selectListNoAdd(queryWrapper);
        List<TRegulationsExaminationProcessDTO> TRegulationsExaminationProcessDTOList = ListCopyUtil.copy(listData, TRegulationsExaminationProcessDTO.class);

        //查询流程办理详情
        LambdaQueryWrapper<TRegulationsHandle> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(TRegulationsHandle::getExaminationId, entityDTO.getExaminationId())
                .orderByDesc(TRegulationsHandle::getCreateTime)
                .last("LIMIT 1");
        TRegulationsHandle tRegulationsHandle = regulationsHandleMapper.selectOneNoAdd(lambdaQueryWrapper);
        List<TRegulationsExaminationProcessDTO> addList=new ArrayList<>();
        if(tRegulationsHandle!=null){
            LambdaQueryWrapper<CscpAuditContent> cscpAuditContentLambdaQueryWrapper=new LambdaQueryWrapper<>();
            cscpAuditContentLambdaQueryWrapper.eq(CscpAuditContent::getBusinessId,tRegulationsHandle.getId());
            cscpAuditContentLambdaQueryWrapper.orderByAsc(CscpAuditContent::getCreateTime);
            List<CscpAuditContent> cscpAuditContents = cscpAuditContentService.selectListNoAdd(cscpAuditContentLambdaQueryWrapper);
            int indexOf=0;
            CscpProcBase cscpProcBase=null;
            if(!cscpAuditContents.isEmpty()){
                for (TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO : TRegulationsExaminationProcessDTOList) {
                    if("H".equals(tRegulationsExaminationProcessDTO.getNode())){
                        indexOf = TRegulationsExaminationProcessDTOList.indexOf(tRegulationsExaminationProcessDTO);
                        for (int i = 0; i < cscpAuditContents.size(); i++) {
                            CscpAuditContent cscpAuditContent = cscpAuditContents.get(i);
                            if(i==0){
                                LambdaQueryWrapper<CscpProcBase> cscpProcBaseLambdaQueryWrapper=new LambdaQueryWrapper<>();
                                cscpProcBaseLambdaQueryWrapper.eq(CscpProcBase::getProcInstId,cscpAuditContent.getProcInstId());
                                cscpProcBase = cscpProcBaseService.selectOneNoAdd(cscpProcBaseLambdaQueryWrapper);
                                tRegulationsExaminationProcessDTO.setNodeCompanyName(cscpAuditContent.getAuditorName()+" "+cscpAuditContent.getActName());
                                if(cscpProcBase!=null&&cscpProcBase.getBpmStatus()==3){
                                    tRegulationsExaminationProcessDTO.setNodeStatus(3);
                                }else {
                                    tRegulationsExaminationProcessDTO.setNodeStatus(2);
                                }
                                tRegulationsExaminationProcessDTO.setNodeUpdateTime(cscpAuditContent.getCreateTime());
                            }else {
                                TRegulationsExaminationProcessDTO tRegulationsExaminationProcessDTO1 = BeanConvertUtils.copyProperties(tRegulationsExaminationProcessDTO, TRegulationsExaminationProcessDTO.class);
                                tRegulationsExaminationProcessDTO.setNodeCompanyName(cscpAuditContent.getAuditorName()+" "+cscpAuditContent.getActName());
                                if(cscpProcBase!=null&&cscpProcBase.getBpmStatus()==3){
                                    tRegulationsExaminationProcessDTO.setNodeStatus(3);
                                }else {
                                    tRegulationsExaminationProcessDTO.setNodeStatus(2);
                                }
                                tRegulationsExaminationProcessDTO.setNodeUpdateTime(cscpAuditContent.getCreateTime());
                                addList.add(tRegulationsExaminationProcessDTO1);
                            }
                        }

                    }
                }
                TRegulationsExaminationProcessDTOList.addAll(indexOf,addList);
            }

        }


        return TRegulationsExaminationProcessDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TRegulationsExaminationProcessDTO findOne(Long id) {
        TRegulationsExaminationProcess  tRegulationsExaminationProcess =  tRegulationsExaminationProcessMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tRegulationsExaminationProcess,TRegulationsExaminationProcessDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TRegulationsExaminationProcessDTO create(TRegulationsExaminationProcessDTO entityDTO) {
       TRegulationsExaminationProcess tRegulationsExaminationProcess =  BeanConvertUtils.copyProperties(entityDTO,TRegulationsExaminationProcess.class);
        save(tRegulationsExaminationProcess);
        return  BeanConvertUtils.copyProperties(tRegulationsExaminationProcess,TRegulationsExaminationProcessDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TRegulationsExaminationProcessDTO entity) {
        TRegulationsExaminationProcess tRegulationsExaminationProcess = BeanConvertUtils.copyProperties(entity,TRegulationsExaminationProcess.class);
        return tRegulationsExaminationProcessMapper.updateById(tRegulationsExaminationProcess);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tRegulationsExaminationProcessMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TRegulationsExaminationProcessId
     * @return
     */
    @Override
    public boolean existByTRegulationsExaminationProcessId(Long TRegulationsExaminationProcessId) {
        if (TRegulationsExaminationProcessId != null) {
            LambdaQueryWrapper<TRegulationsExaminationProcess> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TRegulationsExaminationProcess::getId, TRegulationsExaminationProcessId);
            List<TRegulationsExaminationProcess> result = tRegulationsExaminationProcessMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TRegulationsExaminationProcessDTO> dataList) {
        List<TRegulationsExaminationProcess> result = ListCopyUtil.copy(dataList, TRegulationsExaminationProcess.class);
        return saveBatch(result);
    }

    /**
     * 初始化生成所有节点
     * @param regulationsExamination
     * @param entityDTO
     * @return
     */
    @Override
    public Integer initProcess(TRegulationsExamination regulationsExamination) {
        LambdaQueryWrapper<TRegulationsExaminationProcess> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getExaminationId, regulationsExamination.getId());
        lambdaQueryWrapper.in(TRegulationsExaminationProcess::getNode, stringList);
        List<TRegulationsExaminationProcess> tRegulationsExaminationProcesses = tRegulationsExaminationProcessMapper.selectListNoAdd(lambdaQueryWrapper);
        List<String> haveNode = new ArrayList<>();
        for (TRegulationsExaminationProcess tRegulationsExaminationProcess : tRegulationsExaminationProcesses) {
            haveNode.add(tRegulationsExaminationProcess.getNode());
        }
        List<TRegulationsExaminationProcess> initList = new ArrayList<>();
        for (int i = 0; i < stringList.size(); i++) {
            if (haveNode.contains(stringList.get(i))) {
                continue;
            }
            TRegulationsExaminationProcess regulationsExaminationProcess = new TRegulationsExaminationProcess();
            regulationsExaminationProcess.setName("关于印发"+regulationsExamination.getFileName()+"备案审查");
            regulationsExaminationProcess.setExaminationId(regulationsExamination.getId());
            regulationsExaminationProcess.setNode(stringList.get(i));
            regulationsExaminationProcess.setNodeStatus(1);
            // 初始化 B C
            if(StrUtil.equals(stringList.get(i),"B") || StrUtil.equals(stringList.get(i),"C")  ){
                regulationsExaminationProcess.setNodeStatus(3);
            }
            if(StrUtil.equals(stringList.get(i),"B")  ){
                regulationsExaminationProcess.setNodeCompanyName(regulationsExamination.getReportingUnitName());
                regulationsExaminationProcess.setNodeUpdateTime(regulationsExamination.getReportingTime());
            }

            if( StrUtil.equals(stringList.get(i),"C")  ){
                regulationsExaminationProcess.setNodeCompanyName(regulationsExamination.getRegisterUnitName());
                regulationsExaminationProcess.setNodeUpdateTime(regulationsExamination.getRegisterTime());

                TRegulationsExaminationProcess tRegulationsExaminationProcess = BeanConvertUtils.copyProperties(regulationsExaminationProcess , TRegulationsExaminationProcess.class);
                tRegulationsExaminationProcess.setNodeCompanyName(regulationsExamination.getRegisterComfirmUnitName());
                tRegulationsExaminationProcess.setNodeUpdateTime(regulationsExamination.getRegisterComfirmTime());
                initList.add(tRegulationsExaminationProcess);
            }
            if( StrUtil.equals(stringList.get(i),"D")  ) {
                regulationsExaminationProcess.setNodeStatus(2);
            }
            initList.add(regulationsExaminationProcess);
        }
        if (!initList.isEmpty()) {
            saveBatch(initList);
            return initList.size();
        } else {
            return 0;
        }
    }

    @Override
    public boolean updateStatus(TRegulationsExamination regulationsExamination,String node, Integer status, String orgName) {
        List<TRegulationsExaminationProcess> regulationsExaminationProcess = this.selectBtExaminationIdNode(regulationsExamination.getId(), node,null);
        if(regulationsExaminationProcess==null){
            return false;
        }
        for (TRegulationsExaminationProcess examinationProcess : regulationsExaminationProcess) {
            if(examinationProcess.getNodeStatus()==3){
                continue;
            }

            examinationProcess.setNodeUpdateTime(LocalDateTime.now());
            examinationProcess.setNodeStatus(status);
//            if(cscpOrg!=null) {
//                examinationProcess.setNodeCompanyId(cscpOrg.getId());
            if(orgName!=null) {
                examinationProcess.setNodeCompanyName(orgName);
            }
//            }
            this.updateById(examinationProcess);
        }

//        if (status == 3) {
//            int indexOf = stringList.indexOf(regulationsExaminationProcess.getNode());
//            if (indexOf < stringList.size()) {
//                if (StringUtils.isNotEmpty(regulationsExamination.getSkipStatus())) {
//                    String[] split = regulationsExamination.getSkipStatus().split(",");
//                    List<String> skipList = Arrays.asList(split);
//                    for (int i = indexOf + 1; i < stringList.size(); i++) {
//                        if (!skipList.contains(i)) {
//                            regulationsExamination.setCurrentStage(stringList.get(i));
//                            break;
//                        }
//                    }
//                } else {
//                    for (int i = indexOf + 1; i < stringList.size(); i++) {
//                        regulationsExamination.setCurrentStage(stringList.get(i));
//                        break;
//                    }
//                }
//            }
//        } else {
            if(node.charAt(0)>regulationsExamination.getCurrentStage().charAt(0)) {
                regulationsExamination.setCurrentStage(node);
                regulationsExaminationMapper.updateById(regulationsExamination);
            }
//        }

        return true;
    }

    @Override
    public boolean updateStatus(TRegulationsExamination regulationsExamination, String node, Integer status, String orgName, boolean isStart) {
        List<TRegulationsExaminationProcess> regulationsExaminationProcess = this.selectBtExaminationIdNode(regulationsExamination.getId(), node,null);
        if(regulationsExaminationProcess==null){
            return false;
        }
        for (TRegulationsExaminationProcess examinationProcess : regulationsExaminationProcess) {
            if(examinationProcess.getNodeStatus()==3){
                continue;
            }
            if(isStart&&examinationProcess.getNodeStatus()==1){
                continue;
            }
            examinationProcess.setNodeUpdateTime(LocalDateTime.now());
            examinationProcess.setNodeStatus(status);
            if(orgName!=null) {
                examinationProcess.setNodeCompanyName(orgName);
            }
            this.updateById(examinationProcess);
        }
        if(node.charAt(0)>regulationsExamination.getCurrentStage().charAt(0)) {
            regulationsExamination.setCurrentStage(node);
            regulationsExaminationMapper.updateById(regulationsExamination);
        }

        return true;
    }

    @Override
    public boolean updateStatus(TRegulationsExamination regulationsExamination, String node, Integer status, String orgName, Long businessId,String remark) {
        LambdaQueryWrapper<TRegulationsExaminationProcess> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getExaminationId,regulationsExamination.getId());
        lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getNode,node);
        if(remark!=null){
            lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getRemark,remark);
        }

        if(businessId!=null){
            lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getBusinessId,businessId);
        }

        List<TRegulationsExaminationProcess> regulationsExaminationProcess = this.selectListNoAdd(lambdaQueryWrapper);
        if (regulationsExaminationProcess == null) {
            return false;
        }
        for (TRegulationsExaminationProcess examinationProcess : regulationsExaminationProcess) {
            examinationProcess.setNodeUpdateTime(LocalDateTime.now());
            examinationProcess.setNodeStatus(status);
            if (orgName != null) {
                examinationProcess.setNodeCompanyName(orgName);
            }
            this.updateById(examinationProcess);
        }
        if(node.charAt(0)>regulationsExamination.getCurrentStage().charAt(0)) {
            regulationsExamination.setCurrentStage(node);
            regulationsExaminationMapper.updateById(regulationsExamination);
        }

        return true;
    }

    @Override
    public boolean updateStatusAllEnd(TRegulationsExamination regulationsExamination, String node, Integer status, String orgName) {
        LambdaQueryWrapper<TRegulationsExaminationProcess> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getExaminationId,regulationsExamination.getId());
        List<TRegulationsExaminationProcess> tRegulationsExaminationProcesses = this.selectListNoAdd(lambdaQueryWrapper);
        LocalDateTime now = LocalDateTime.now();
        for (TRegulationsExaminationProcess tRegulationsExaminationProcess : tRegulationsExaminationProcesses) {
            if(tRegulationsExaminationProcess.getNodeStatus()==3){
                continue;
            }

            if("J".equals(tRegulationsExaminationProcess.getNode())){
                tRegulationsExaminationProcess.setNodeUpdateTime(now);
                tRegulationsExaminationProcess.setNodeStatus(3);
            }
            if(tRegulationsExaminationProcess.getNodeStatus()!=1){
                tRegulationsExaminationProcess.setNodeStatus(3);
            }
        }
        regulationsExamination.setCurrentStage(node);
        regulationsExaminationMapper.updateById(regulationsExamination);
        this.updateBatchById(tRegulationsExaminationProcesses);
        return true;
    }

    @Override
    public List<TRegulationsExaminationProcess> selectBtExaminationIdNode(Long examinationId, String node,Long businessId) {
        LambdaQueryWrapper<TRegulationsExaminationProcess> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getExaminationId,examinationId);
        lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getNode,node);
        if(businessId!=null){
            lambdaQueryWrapper.eq(TRegulationsExaminationProcess::getBusinessId,businessId);
        }
        return this.selectListNoAdd(lambdaQueryWrapper);
    }


}
