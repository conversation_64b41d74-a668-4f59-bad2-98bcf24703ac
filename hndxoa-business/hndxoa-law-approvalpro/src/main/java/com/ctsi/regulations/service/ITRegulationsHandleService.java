package com.ctsi.regulations.service;

import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import com.ctsi.regulations.entity.dto.TRegulationsHandleDTO;
import com.ctsi.regulations.entity.TRegulationsHandle;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 法规室审查处理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
public interface ITRegulationsHandleService extends SysBaseServiceI<TRegulationsHandle> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TRegulationsHandleDTO> queryListPage(TRegulationsHandleDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TRegulationsHandleDTO> queryList(TRegulationsHandleDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TRegulationsHandleDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TRegulationsHandleDTO create(TRegulationsHandleDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TRegulationsHandleDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTRegulationsHandleId
     * @param code
     * @return
     */
    boolean existByTRegulationsHandleId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TRegulationsHandleDTO> dataList);


    /**
     * 批量送审
     *
     * create batch
     * @param regulationsExaminationDTOList
     * @return
     */
    TRegulationsHandle batchSubmittals(List<TRegulationsExaminationDTO> regulationsExaminationDTOList);
}
