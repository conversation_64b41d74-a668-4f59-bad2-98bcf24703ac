package com.ctsi.regulations.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.enums.DocumentFormat;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.*;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.regulations.entity.TRegulationsConfirmSave;
import com.ctsi.regulations.entity.TRegulationsExamination;
import com.ctsi.regulations.entity.TRegulationsProblemDescription;
import com.ctsi.regulations.entity.TRegulationsSolicitOpinions;
import com.ctsi.regulations.entity.dto.ExaminationFile;
import com.ctsi.regulations.entity.dto.TRegulationsExaminationDTO;
import com.ctsi.regulations.mapper.*;
import com.ctsi.regulations.service.ITRegulationsExaminationProcessService;
import com.ctsi.regulations.service.ITRegulationsExaminationService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.repository.BizApprovalManagementMapper;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 法规室审查表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Slf4j
@Service
public class TRegulationsExaminationServiceImpl extends SysBaseServiceImpl<TRegulationsExaminationMapper, TRegulationsExamination> implements ITRegulationsExaminationService {

    @Autowired
    private TRegulationsExaminationMapper tRegulationsExaminationMapper;

    @Autowired
    private TRegulationsProblemDescriptionMapper regulationsProblemDescriptionMapper;

    @Autowired
    private TRegulationsSolicitOpinionsMapper regulationsSolicitOpinionsMapper;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;

    @Autowired
    ITRegulationsExaminationProcessService regulationsExaminationProcessService;

    @Autowired
    private BizApprovalManagementMapper approvalManagementMapper;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;

    @Autowired
    private TRegulationsHandleMapper regulationsHandleMapper;

    @Autowired
    TRegulationsConfirmSaveMapper regulationsConfirmSaveMapper;
    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TRegulationsExaminationDTO> queryListPage(TRegulationsExaminationDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
//        LambdaQueryWrapper<TRegulationsExamination> queryWrapper = new LambdaQueryWrapper();
//        if(StringUtils.isNotEmpty(entityDTO.getFileName())){
//            queryWrapper.like(TRegulationsExamination::getFileName,entityDTO.getFileName());
//        }
//        if(entityDTO.getStartTime()!=null&&entityDTO.getEndTime()!=null){
//            queryWrapper.between(TRegulationsExamination::getReportingTime,entityDTO.getStartTime(),entityDTO.getEndTime());
//        }
//        if(entityDTO.getReviewOpinions()!=null){
//            queryWrapper.eq(TRegulationsExamination::getReviewOpinions,entityDTO.getReviewOpinions());
//        }
//        queryWrapper.orderByDesc(TRegulationsExamination::getReleaseTime,TRegulationsExamination::getCreateTime);
//        IPage<TRegulationsExamination> pageData = tRegulationsExaminationMapper.selectPageNoAdd(
//             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
                IPage<TRegulationsExamination> pageData = tRegulationsExaminationMapper.selectPageNoHandel(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);

        //返回
        IPage<TRegulationsExaminationDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TRegulationsExaminationDTO.class));
        for (TRegulationsExaminationDTO record : data.getRecords()) {
            if(record.getFileContent()!=null) {
                //处理文件附件
                List<ExaminationFile> examinationFileList = JSON.parseObject(record.getFileContent(), new TypeReference<List<ExaminationFile>>() {
                });
                record.setExaminationFileList(examinationFileList);
            }
            //检查是否有未读的问题说明
            LambdaQueryWrapper<TRegulationsProblemDescription> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TRegulationsProblemDescription::getExaminationId,record.getId());
            lambdaQueryWrapper.eq(TRegulationsProblemDescription::getIsRead,0);
            Integer countNoAdd = regulationsProblemDescriptionMapper.selectCountNoAdd(lambdaQueryWrapper);
            if(countNoAdd>0){
                record.setIsReadProblem(0);
            }else{
                record.setIsReadProblem(1);
            }

            LambdaQueryWrapper<TRegulationsSolicitOpinions> opinionsLambdaQueryWrapper=new LambdaQueryWrapper<>();
            opinionsLambdaQueryWrapper.eq(TRegulationsSolicitOpinions::getExaminationId,record.getId());
            opinionsLambdaQueryWrapper.eq(TRegulationsSolicitOpinions::getIsRead,0);
            Integer countNoAddSolicit = regulationsSolicitOpinionsMapper.selectCountNoAdd(opinionsLambdaQueryWrapper);
            if(countNoAddSolicit>0){
                record.setIsReadSolicit(0);
            }else{
                record.setIsReadSolicit(1);
            }

            //查询表单流程信息
            if(StringUtils.isNotEmpty(record.getCpbId())){
//                BizApprovalManagement bizApprovalManagement = approvalManagementMapper.selectById(record.getCpbId());
                CscpProcBase base = cscpProcBaseService.getProcBaseByFormDataId(String.valueOf(record.getCpbId()));
                TaskVO processInfoByInstanceId = cscpProcBaseRepository.getProcessInfoByInstanceId(base.getProcInstId());
                record.setTaskVO(processInfoByInstanceId);
            }

            //检查征求意见和问题说明是否完成
            LambdaQueryWrapper<TRegulationsProblemDescription> problemDescriptionLambdaQueryWrapper=new LambdaQueryWrapper<>();
            problemDescriptionLambdaQueryWrapper.eq(TRegulationsProblemDescription::getExaminationId,record.getId());
            List<TRegulationsProblemDescription> tRegulationsProblemDescriptions = regulationsProblemDescriptionMapper.selectListNoAdd(problemDescriptionLambdaQueryWrapper);
            boolean problemDescriptionEnd=true;
            for (TRegulationsProblemDescription tRegulationsProblemDescription : tRegulationsProblemDescriptions) {
                if(tRegulationsProblemDescription.getDescriptionResult()==null&&tRegulationsProblemDescription.getDescriptionFileId()==null){
                    problemDescriptionEnd=false;
                    break;
                }
            }
            if(problemDescriptionEnd){
                record.setIsProblemDescriptionEnd(1);
            }else{
                record.setIsProblemDescriptionEnd(0);
            }

            LambdaQueryWrapper<TRegulationsSolicitOpinions> solicitOpinionsLambdaQueryWrapper =new LambdaQueryWrapper<>();
            solicitOpinionsLambdaQueryWrapper.eq(TRegulationsSolicitOpinions::getExaminationId,record.getId());
            List<TRegulationsSolicitOpinions> regulationsSolicitOpinions = regulationsSolicitOpinionsMapper.selectListNoAdd(solicitOpinionsLambdaQueryWrapper);
            boolean solicitOpinionsEnd=true;
            for (TRegulationsSolicitOpinions regulationsSolicitOpinion : regulationsSolicitOpinions) {
                if(regulationsSolicitOpinion.getFeedbackTime()==null){
                    solicitOpinionsEnd=false;
                    break;
                }
            }
            if(solicitOpinionsEnd){
                record.setIsSolicitOpinionsEnd(1);
            }else{
                record.setIsSolicitOpinionsEnd(0);
            }

            //查询是否已经送处理
//            LambdaQueryWrapper<TRegulationsHandle> handleLambdaQueryWrapper=new LambdaQueryWrapper<>();
//            handleLambdaQueryWrapper.like(TRegulationsHandle::getExaminationId,record.getId());
//            Integer selectCountNoAdd = regulationsHandleMapper.selectCountNoAdd(handleLambdaQueryWrapper);
//            if(selectCountNoAdd>0){
//                record.setIsHandle(1);
//                record.setIsSolicitOpinionsEnd(1);
//                record.setIsProblemDescriptionEnd(1);
//            }else{
//                record.setIsHandle(0);
//            }
            record.setIsHandle(0);

        }


        return new PageResult<TRegulationsExaminationDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TRegulationsExaminationDTO> queryList(TRegulationsExaminationDTO entityDTO) {
        LambdaQueryWrapper<TRegulationsExamination> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(StrUtil.isNotEmpty(entityDTO.getRecordId()),TRegulationsExamination::getRecordId,
                entityDTO.getRecordId())
        ;
            List<TRegulationsExamination> listData = tRegulationsExaminationMapper.selectListNoAdd(queryWrapper);

            List<TRegulationsExaminationDTO> TRegulationsExaminationDTOList = ListCopyUtil.copy(listData, TRegulationsExaminationDTO.class);
        return TRegulationsExaminationDTOList;
    }

    @Override
    public List<TRegulationsExaminationDTO> queryListByIdList(List<Long> idList) {
        List<TRegulationsExamination> listData = tRegulationsExaminationMapper.selectBatchIds(idList);
        List<TRegulationsExaminationDTO> TRegulationsExaminationDTOList = ListCopyUtil.copy(listData, TRegulationsExaminationDTO.class);
        return TRegulationsExaminationDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TRegulationsExaminationDTO findOne(Long id) {
        TRegulationsExamination  tRegulationsExamination =  tRegulationsExaminationMapper.selectById(id);
        TRegulationsExaminationDTO tRegulationsExaminationDTO = BeanConvertUtils.copyProperties(tRegulationsExamination, TRegulationsExaminationDTO.class);
        if(tRegulationsExaminationDTO.getFileContent()!=null) {
            List<ExaminationFile> examinationFileList = JSON.parseObject(tRegulationsExaminationDTO.getFileContent(), new TypeReference<List<ExaminationFile>>() {
            });
            tRegulationsExaminationDTO.setExaminationFileList(examinationFileList);
        }
        if(tRegulationsExaminationDTO.getSolicitOpinionsId()==null) {
            if (!org.springframework.util.StringUtils.isEmpty(tRegulationsExaminationDTO.getSkipStatus())) {
                List<String> list = Arrays.asList(tRegulationsExaminationDTO.getSkipStatus().split(","));
                Set<String> skipSet = new HashSet<>(list);
                skipSet.add("E");
                String skipString = String.join(",", skipSet);
                tRegulationsExaminationDTO.setSkipStatus(skipString);
            } else {
                tRegulationsExaminationDTO.setSkipStatus("E");
            }
        }

        if(tRegulationsExaminationDTO.getProblemDescriptionId()==null) {
            if (!org.springframework.util.StringUtils.isEmpty(tRegulationsExaminationDTO.getSkipStatus())) {
                List<String> list = Arrays.asList(tRegulationsExaminationDTO.getSkipStatus().split(","));
                Set<String> skipSet = new HashSet<>(list);
                skipSet.add("F");
                String skipString = String.join(",", skipSet);
                tRegulationsExaminationDTO.setSkipStatus(skipString);
            } else {
                tRegulationsExaminationDTO.setSkipStatus("F");
            }
        }

        //查询是否有整改反馈
        LambdaQueryWrapper<TRegulationsConfirmSave> tRegulationsConfirmSaveLambdaQueryWrapper=new LambdaQueryWrapper<>();
        tRegulationsConfirmSaveLambdaQueryWrapper.eq(TRegulationsConfirmSave::getExaminationId,id);
        TRegulationsConfirmSave regulationsConfirmSave = regulationsConfirmSaveMapper.selectOneNoAdd(tRegulationsConfirmSaveLambdaQueryWrapper);
        if("J".equals(tRegulationsExaminationDTO.getCurrentStage())&&StringUtils.isEmpty(regulationsConfirmSave.getRectificationHistory())){
            if (!org.springframework.util.StringUtils.isEmpty(tRegulationsExaminationDTO.getSkipStatus())) {
                List<String> list = Arrays.asList(tRegulationsExaminationDTO.getSkipStatus().split(","));
                Set<String> skipSet = new HashSet<>(list);
                skipSet.add("I");
                String skipString = String.join(",", skipSet);
                tRegulationsExaminationDTO.setSkipStatus(skipString);
            } else {
                tRegulationsExaminationDTO.setSkipStatus("I");
            }
        }

        return tRegulationsExaminationDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TRegulationsExaminationDTO create(TRegulationsExaminationDTO entityDTO) {
       TRegulationsExamination tRegulationsExamination =  BeanConvertUtils.copyProperties(entityDTO,TRegulationsExamination.class);
       // 初审中
       tRegulationsExamination.setReviewOpinions(0);
        regulationsExaminationProcessService.initProcess(tRegulationsExamination);
        save(tRegulationsExamination);
        return  BeanConvertUtils.copyProperties(tRegulationsExamination,TRegulationsExaminationDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TRegulationsExaminationDTO entity) {
        TRegulationsExamination tRegulationsExamination = BeanConvertUtils.copyProperties(entity,TRegulationsExamination.class);
        int result = tRegulationsExaminationMapper.updateById(tRegulationsExamination);
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        regulationsExaminationProcessService.updateStatus(tRegulationsExamination,"D",2,companyName);
        return result;
    }

    @Override
    public int updateNoProcess(TRegulationsExaminationDTO entity) {
        TRegulationsExamination tRegulationsExamination = BeanConvertUtils.copyProperties(entity,TRegulationsExamination.class);
        int result = tRegulationsExaminationMapper.updateById(tRegulationsExamination);
        return result;
    }


    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tRegulationsExaminationMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TRegulationsExaminationId
     * @return
     */
    @Override
    public boolean existByTRegulationsExaminationId(Long TRegulationsExaminationId) {
        if (TRegulationsExaminationId != null) {
            LambdaQueryWrapper<TRegulationsExamination> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TRegulationsExamination::getId, TRegulationsExaminationId);
            List<TRegulationsExamination> result = tRegulationsExaminationMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TRegulationsExaminationDTO> dataList) {
        List<TRegulationsExamination> result = ListCopyUtil.copy(dataList, TRegulationsExamination.class);
        for (TRegulationsExamination tRegulationsExamination : result) {
            regulationsExaminationProcessService.initProcess(tRegulationsExamination);
        }
        return saveBatch(result);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response, Integer type, Map<String, String> placeholders, String filePath, String filename) throws IOException{
        // 读取模板文件作为资源流
        Resource resource = new ClassPathResource(filePath);
        InputStream inputStream = resource.getInputStream();

        // 生成文档
        XWPFDocument document = generateDocx(inputStream, placeholders);

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFilename + ".docx\"");

        // 将生成的文档写入响应
        document.write(response.getOutputStream());
        document.close();
    }

    @Override
    public void create(String paramJsonStr , MultipartFile[] babgFile , MultipartFile[] basmFile , MultipartFile[] zswb) {
        if (zswb == null || zswb.length < 1) {
            throw new BusinessException("90002" , "无法获取:正式文本");
        }
        Long formDataId = SnowflakeIdUtil.getSnowFlakeLongId();
        TRegulationsExaminationDTO dto = JSON.parseObject(paramJsonStr , TRegulationsExaminationDTO.class);
        dto.setId(formDataId);

        // recordId 重复判断
//        List<TRegulationsExaminationDTO> tRegulationsExaminationDTOS = this.queryList(TRegulationsExaminationDTO.builder().recordId(dto.getRecordId()).build());
//
       // if(!tRegulationsExaminationDTOS.isEmpty()){
       //     throw new BusinessException("90003:{}" , "recordId重复了");
       // }

        // 附件处理
        List<CscpEnclosureFile> zswbList = annexHandler( zswb,formDataId ,dto.getSecrecyGrade() );
        List<ExaminationFile> zswbData = zswbList.stream().map(i -> {
           return ExaminationFile.builder()
                    .fileName(i.getFileName())
                    .fileType(ExaminationFile.FileTypeEnum.ZSWB.getCode())
                    .fileUrl(i.getFileUrl())
                   .fileId(i.getId())
                    .build();
        }).collect(Collectors.toList());

        List<CscpEnclosureFile> basmFileList = annexHandler(basmFile , formDataId ,dto.getSecrecyGrade() );
        List<ExaminationFile> basmFData = basmFileList.stream().map(i -> ExaminationFile.builder()
                .fileName(i.getFileName())
                .fileType(ExaminationFile.FileTypeEnum.BASM.getCode())
                .fileUrl(i.getFileUrl())
                .fileId(i.getId())
                .build()
        ).collect(Collectors.toList());

        List<CscpEnclosureFile> babgFileList = annexHandler(babgFile , formDataId,dto.getSecrecyGrade() );
        List<ExaminationFile> babgData = babgFileList.stream().map(i -> ExaminationFile.builder()
                .fileName(i.getFileName())
                .fileType(ExaminationFile.FileTypeEnum.BABG.getCode())
                .fileUrl(i.getFileUrl())
                .fileId(i.getId())
                .build()
        ).collect(Collectors.toList());

        List<ExaminationFile> concat = Stream.concat(babgData.stream() , basmFData.stream())
                .collect(Collectors.toList());
        List<ExaminationFile> allFile = Stream.concat(concat.stream() , zswbData.stream()).collect(Collectors.toList());
        String fileJson = JSON.toJSONString(allFile);
        dto.setFileContent(fileJson);
        dto.setCurrentStage("C");
        create(dto);
    }


    public XWPFDocument generateDocx(InputStream templateInputStream, Map<String, String> placeholders) {
        XWPFDocument document = null;
        try {
            document = new XWPFDocument(templateInputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 遍历段落并替换占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replacePlaceholders(paragraph, placeholders);
        }
        // 遍历表格并替换占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replacePlaceholders(paragraph, placeholders);
                    }
                }
            }
        }
        return document;
    }

    public void replacePlaceholders(XWPFParagraph paragraph, Map<String, String> placeholders) {
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            String placeholder = entry.getKey();
            String replacement = entry.getValue();
            List<XWPFRun> runs = paragraph.getRuns();
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String text = run.getText(0);
                if (text != null && text.contains(placeholder)) {
                    String[] parts = text.split(Pattern.quote(placeholder), -1);
                    run.setText(parts[0], 0); // 设置替换前的文本
                    XWPFRun newRun = paragraph.insertNewRun(i + 1); // 插入一个新的run
                    newRun.setText(replacement); // 设置替换后的文本
                    // 保留原始文本的字体样式
                    newRun.setFontFamily(run.getFontFamily());
                    newRun.setFontSize(run.getFontSize());
                    newRun.setBold(run.isBold());
                    newRun.setItalic(run.isItalic());
                    newRun.setUnderline(run.getUnderline());
                    newRun.setColor(run.getColor());
                    // 删除原始run中的文本
                    run.setText("", 0);
                }
            }
        }
    }

    /**
     * 附件处理
     *
     * @param annex
     * @param
     * @param formDataId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CscpEnclosureFile> annexHandler(MultipartFile[] annex , Long formDataId,Integer secrecyGrade) {
        if(secrecyGrade == null ){
            secrecyGrade = 1;
        }
        List<CscpEnclosureFile> lRes = new ArrayList<>();
        List<CscpEnclosureFile> resultList = new ArrayList<>();
        for (MultipartFile f : annex) {
            if (null != f && 0L == f.getSize()) {
                continue;
            }
            CscpDocumentFile cdf = new CscpDocumentFile();
            CscpEnclosureFile i = new CscpEnclosureFile();
            switch (secrecyGrade) {
                case 1:
                case 3:
                    i.setSecurityClassifiedCode(1);
                    i.setSecurityClassified("内部");

                    cdf.setSecurityClassifiedCode(1);
                    cdf.setSecurityClassified("内部");


                    break;
                case 2:
                    i.setSecurityClassifiedCode(0);
                    i.setSecurityClassified("公开");

                    cdf.setSecurityClassifiedCode(0);
                    cdf.setSecurityClassified("公开");


                    break;
                case 4:
                case 6:
                    i.setSecurityClassifiedCode(2);
                    i.setSecurityClassified("秘密");
                    cdf.setSecurityClassifiedCode(2);
                    cdf.setSecurityClassified("秘密");

                    break;
                case 5:
                    i.setSecurityClassifiedCode(3);
                    i.setSecurityClassified("机密");
                    cdf.setSecurityClassifiedCode(3);
                    cdf.setSecurityClassified("机密");

                    break;
                case 7:
                    i.setSecurityClassifiedCode(1);
                    i.setSecurityClassified("内部");
                    cdf.setSecurityClassifiedCode(1);
                    cdf.setSecurityClassified("内部");

                    break;
                default:
                    break;
            }
            String extNameByFileName = FileNameUtil.getExtNameByFileName(f.getOriginalFilename());
            if (DocumentFormat.asMyEnum(extNameByFileName)) {
                // 正文表保存一份，方便后续编辑
                // enclosureFileFlag：1，表示为可编辑的附件，非正文件
                cdf.setEnclosureFileFlag(1);
                cdf.setFormDataId(formDataId);

                CscpDocumentFile cdfRes = cscpDocumentFileService.uploadFormFile(cdf, f);
                CscpEnclosureFile cffRes = BeanConvertUtils.copyProperties(cdfRes, CscpEnclosureFile.class);
                resultList.add(cffRes);
                try {
                    // TODO 正文转换
                    cscpDocumentFileService.doWpsZtZwConvert(cdf);
                } catch (Exception ex) {
                    log.info("WPS文档中台环境未搭建好，暂不支持文件转换");
                }
                CscpDocumentFile newDoc = new CscpDocumentFile();
                newDoc.setId(cdfRes.getId());
                newDoc.setVersion((null == cdfRes.getVersion()) ? 1 : cdfRes.getVersion() + 1);
                newDoc.setUpdateTime(LocalDateTime.now());
                cscpDocumentFileService.updateById(newDoc);
            }else {
                CscpEnclosureFile cff = new CscpEnclosureFile();
                cff.setFormDataId(formDataId);
                CscpEnclosureFile cffRes = cscpEnclosureFileService.uploadFormFile(cff, f);
                lRes.add(cffRes);
                resultList.add(cffRes);
            }
            i.setFormDataId(formDataId);
            CscpEnclosureFile cffRes = cscpEnclosureFileService.uploadFormFile(i , f);
            lRes.add(cffRes);
        }
        try {
            // TODO 附件转换
            cscpEnclosureFileService.convertFj2PdfOrOfd(lRes);
        } catch (Exception ex) {
            log.info("WPS文档中台环境未搭建好，暂不支持文件转换");
        }
        return lRes;
    }

}
