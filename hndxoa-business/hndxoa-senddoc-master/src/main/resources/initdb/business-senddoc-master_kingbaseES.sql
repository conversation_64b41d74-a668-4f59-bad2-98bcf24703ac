CREATE TABLE "public"."t_gw_num_type" (
                                          "id" int8 NOT NULL,
                                          "document_number" varchar(50 char) NULL,
	"document_type" varchar(20 char) NULL,
	"document_number_type" character(1 char) NULL,
	"symbol_prefix" varchar(20 char) NULL,
	"symbol_suffix" varchar(20 char) NULL,
	"symbol_ending" varchar(20 char) NULL,
	"code" varchar(20 char) NULL,
	"particular_year" varchar(20 char) NULL,
	"number_digit" int4 NULL,
	"sort" int4 NULL,
	"belonging_department_id" int8 NULL,
	"belonging_department_name" varchar(100 char) NULL,
	"document_number_classification_id" int8 NULL,
	"document_number_classification_name" varchar(100 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(64 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(64 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"whether_effective" character(1 char) NULL,
	CONSTRAINT "t_gw_num_type_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_gw_num_type"."whether_effective" IS '是否有效 1：有效 0：无效';
COMMENT ON COLUMN "public"."t_gw_num_type"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_gw_num_type"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."t_gw_num_type"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."t_gw_num_type"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."t_gw_num_type"."update_time" IS '跟新时间';
COMMENT ON COLUMN "public"."t_gw_num_type"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."t_gw_num_type"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."t_gw_num_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_gw_num_type"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."t_gw_num_type"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_gw_num_type"."document_number_classification_name" IS '文号父类名称';
COMMENT ON COLUMN "public"."t_gw_num_type"."document_number_classification_id" IS '文号分类id';
COMMENT ON COLUMN "public"."t_gw_num_type"."belonging_department_name" IS '所属部门名称';
COMMENT ON COLUMN "public"."t_gw_num_type"."belonging_department_id" IS '所属部门id';
COMMENT ON COLUMN "public"."t_gw_num_type"."sort" IS '排序';
COMMENT ON COLUMN "public"."t_gw_num_type"."number_digit" IS '编号位数';
COMMENT ON COLUMN "public"."t_gw_num_type"."particular_year" IS '年份';
COMMENT ON COLUMN "public"."t_gw_num_type"."code" IS '编码';
COMMENT ON COLUMN "public"."t_gw_num_type"."symbol_ending" IS '文号结尾';
COMMENT ON COLUMN "public"."t_gw_num_type"."symbol_suffix" IS '文号后缀';
COMMENT ON COLUMN "public"."t_gw_num_type"."symbol_prefix" IS '文号前缀';
COMMENT ON COLUMN "public"."t_gw_num_type"."document_number_type" IS '文号类型  (1:综合性文号 2:部门文号)';
COMMENT ON COLUMN "public"."t_gw_num_type"."document_type" IS '公文类型（1：发文 2：收文）';
COMMENT ON COLUMN "public"."t_gw_num_type"."document_number" IS '公文文号';
COMMENT ON COLUMN "public"."t_gw_num_type"."id" IS '主键';
COMMENT ON TABLE "public"."t_gw_num_type" IS '文号管理表';


CREATE TABLE "public"."t_official_type" (
                                            "id" int8 NOT NULL,
                                            CREATE_BY int8 NULL,
                                            CREATE_TIME timestamp(6) NULL,
                                            UPDATE_BY int8 NULL,
                                            "department_id" int8 NULL,
                                            UPDATE_TIME timestamp(6) NULL,
                                            "company_id" int8 NULL,
                                            "tenant_id" int8 NULL,
                                            "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	DELETED int4 NULL,
	"official_name" varchar(200 char) NULL,
	"official_type" varchar(32 char) NULL,
	"sort" int4 NULL,
	CONSTRAINT "t_official_type_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_official_type"."sort" IS '排序';
COMMENT ON COLUMN "public"."t_official_type"."official_type" IS '公文的类型';
COMMENT ON COLUMN "public"."t_official_type"."official_name" IS '公文类型名称';
COMMENT ON COLUMN "public"."t_official_type"."DELETED" IS '逻辑删除字段1表示删除0表示未删除';
COMMENT ON COLUMN "public"."t_official_type"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."t_official_type"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."t_official_type"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."t_official_type"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."t_official_type"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."t_official_type"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."t_official_type"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."t_official_type"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."t_official_type"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."t_official_type"."id" IS '主键';
COMMENT ON TABLE "public"."t_official_type" IS '公文类型表 t_official_type';


CREATE TABLE "public"."t_senddoc_master" (
                                             "id" int8 NOT NULL,
                                             CREATE_BY int8 NULL,
                                             CREATE_TIME timestamp(6) NULL,
                                             UPDATE_BY int8 NULL,
                                             "deleted" varchar(32 char) NULL,
	"department_id" int8 NULL,
	UPDATE_TIME timestamp(6) NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"title" varchar(500 char) NULL,
	"bpm_status" int4 NULL,
	"document" varchar(1 char) NULL,
	"annex" varchar(1 char) NULL,
	"process_instance_id" int8 NULL,
	"mobile" varchar(32 char) NULL,
	"department_name" varchar(128 char) NULL,
	"company_name" varchar(128 char) NULL,
	"secret" varchar(32 char) NULL,
	"urgency" varchar(32 char) NULL,
	"submitto" varchar(3072 char) NULL,
	"copyto" varchar(3072 char) NULL,
	"description" varchar(3072 char) NULL,
	"print_number" int4 NULL,
	"send_name" varchar(32 char) NULL,
	"flow_direction" varchar(32 char) NULL,
	"document_type" varchar(32 char) NULL,
	"audit_instructions" varchar(2500 char) NULL,
	"ask_company_name" varchar(255 char) NULL,
	"post_range" varchar(255 char) NULL,
	"reference_number" varchar(50 char) NULL,
	"document_practice" varchar(50 char) NULL,
	"draft_people" varchar(50 char) NULL,
	CONSTRAINT "t_senddoc_master_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_senddoc_master"."draft_people" IS '核稿人';
COMMENT ON COLUMN "public"."t_senddoc_master"."document_practice" IS '公文文种';
COMMENT ON COLUMN "public"."t_senddoc_master"."reference_number" IS '公文文号';
COMMENT ON COLUMN "public"."t_senddoc_master"."post_range" IS '发文范围';
COMMENT ON COLUMN "public"."t_senddoc_master"."ask_company_name" IS '请示单位名称';
COMMENT ON COLUMN "public"."t_senddoc_master"."audit_instructions" IS '审核说明';
COMMENT ON COLUMN "public"."t_senddoc_master"."document_type" IS '公文种类';
COMMENT ON COLUMN "public"."t_senddoc_master"."flow_direction" IS '行文方向';
COMMENT ON COLUMN "public"."t_senddoc_master"."send_name" IS '发文名义';
COMMENT ON COLUMN "public"."t_senddoc_master"."print_number" IS '打印份数';
COMMENT ON COLUMN "public"."t_senddoc_master"."description" IS '备注';
COMMENT ON COLUMN "public"."t_senddoc_master"."copyto" IS '抄送';
COMMENT ON COLUMN "public"."t_senddoc_master"."submitto" IS '主送';
COMMENT ON COLUMN "public"."t_senddoc_master"."urgency" IS '公文紧急程度';
COMMENT ON COLUMN "public"."t_senddoc_master"."secret" IS '公文密级';
COMMENT ON COLUMN "public"."t_senddoc_master"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."t_senddoc_master"."department_name" IS '拟稿部门';
COMMENT ON COLUMN "public"."t_senddoc_master"."mobile" IS '手机号码拟稿人';
COMMENT ON COLUMN "public"."t_senddoc_master"."process_instance_id" IS '流程实例id';
COMMENT ON COLUMN "public"."t_senddoc_master"."annex" IS '是否附件';
COMMENT ON COLUMN "public"."t_senddoc_master"."document" IS '是否正文';
COMMENT ON COLUMN "public"."t_senddoc_master"."bpm_status" IS '流程状态，参照通用状态';
COMMENT ON COLUMN "public"."t_senddoc_master"."title" IS '发文标题';
COMMENT ON COLUMN "public"."t_senddoc_master"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."t_senddoc_master"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."t_senddoc_master"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."t_senddoc_master"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."t_senddoc_master"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."t_senddoc_master"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."t_senddoc_master"."deleted" IS '逻辑删除1表示删除0未删除';
COMMENT ON COLUMN "public"."t_senddoc_master"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."t_senddoc_master"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."t_senddoc_master"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."t_senddoc_master"."id" IS '主键';
COMMENT ON TABLE "public"."t_senddoc_master" IS '公文发文表 公文发文表';
