CREATE TABLE `t_gw_num_type` (
                                 `id` bigint NOT NULL COMMENT '主键',
                                 `document_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文文号',
                                 `document_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文类型（1：发文 2：收文）',
                                 `document_number_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文号类型  (1:综合性文号 2:部门文号)',
                                 `symbol_prefix` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文号前缀',
                                 `symbol_suffix` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文号后缀',
                                 `symbol_ending` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文号结尾',
                                 `code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '编码',
                                 `particular_year` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '年份',
                                 `number_digit` int DEFAULT NULL COMMENT '编号位数',
                                 `sort` int DEFAULT NULL COMMENT '排序',
                                 `belonging_department_id` bigint DEFAULT NULL COMMENT '所属部门id',
                                 `belonging_department_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属部门名称',
                                 `document_number_classification_id` bigint DEFAULT NULL COMMENT '文号分类id',
                                 `document_number_classification_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文号父类名称',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                 `create_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                 `update_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime DEFAULT NULL COMMENT '跟新时间',
                                 `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                 `company_id` bigint DEFAULT NULL COMMENT '单位id',
                                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                 `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                 `whether_effective` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否有效 1：有效 0：无效',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文号管理表';

CREATE TABLE `t_official_type` (
                                   `id` bigint NOT NULL COMMENT '主键',
                                   `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                   `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                   `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                   `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                   `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                   `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                   `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                   `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                   `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                   `DELETED` int DEFAULT NULL COMMENT '逻辑删除字段1表示删除0表示未删除',
                                   `official_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文类型名称',
                                   `official_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文的类型',
                                   `sort` int DEFAULT NULL COMMENT '排序',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公文类型表 t_official_type';

CREATE TABLE `t_senddoc_master` (
                                    `id` bigint NOT NULL COMMENT '主键',
                                    `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                    `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                    `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                    `deleted` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '逻辑删除1表示删除0未删除',
                                    `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                    `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                    `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                    `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                    `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                    `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                    `title` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发文标题',
                                    `bpm_status` int DEFAULT NULL COMMENT '流程状态，参照通用状态',
                                    `document` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否正文',
                                    `annex` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否附件',
                                    `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例id',
                                    `mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手机号码拟稿人',
                                    `department_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿部门',
                                    `company_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                    `secret` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文密级',
                                    `urgency` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文紧急程度',
                                    `submitto` varchar(3072) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主送',
                                    `copyto` varchar(3072) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抄送',
                                    `description` varchar(3072) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                    `print_number` int DEFAULT NULL COMMENT '打印份数',
                                    `send_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发文名义',
                                    `flow_direction` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行文方向',
                                    `document_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文种类',
                                    `audit_instructions` varchar(2500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核说明',
                                    `ask_company_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '请示单位名称',
                                    `post_range` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发文范围',
                                    `reference_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文文号',
                                    `document_practice` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文文种',
                                    `draft_people` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '核稿人',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公文发文表 公文发文表';

