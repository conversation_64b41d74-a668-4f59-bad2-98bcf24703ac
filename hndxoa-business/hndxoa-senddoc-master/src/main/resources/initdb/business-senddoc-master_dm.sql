CREATE TABLE "myapp"."t_gw_num_type"
(
"id" BIGINT NOT NULL,
"document_number" VARCHAR(50),
"document_type" VARCHAR(20),
"document_number_type" CHAR(1),
"symbol_prefix" VARCHAR(20),
"symbol_suffix" VARCHAR(20),
"symbol_ending" VARCHAR(20),
"code" VARCHAR(20),
"particular_year" VARCHAR(20),
"number_digit" INT,
"sort" INT,
"belonging_department_id" BIGINT,
"belonging_department_name" VARCHAR(100),
"document_number_classification_id" BIGINT,
"document_number_classification_name" VAR, CHAR(100),
"create_by" BIGINT,
"create_name" VARCHAR(64),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(64),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
"whether_effective" CHAR(1),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_gw_num_type" IS '文号管理表';COMMENT ON COLUMN "myapp"."t_gw_num_type"."id" IS '主键';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."document_number" IS '公文文号';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."document_type" IS '公文类型（1：发文 2：收文）';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."document_number_type" IS '文号类型  (1:综合性文号 2:部门文号)';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."symbol_prefix" IS '文号前缀';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."symbol_suffix" IS '文号后缀';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."symbol_ending" IS '文号结尾';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."code" IS '编码';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."particular_year" IS '年份';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."number_digit" IS '编号位数';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."sort" IS '排序';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."belonging_department_id" IS '所属部门id';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."belonging_department_name" IS '所属部门名称';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."document_number_classification_id" IS '文号分类id';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."document_number_classification_name" IS '文号父类名称';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."update_time" IS '跟新时间';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_gw_num_type"."whether_effective" IS '是否有效 1：有效 0：无效';




CREATE TABLE "myapp"."t_official_type"
(
"id" BIGINT NOT NULL,
"CREATE_BY" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"department_id" BIGINT,
"UPDATE_TIME" TIMESTAMP(0),
"company_id" BIGINT,
"tenant_id" BIGINT,
"create_name" VARCHAR(32),
"update_name" VARCHAR(32),
"DELETED" INT,
"official_name" VARCHAR(200),
"official_type" VARCHAR(32),
"sort" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_official_type" IS '公文类型表 t_official_type';COMMENT ON COLUMN "myapp"."t_official_type"."id" IS '主键';
COMMENT ON COLUMN "myapp"."t_official_type"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."t_official_type"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_official_type"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."t_official_type"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."t_official_type"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_official_type"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."t_official_type"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."t_official_type"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."t_official_type"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."t_official_type"."DELETED" IS '逻辑删除字段1表示删除0表示未删除';
COMMENT ON COLUMN "myapp"."t_official_type"."official_name" IS '公文类型名称';
COMMENT ON COLUMN "myapp"."t_official_type"."official_type" IS '公文的类型';
COMMENT ON COLUMN "myapp"."t_official_type"."sort" IS '排序';




CREATE TABLE "myapp"."t_senddoc_master"
(
"id" BIGINT NOT NULL,
"CREATE_BY" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"deleted" VARCHAR(32),
"department_id" BIGINT,
"UPDATE_TIME" TIMESTAMP(0),
"company_id" BIGINT,
"tenant_id" BIGINT,
"create_name" VARCHAR(32),
"update_name" VARCHAR(32),
"title" VARCHAR(500),
"bpm_status" INT,
"document" VARCHAR(1),
"annex" VARCHAR(1),
"process_instance_id" BIGINT,
"mobile" VARCHAR(32),
"department_name" VARCHAR(128),
"company_na, me" VARCHAR(128),
"secret" VARCHAR(32),
"urgency" VARCHAR(32),
"submitto" VARCHAR(3072),
"copyto" VARCHAR(3072),
"description" VARCHAR(3072),
"print_number" INT,
"send_name" VARCHAR(32),
"flow_direction" VARCHAR(32),
"document_type" VARCHAR(32),
"audit_instructions" VARCHAR(2500),
"ask_company_name" VARCHAR(255),
"post_range" VARCHAR(255),
"reference_number" VARCHAR(50),
"document_practice" VARCHAR(50),
"draft_people" VARCHAR(50),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN",,  CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_senddoc_master" IS '公文发文表 公文发文表';COMMENT ON COLUMN "myapp"."t_senddoc_master"."id" IS '主键';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."deleted" IS '逻辑删除1表示删除0未删除';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."title" IS '发文标题';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."bpm_status" IS '流程状态，参照通用状态';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."document" IS '是否正文';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."annex" IS '是否附件';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."process_instance_id" IS '流程实例id';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."mobile" IS '手机号码拟稿人';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."department_name" IS '拟稿部门';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."company_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."secret" IS '公文密级';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."urgency" IS '公文紧急程度';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."submitto" IS '主送';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."copyto" IS '抄送';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."description" IS '备注';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."print_number" IS '打印份数';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."send_name" IS '发文名义';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."flow_direction" IS '行文方向';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."document_type" IS '公文种类';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."audit_instructions" IS '审核说明';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."ask_company_name" IS '请示单位名称';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."post_range" IS '发文范围';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."reference_number" IS '公文文号';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."document_practice" IS '公文文种';
COMMENT ON COLUMN "myapp"."t_senddoc_master"."draft_people" IS '核稿人';




