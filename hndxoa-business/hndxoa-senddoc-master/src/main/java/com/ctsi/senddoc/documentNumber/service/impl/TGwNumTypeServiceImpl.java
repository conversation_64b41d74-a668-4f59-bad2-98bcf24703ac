package com.ctsi.senddoc.documentNumber.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.MybatisQueryUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.senddoc.documentNumber.entity.TGwNumType;
import com.ctsi.senddoc.documentNumber.entity.dto.TGwNumTypeDTO;
import com.ctsi.senddoc.documentNumber.mapper.TGwNumTypeMapper;
import com.ctsi.senddoc.documentNumber.service.ITGwNumTypeService;
import com.ctsi.senddoc.officialtype.entity.TOfficialType;
import com.ctsi.senddoc.officialtype.mapper.TOfficialTypeMapper;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */

@Slf4j
@Service
public class TGwNumTypeServiceImpl extends SysBaseServiceImpl<TGwNumTypeMapper, TGwNumType> implements ITGwNumTypeService {

    @Autowired
    private TGwNumTypeMapper tGwNumTypeMapper;

    @Autowired
    private TOfficialTypeMapper tOfficialTypeMapper;

    /**
     * 翻页
     *
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TGwNumTypeDTO> queryListPage(TGwNumTypeDTO tGwNumTypeDTO, BasePageForm basePageForm) {

        //设置条件
        TGwNumType tGwNumType = BeanConvertUtils.copyProperties(tGwNumTypeDTO, TGwNumType.class);
        QueryWrapper<TGwNumType> tGwNumTypeQueryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(TGwNumType.class, tGwNumType);
        tGwNumTypeQueryWrapper.orderByAsc("sort");
        IPage<TGwNumType> pageData = tGwNumTypeMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), tGwNumTypeQueryWrapper);

        List<Long> typeIdList = pageData.getRecords().stream().map(i -> i.getDocumentNumberClassificationId()).collect(Collectors.toList());
        //获取对应的类型名称
        if (!typeIdList.isEmpty()) {

            List<TOfficialType> tOfficialTypes = tOfficialTypeMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TOfficialType>()
                            .select(TOfficialType::getId, TOfficialType::getOfficialName)
                            .in(TOfficialType::getId, typeIdList)
            );

            Map<Long, List<TOfficialType>> typeMap = tOfficialTypes.stream().collect(Collectors.groupingBy(TOfficialType::getId));
            pageData.getRecords().forEach(i -> {
                List<TOfficialType> v1 = typeMap.get(i.getDocumentNumberClassificationId());
                if (!Objects.isNull(v1) && !v1.isEmpty()) {
                    i.setDocumentNumberClassificationName(v1.get(0).getOfficialName());
                }
            });
        }

        //返回
        IPage<TGwNumTypeDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TGwNumTypeDTO.class));
        return new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TGwNumTypeDTO findOne(Long id) {
        TGwNumType tGwNumType = tGwNumTypeMapper.selectOne(
                new LambdaQueryWrapper<TGwNumType>()
                        //根据主键id查询
                        .eq(TGwNumType::getId, id)
                        //只查询有效的数据
                        .eq(TGwNumType::getWhetherEffective, 1));
        return BeanConvertUtils.copyProperties(tGwNumType, TGwNumTypeDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TGwNumTypeDTO create(TGwNumTypeDTO entityDTO) {
        TGwNumType tGwNumType = BeanConvertUtils.copyProperties(entityDTO, TGwNumType.class);
        save(tGwNumType);
        return BeanConvertUtils.copyProperties(tGwNumType, TGwNumTypeDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TGwNumTypeDTO entity) {
        TGwNumType tGwNumType = BeanConvertUtils.copyProperties(entity, TGwNumType.class);
        return tGwNumTypeMapper.updateById(tGwNumType);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(List<Long> id) {
        boolean b = this.removeByIds(id);
        return b ? 1 : 0;
    }


}
