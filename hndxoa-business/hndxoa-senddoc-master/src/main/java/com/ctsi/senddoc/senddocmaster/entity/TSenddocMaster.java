package com.ctsi.senddoc.senddocmaster.entity;

import com.ctsi.hndx.common.ProcessBusinessBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公文发文表 公文发文表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TSenddocMaster对象", description="公文发文表 公文发文表")
public class TSenddocMaster extends ProcessBusinessBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private Long processInstanceId;

    /**
     * 手机号码拟稿人
     */
    @ApiModelProperty(value = "手机号码拟稿人")
    private String mobile;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级名称")
    private String secretName;

    /**
     * 公文紧急程度
     */
    @ApiModelProperty(value = "公文紧急程度")
    private String urgency;

    /**
     * 公文紧急程度名称
     */
    @ApiModelProperty(value = "公文紧急程度名称")
    private String urgencyName;

    /**
     * 主送
     */
    @ApiModelProperty(value = "主送")
    private String submitto;

    /**
     * 抄送
     */
    @ApiModelProperty(value = "抄送")
    private String copyto;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String description;

    /**
     * 打印份数
     */
    @ApiModelProperty(value = "打印份数")
    private Integer printNumber;

    /**
     * 发文名义
     */
    @ApiModelProperty(value = "发文名义")
    private String sendName;

    /**
     * 发文名义名称
     */
    @ApiModelProperty(value = "发文名义名称")
    private String sendNameName;

    /**
     * 行文方向
     */
    @ApiModelProperty(value = "行文方向")
    private String flowDirection;

    /**
     * 行文方向名称
     */
    @ApiModelProperty(value = "行文方向名称")
    private String flowDirectionName;

    /**
     * 公文种类
     */
    @ApiModelProperty(value = "公文种类")
    private String documentType;

    /**
     * 公文种类名称
     */
    @ApiModelProperty(value = "公文种类名称")
    private String documentTypeName;

    /**
     * 审核说明
     */
    @ApiModelProperty(value = "审核说明")
    private String auditInstructions;

    /**
     * 请示单位名称
     */
    @ApiModelProperty(value = "请示单位名称")
    private String askCompanyName;

    /**
     * 发文范围
     */
    @ApiModelProperty(value = "发文范围")
    private String postRange;

    /**
     * 发文范围名称
     */
    @ApiModelProperty(value = "发文范围名称")
    private String postRangeName;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 公文文种
     */
    @ApiModelProperty(value = "公文文种")
    private String documentPractice;


    /**
     * 公文文种
     */
    @ApiModelProperty(value = "公文文种")
    private String draftPeople;


    @ApiModelProperty(value = "经办人")
    private String agentPeople;


    @ApiModelProperty(value = "发文日期")
    private String dispatchDate;

    @ApiModelProperty(value = "校对人")
    private String proofreader;

    @ApiModelProperty(value = "表单json")
    private String formJson;

    @ApiModelProperty(value = "文件口径")
    private String documentCaliber;

    @ApiModelProperty(value = "文件口径名称")
    private String documentCaliberName;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件类型名称")
    private String fileTypeName;
}
