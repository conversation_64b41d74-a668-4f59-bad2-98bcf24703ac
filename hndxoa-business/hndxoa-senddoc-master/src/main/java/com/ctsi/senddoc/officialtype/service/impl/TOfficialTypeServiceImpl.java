package com.ctsi.senddoc.officialtype.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.senddoc.officialtype.entity.TOfficialType;
import com.ctsi.senddoc.officialtype.entity.dto.TOfficialTypeDTO;
import com.ctsi.senddoc.officialtype.mapper.TOfficialTypeMapper;
import com.ctsi.senddoc.officialtype.service.ITOfficialTypeService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 公文类型表 t_official_type 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */

@Slf4j
@Service
public class TOfficialTypeServiceImpl extends SysBaseServiceImpl<TOfficialTypeMapper, TOfficialType> implements ITOfficialTypeService {

    @Autowired
    private TOfficialTypeMapper tOfficialTypeMapper;

    /**
     * 翻页
     *
     * @param entity
     * @param pageable
     * @return
     */
    @Override
    public IPage<TOfficialTypeDTO> queryListPage(TOfficialTypeDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TOfficialType> queryWrapper = new LambdaQueryWrapper();
        //根据名称模糊查询
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getOfficialName()), TOfficialType::getOfficialName, entityDTO.getOfficialName());
        //根据类型查询
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getOfficialType()), TOfficialType::getOfficialType, entityDTO.getOfficialType());

        IPage<TOfficialType> pageData = tOfficialTypeMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TOfficialTypeDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TOfficialTypeDTO.class));
        return data;
    }

    /**
     * 列表查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<TOfficialTypeDTO> queryList(TOfficialTypeDTO entityDTO) {
        LambdaQueryWrapper<TOfficialType> queryWrapper = new LambdaQueryWrapper();
        List<TOfficialType> listData = tOfficialTypeMapper.selectList(queryWrapper);
        List<TOfficialTypeDTO> TOfficialTypeDTOList = ListCopyUtil.copy(listData, TOfficialTypeDTO.class);
        return TOfficialTypeDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TOfficialTypeDTO findOne(Long id) {
        TOfficialType tOfficialType = tOfficialTypeMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tOfficialType, TOfficialTypeDTO.class);
    }


    /**
     * 新增
     *
     * @param entity the entity to create
     * @return
     */
    @Override
    @Transactional
    public TOfficialTypeDTO create(TOfficialTypeDTO entityDTO) {
        TOfficialType tOfficialType = BeanConvertUtils.copyProperties(entityDTO, TOfficialType.class);
        save(tOfficialType);
        return BeanConvertUtils.copyProperties(tOfficialType, TOfficialTypeDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TOfficialTypeDTO entity) {
        TOfficialType tOfficialType = BeanConvertUtils.copyProperties(entity, TOfficialType.class);
        return tOfficialTypeMapper.updateById(tOfficialType);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return tOfficialTypeMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TOfficialTypeId
     * @return
     */
    @Override
    public boolean existByTOfficialTypeId(Long TOfficialTypeId) {
        if (TOfficialTypeId != null) {
            LambdaQueryWrapper<TOfficialType> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TOfficialType::getId, TOfficialTypeId);
            List<TOfficialType> result = tOfficialTypeMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<TOfficialTypeDTO> dataList) {
        List<TOfficialType> result = ListCopyUtil.copy(dataList, TOfficialType.class);
        return saveBatch(result);
    }


}
