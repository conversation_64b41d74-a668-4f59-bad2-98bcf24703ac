package com.ctsi.senddoc.officialtype.controller;
import java.net.URISyntaxException;
import java.util.List;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.senddoc.officialtype.entity.dto.TOfficialTypeDTO;
import com.ctsi.senddoc.officialtype.service.ITOfficialTypeService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tOfficialType")
@Api(value = "公文类型表 t_official_type", tags = "公文类型表 t_official_type接口")
public class TOfficialTypeController extends BaseController {

    private static final String ENTITY_NAME = "tOfficialType";

    @Autowired
    private ITOfficialTypeService tOfficialTypeService;



    /**
     *  新增公文类型表 t_official_type批量数据.
     */
    @PostMapping("/createBatch")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增批量公文类型")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    public ResultVO createBatch(@RequestBody List<TOfficialTypeDTO> tOfficialTypeList) {
       Boolean  result = tOfficialTypeService.insertBatch(tOfficialTypeList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增公文类型")
    @ApiOperation(value = "新增", notes = "传入参数")
    public ResultVO<TOfficialTypeDTO> create(@RequestBody TOfficialTypeDTO tOfficialTypeDTO) throws URISyntaxException {
        TOfficialTypeDTO result = tOfficialTypeService.create(tOfficialTypeDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新公文类型")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO update(@RequestBody TOfficialTypeDTO tOfficialTypeDTO) {
	    Assert.notNull(tOfficialTypeDTO.getId(), "general.IdNotNull");
        int count = tOfficialTypeService.update(tOfficialTypeDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除存在的公文类型")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tOfficialTypeService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TOfficialTypeDTO tOfficialTypeDTO = tOfficialTypeService.findOne(id);
        return ResultVO.success(tOfficialTypeDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTOfficialTypePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TOfficialTypeDTO>> queryTOfficialTypePage(TOfficialTypeDTO tOfficialTypeDTO, BasePageForm basePageForm) {
        IPage<TOfficialTypeDTO> tOfficialTypeList = tOfficialTypeService.queryListPage(tOfficialTypeDTO, basePageForm);
        PageResult<TOfficialTypeDTO> pageResult = new PageResult<>(tOfficialTypeList.getRecords(),tOfficialTypeList.getTotal(),
                tOfficialTypeList.getTotal());
        return ResultVO.success(pageResult);
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTOfficialType")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<List<TOfficialTypeDTO>> queryTOfficialType(TOfficialTypeDTO tOfficialTypeDTO) {
       List<TOfficialTypeDTO> list = tOfficialTypeService.queryList(tOfficialTypeDTO);
       return ResultVO.success(list);
   }

}
