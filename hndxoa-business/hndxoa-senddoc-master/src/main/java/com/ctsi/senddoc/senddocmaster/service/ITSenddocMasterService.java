package com.ctsi.senddoc.senddocmaster.service;

import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.senddoc.senddocmaster.entity.dto.TSenddocMasterDTO;
import com.ctsi.senddoc.senddocmaster.entity.TSenddocMaster;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <p>
 * 公文发文表 公文发文表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
public interface ITSenddocMasterService extends SysBaseServiceI<TSenddocMaster> {


    /**
     * 分页查询
     */
    PageResult<TSenddocMasterDTO> queryListPage(TSenddocMasterDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TSenddocMasterDTO> queryList(TSenddocMasterDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TSenddocMasterDTO findOne(Long id);

    /**
     * 新增
     */
    TSenddocMasterDTO create(TSenddocMasterDTO entity);


    /**
     * 更新
     */
    int update(TSenddocMasterDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTSenddocMasterId
     */
    boolean existByTSenddocMasterId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TSenddocMasterDTO> dataList);

    /**
     * 异步复制正文pdf到好签
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void copyPdfToHaoqian(CscpHaoqianVersionDTO entityDTO);

    /**
     * 档案规范标题
     *
     * @param
     * @return
     */
    String getElectronArchivesNorm();


}
