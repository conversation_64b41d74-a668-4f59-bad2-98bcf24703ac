package com.ctsi.senddoc.officialtype.service;

import com.ctsi.senddoc.officialtype.entity.dto.TOfficialTypeDTO;
import com.ctsi.senddoc.officialtype.entity.TOfficialType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import java.util.List;

/**
 * <p>
 * 公文类型表 t_official_type 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface ITOfficialTypeService extends SysBaseServiceI<TOfficialType> {


    /**
     * 分页查询
     */
    IPage<TOfficialTypeDTO> queryListPage(TOfficialTypeDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TOfficialTypeDTO> queryList(TOfficialTypeDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TOfficialTypeDTO findOne(Long id);

    /**
     * 新增
     */
    TOfficialTypeDTO create(TOfficialTypeDTO entity);


    /**
     * 更新
     */
    int update(TOfficialTypeDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTOfficialTypeId
     */
    boolean existByTOfficialTypeId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TOfficialTypeDTO> dataList);


}
