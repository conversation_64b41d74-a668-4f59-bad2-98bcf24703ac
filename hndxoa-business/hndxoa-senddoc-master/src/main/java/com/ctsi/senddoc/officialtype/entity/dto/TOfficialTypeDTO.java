package com.ctsi.senddoc.officialtype.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公文类型表 t_official_type
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TOfficialTypeDTO对象", description="公文类型表 t_official_type")
public class TOfficialTypeDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公文类型名称
     */
    @ApiModelProperty(value = "公文类型名称")
    private String officialName;

    /**
     * 公文的类型
     */
    @ApiModelProperty(value = "公文的类型")
    private String officialType;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;


}
