package com.ctsi.senddoc.senddocmaster.controller;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianVersionService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.DateUtils;
import com.ctsi.hndx.utils.HaoQianUtils;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.senddoc.senddocmaster.entity.TSenddocMaster;
import com.ctsi.senddoc.senddocmaster.entity.dto.TSenddocMasterDTO;
import com.ctsi.senddoc.senddocmaster.service.ITSenddocMasterService;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import org.springframework.web.multipart.MultipartFile;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSenddocMaster")
@Api(value = "公文发文表 公文发文表", tags = "公文发文表 公文发文表接口")
public class TSenddocMasterController extends BaseController {

    private static final String ENTITY_NAME = "tSenddocMaster";

    @Autowired
    private ITSenddocMasterService tSenddocMasterService;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private HaoQianUtils haoQianUtils;

    @Autowired
    private ICscpHaoqianVersionService cscpHaoqianVersionService;



    /**
     *  新增公文发文表 公文发文表批量数据.
     */
    @PostMapping("/createBatch")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增公文发文表 公文发文表批量数据.")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    public ResultVO createBatch(@RequestBody List<TSenddocMasterDTO> tSenddocMasterList) {
       Boolean  result = tSenddocMasterService.insertBatch(tSenddocMasterList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增公文发文")
    @ApiOperation(value = "新增", notes = "传入参数")
    public ResultVO<TSenddocMasterDTO> create(@RequestBody TSenddocMasterDTO tSenddocMasterDTO) throws URISyntaxException {
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        tSenddocMasterDTO.setMobile(currentCscpUserDetail.getMobile());
        if (StringUtil.isEmpty(tSenddocMasterDTO.getDepartmentName())){
            tSenddocMasterDTO.setDepartmentName(currentCscpUserDetail.getDepartmentName());
        }

        if (StringUtil.isEmpty(tSenddocMasterDTO.getCompanyName())){
            tSenddocMasterDTO.setCompanyName(currentCscpUserDetail.getCompanyName());
        }

        if (StringUtil.isEmpty(tSenddocMasterDTO.getAgentPeople())){
            tSenddocMasterDTO.setAgentPeople(currentCscpUserDetail.getRealName());
        }

//        String archivesSign =tSenddocMasterService.getElectronArchivesNorm();//获取档案标识
//        if(!archivesSign.isEmpty())
//        {
//            String[] srts = archivesSign.split("-");
//            tSenddocMasterDTO.setSerialNumber(srts[4]);
//            tSenddocMasterDTO.setArchivesSign(archivesSign);
//        }

        TSenddocMasterDTO result = tSenddocMasterService.create(tSenddocMasterDTO);

        //增加档案流水号
//        bizSerialNumberService.saverUpdateSerialNumbe();

        // TODO: 2023/4/10 wubin 将正文pdf上传到好签服务器，并且保存好签签批版本管理
//        CscpHaoqianVersionDTO cscpHaoqianVersionDTO=new CscpHaoqianVersionDTO();
//        cscpHaoqianVersionDTO.setFormDataId(tSenddocMasterDTO.getId());
//        cscpHaoqianVersionDTO.setProcTypeName("发文");
//        cscpHaoqianVersionDTO.setTaskLink("版本1");
//        cscpHaoqianVersionDTO.setVersionNumber(1);
//        tSenddocMasterService.copyPdfToHaoqian(cscpHaoqianVersionDTO);
//        try {
//            //获取pdf文件字节流
//            MultipartFile file =cscpDocumentFileService.getDocumentMultipartFile(tSenddocMasterDTO.getId(),"2");
//            //上传pdf文件到好签服务器
//            String signid=haoQianUtils.uploadFileToHaoQian(file,null,null,null);
//            CscpHaoqianVersionDTO cscpHaoqianVersionDTO=new CscpHaoqianVersionDTO();
//            cscpHaoqianVersionDTO.setFormDataId(tSenddocMasterDTO.getId());
//            cscpHaoqianVersionDTO.setSignId(signid);
//            cscpHaoqianVersionDTO.setProcTypeName("发文");
//            cscpHaoqianVersionDTO.setTaskLink("拟稿");
//            cscpHaoqianVersionDTO.setVersionNumber(1);
//            cscpHaoqianVersionService.create(cscpHaoqianVersionDTO);
//        }catch (Exception e){
//            log.info("发文拟稿复制好签pdf失败"+e.toString());
//        }
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新公文发文")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO update(@RequestBody TSenddocMasterDTO tSenddocMasterDTO) {
	    Assert.notNull(tSenddocMasterDTO.getId(), "general.IdNotNull");
        int count = tSenddocMasterService.update(tSenddocMasterDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除存在的公文发文")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tSenddocMasterService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TSenddocMasterDTO tSenddocMasterDTO = tSenddocMasterService.findOne(id);
        return ResultVO.success(tSenddocMasterDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTSenddocMasterPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TSenddocMasterDTO>> queryTSenddocMasterPage(TSenddocMasterDTO tSenddocMasterDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSenddocMasterService.queryListPage(tSenddocMasterDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTSenddocMaster")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<ResResult<TSenddocMasterDTO>> queryTSenddocMaster(TSenddocMasterDTO tSenddocMasterDTO) {
       List<TSenddocMasterDTO> list = tSenddocMasterService.queryList(tSenddocMasterDTO);
       return ResultVO.success(new ResResult<TSenddocMasterDTO>(list));
   }

    /**
    * 根据文号查询文号是否重复，返回当前的最新文号
    */
    @GetMapping("/queryTSenddocMasterByReferenceNumber")
    @ApiOperation(value = "根据文号查询文号是否重复，返回当前的最新文号,返回中false表示已经存在，true表示不存在", notes = "传入公文文号")
    public ResultVO<Boolean> queryTSenddocMasterByReferenceNumber(@RequestParam  String referenceNumber) {

        LambdaQueryWrapper<TSenddocMaster> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TSenddocMaster::getReferenceNumber,referenceNumber).select(TSenddocMaster::getId);
        List<TSenddocMaster> list = tSenddocMasterService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(list)){
            //用过
            return ResultVO.success(true);
        }else {
            //没用过
            return ResultVO.success(false);
        }
    }

}
