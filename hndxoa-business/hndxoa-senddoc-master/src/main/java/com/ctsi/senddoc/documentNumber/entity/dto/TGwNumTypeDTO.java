package com.ctsi.senddoc.documentNumber.entity.dto;

import com.ctsi.hndx.annotations.Dict;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.ctsi.hndx.mybatisplus.query.QueryCondition;
import com.ctsi.hndx.mybatisplus.query.QueryConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TGwNumTypeDTO对象", description = "")
public class TGwNumTypeDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    @QueryCondition(value = QueryConditionEnum.LIKE)
    private String documentNumber;

    /**
     * 公文类型（1：发文 2：收文）
     */
    @ApiModelProperty(value = "公文类型（1：发文 2：收文），调用数据字典gongwentype")
    @Dict(dicCode = "documentType")
    private String documentType;

    /**
     * 文号类型  (1:综合性文号 2:部门文号)
     */
    @ApiModelProperty(value = "文号类型  (1:综合性文号 2:部门文号) 调用数据字典wenhaotype")
    private String documentNumberType;

    /**
     * 文号前缀
     */
    @ApiModelProperty(value = "文号前缀")
    private String symbolPrefix;

    /**
     * 文号后缀
     */
    @ApiModelProperty(value = "文号后缀")
    private String symbolSuffix;

    /**
     * 文号结尾
     */
    @ApiModelProperty(value = "文号结尾")
    private String symbolEnding;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String particularYear;

    /**
     * 编号位数
     */
    @ApiModelProperty(value = "编号位数")
    private Integer numberDigit;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 所属部门id
     */
    @ApiModelProperty(value = "所属部门id")
    private Long belongingDepartmentId;


    /**
     * 所属部门名称
     */
    @ApiModelProperty(value = "所属部门名称")
    private String belongingDepartmentName;

    /**
     * 文号分类id
     */
    @ApiModelProperty(value = "文号分类id")
    private Long documentNumberClassificationId;

    /**
     * 文号分类.
     */
    @ApiModelProperty(value = "文号分类名称")
    private String documentNumberClassificationName;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效 1有效 0无效")
    private String whetherEffective;
}
