package com.ctsi.senddoc.documentNumber.service;

import com.ctsi.senddoc.documentNumber.entity.dto.TGwNumTypeDTO;
import com.ctsi.senddoc.documentNumber.entity.TGwNumType;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
public interface ITGwNumTypeService extends SysBaseServiceI<TGwNumType> {


    /**
     * 分页查询
     */
    PageResult<TGwNumTypeDTO> queryListPage(TGwNumTypeDTO tGwNumTypeDTO,BasePageForm page);

    /**
     * 根据主键id获取单个对象
     */
    TGwNumTypeDTO findOne(Long id);

    /**
     * 新增
     */
    TGwNumTypeDTO create(TGwNumTypeDTO entity);

    /**
     * 更新
     */
    int update(TGwNumTypeDTO entity);

    /**
     * 删除
     */
    int delete(List<Long> id);


}
