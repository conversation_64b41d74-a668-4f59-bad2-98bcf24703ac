package com.ctsi.senddoc.documentNumber.controller;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Optional;

import com.ctsi.senddoc.documentNumber.entity.TGwNumType;
import com.ctsi.senddoc.documentNumber.entity.dto.TGwNumTypeDTO;
import com.ctsi.senddoc.documentNumber.service.ITGwNumTypeService;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tGwNumType")
@Api(value = "公文文号", tags = "公文文号 接口")
public class TGwNumTypeController extends BaseController {

    private static final String ENTITY_NAME = "tGwNumType";

    @Autowired
    private ITGwNumTypeService tGwNumTypeService;


    /**
     * 新增公文文号.
     */
    @PostMapping("/create")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增公文文号")
    @ApiOperation(value = "新增公文文号", notes = "传入参数")
    public ResultVO<TGwNumTypeDTO> create(@RequestBody TGwNumTypeDTO tGwNumTypeDTO) throws URISyntaxException {
        TGwNumTypeDTO result = tGwNumTypeService.create(tGwNumTypeDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新存在的公文文号")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO update(@RequestBody TGwNumTypeDTO tGwNumTypeDTO) {
        Assert.notNull(tGwNumTypeDTO.getId(), "general.IdNotNull");
        int count = tGwNumTypeService.update(tGwNumTypeDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除存在的公文文号")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable List<Long> id) {
        int count = tGwNumTypeService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TGwNumTypeDTO tGwNumTypeDTO = tGwNumTypeService.findOne(id);
        return ResultVO.success(tGwNumTypeDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryTGwNumTypePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TGwNumTypeDTO>> queryTGwNumTypePage(TGwNumTypeDTO tGwNumTypeDTO, BasePageForm basePageForm) {
        return ResultVO.success(tGwNumTypeService.queryListPage(tGwNumTypeDTO, basePageForm));
    }


}
