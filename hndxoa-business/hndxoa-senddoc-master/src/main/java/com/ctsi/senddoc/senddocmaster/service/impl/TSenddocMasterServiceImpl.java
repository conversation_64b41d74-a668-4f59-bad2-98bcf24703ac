package com.ctsi.senddoc.senddocmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianVersionService;
import com.ctsi.hndx.utils.*;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.senddoc.senddocmaster.entity.TSenddocMaster;
import com.ctsi.senddoc.senddocmaster.entity.dto.TSenddocMasterDTO;
import com.ctsi.senddoc.senddocmaster.mapper.TSenddocMasterMapper;
import com.ctsi.senddoc.senddocmaster.service.ITSenddocMasterService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 公文发文表 公文发文表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */

@Slf4j
@Service
public class TSenddocMasterServiceImpl extends SysBaseServiceImpl<TSenddocMasterMapper, TSenddocMaster> implements ITSenddocMasterService {

    @Autowired
    private TSenddocMasterMapper tSenddocMasterMapper;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private HaoQianUtils haoQianUtils;

    @Autowired
    private ICscpHaoqianVersionService cscpHaoqianVersionService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSenddocMasterDTO> queryListPage(TSenddocMasterDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TSenddocMaster> queryWrapper = new LambdaQueryWrapper();

        IPage<TSenddocMaster> pageData = tSenddocMasterMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TSenddocMasterDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TSenddocMasterDTO.class));

        return new PageResult<TSenddocMasterDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSenddocMasterDTO> queryList(TSenddocMasterDTO entityDTO) {
        LambdaQueryWrapper<TSenddocMaster> queryWrapper = new LambdaQueryWrapper();
        List<TSenddocMaster> listData = tSenddocMasterMapper.selectList(queryWrapper);
        List<TSenddocMasterDTO> TSenddocMasterDTOList = ListCopyUtil.copy(listData, TSenddocMasterDTO.class);
        return TSenddocMasterDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSenddocMasterDTO findOne(Long id) {
        TSenddocMaster tSenddocMaster = tSenddocMasterMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tSenddocMaster, TSenddocMasterDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TSenddocMasterDTO create(TSenddocMasterDTO entityDTO) {
        TSenddocMaster tSenddocMaster = BeanConvertUtils.copyProperties(entityDTO, TSenddocMaster.class);
        try{
            save(tSenddocMaster);
        }catch (Exception e){
            throw new BusinessException(e.toString());
        }
        return BeanConvertUtils.copyProperties(tSenddocMaster, TSenddocMasterDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TSenddocMasterDTO entity) {
        TSenddocMaster tSenddocMaster = BeanConvertUtils.copyProperties(entity, TSenddocMaster.class);
        return tSenddocMasterMapper.updateById(tSenddocMaster);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return tSenddocMasterMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSenddocMasterId
     * @return
     */
    @Override
    public boolean existByTSenddocMasterId(Long TSenddocMasterId) {
        if (TSenddocMasterId != null) {
            LambdaQueryWrapper<TSenddocMaster> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSenddocMaster::getId, TSenddocMasterId);
            List<TSenddocMaster> result = tSenddocMasterMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<TSenddocMasterDTO> dataList) {
        List<TSenddocMaster> result = ListCopyUtil.copy(dataList, TSenddocMaster.class);
        return saveBatch(result);
    }

    /**
     * 异步复制正文pdf到好签
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void copyPdfToHaoqian(CscpHaoqianVersionDTO entityDTO) {
        try {
            //获取pdf文件字节流
            MultipartFile file = cscpDocumentFileService.getDocumentMultipartFileSync(entityDTO.getFormDataId(), "2");
            //上传pdf文件到好签服务器
            String signid = haoQianUtils.uploadFileToHaoQian(file, null, null, null);
            entityDTO.setSignId(signid);
            cscpHaoqianVersionService.create(entityDTO);
        } catch (Exception e) {
            log.info("发文拟稿复制好签pdf失败" + e.toString());
        }
    }

    @Override
    public String getElectronArchivesNorm() {
        //党政机关电子公文OID固定为:**********。
        String OID = "**********";
        String strOrgCode = SecurityUtils.getCurrentCscpUserDetail().getPathCode();

        String creditCode = SecurityUtils.getCurrentCscpUserDetail().getCreditCode();//获取统一社会信用代码
        String dzCode = SecurityUtils.getCurrentCscpUserDetail().getDzCode();//代子编码

        if (StringUtils.isEmpty(creditCode)) {
            creditCode = "00000000000000" + strOrgCode;
        }
        if (StringUtils.isEmpty(dzCode)) {
            dzCode = "XX";
        }


        //内设机构或下属单位代码000表示公文由本单位顶级机构制发。
        String OrganizationCode = SecurityUtils.getCurrentCscpUserDetail().getOrganizationCode();
        if (StringUtils.isEmpty(OrganizationCode)) {
            OrganizationCode = "000";
        }
        //获取当前年份
        String year = String.valueOf(DateUtils.getYear());
        //档案流水号

        String serialNumber = "";

//        LambdaQueryWrapper<BizSerialNumber> queryWrapper = Wrappers.lambdaQuery();
//        queryWrapper.eq(BizSerialNumber::getCompanyId, SecurityUtils.getCurrentCompanyId());
//        queryWrapper.eq(BizSerialNumber::getYear,DateUtils.getYear());
//        BizSerialNumber bizSerialNumber = bizSerialNumberMapper.selectOne(queryWrapper);
//
//        if(bizSerialNumber.getSerialNumber()==null){
//            serialNumber = String.format("%05d",1);
//
//        }else{
//            serialNumber = String.format("%05d",bizSerialNumber.getSerialNumber()+1);
//        }

        //校验号
        StringBuffer sb = new StringBuffer();
        sb.append(creditCode);
        sb.append(OrganizationCode);
        sb.append(year);
        sb.append(dzCode);
        sb.append(serialNumber);

        //获取校验号
        int checkNumber = calculateCheckDigit(sb.toString());

        StringBuffer strs = new StringBuffer();
        strs.append(OID);
        strs.append(".");
        strs.append(creditCode);
        strs.append("-");
        strs.append(OrganizationCode);
        strs.append("-");
        strs.append(year);
        strs.append("-");
        strs.append(dzCode);
        strs.append("-");
        strs.append(serialNumber);
        strs.append("-");
        strs.append(checkNumber);
        return strs.toString();

    }

    public int calculateCheckDigit(String checkData) {
        int baseNum = 0;
        int length = checkData.length();

        for(int i = 0; i <= length-1; i++) {
            baseNum = baseNum + charToNumber(checkData.substring(i, i+1));

            if(baseNum > 36) {
                baseNum = baseNum - 36;
            }
            baseNum = baseNum * 2;
            if(baseNum >= 37) {
                baseNum = baseNum - 37;
            }
            System.out.println("第"+(i+1)+"步:"+baseNum);
        }
        baseNum = 37 - baseNum;
        if(baseNum == 36) {
            baseNum = 0;
        }

        return baseNum;
    }

    public int charToNumber(String value) {
        value = value.toUpperCase();
        switch (value) {
            case "0":
                return 0;
            case "1":
                return 1;
            case "2":
                return 2;
            case "3":
                return 3;
            case "4":
                return 4;
            case "5":
                return 5;
            case "6":
                return 6;
            case "7":
                return 7;
            case "8":
                return 8;
            case "9":
                return 9;
            case "A":
                return 10;
            case "B":
                return 11;
            case "C":
                return 12;
            case "D":
                return 13;
            case "E":
                return 14;
            case "F":
                return 15;
            case "G":
                return 16;
            case "H":
                return 17;
            case "I":
                return 18;
            case "J":
                return 19;
            case "K":
                return 20;
            case "L":
                return 21;
            case "M":
                return 22;
            case "N":
                return 23;
            case "O":
                return 24;
            case "P":
                return 25;
            case "Q":
                return 26;
            case "R":
                return 27;
            case "S":
                return 28;
            case "T":
                return 29;
            case "U":
                return 30;
            case "V":
                return 31;
            case "W":
                return 32;
            case "X":
                return 33;
            case "Y":
                return 34;
            case "Z":
                return 35;
            default:
                return -1;
        }
    }
}
