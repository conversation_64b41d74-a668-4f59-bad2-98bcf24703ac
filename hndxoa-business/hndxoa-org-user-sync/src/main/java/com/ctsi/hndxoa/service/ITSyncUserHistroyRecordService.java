package com.ctsi.hndxoa.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistoryRecordBackDTO;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;

import java.util.List;

/**
 * <p>
 * 同步机构历史记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface ITSyncUserHistroyRecordService extends SysBaseServiceI<TSyncUserHistroyRecord> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TSyncUserHistroyRecordDTO> queryListPage(TSyncUserHistroyRecordDTO entityDTO, BasePageForm page);

    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TSyncUserHistroyRecordDTO> queryListPageByRole(TSyncUserHistroyRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TSyncUserHistroyRecordDTO> queryList(TSyncUserHistroyRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TSyncUserHistroyRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TSyncUserHistroyRecordDTO create(TSyncUserHistroyRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TSyncUserHistroyRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTSyncUserHistroyRecordId
     * @param code
     * @return
     */
    boolean existByTSyncUserHistroyRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TSyncUserHistroyRecordDTO> dataList);

    /**
     * 同步回调
     *
     * @param dto
     */
    void syncCallback(TSyncUserHistoryRecordBackDTO dto);

    /**
     * 根据应用信息、用户信息-生成一条推送记录
     * @param userId
     * @param appSystemManage
     * @return
     */
    TSyncUserHistroyRecordDTO generateUserHistoryRecord(Long userId, TSyncAppSystemManage appSystemManage, CscpUserDetail cscpUserDetail);
}
