package com.ctsi.hndxoa.pull.service;

import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndxoa.mq.producer.PassiveSyncProducer;
import com.ctsi.hndxoa.pull.entity.dto.PassiveSyncMessage;
import com.ctsi.ssdc.login.LoginParent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/10:11
 * description:
 */
@Component
public class PassiveSendMessageListener extends LoginParent {

    @Autowired
    private PassiveSyncProducer passiveSyncProducer;

    @EventListener
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void onLoginEvent(PassiveSyncMessage passiveSyncMessage) {
        passiveSyncProducer.sendOneMessage(JSONObject.toJSONString(passiveSyncMessage));
    }

    @EventListener
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void onLoginEvent(List<PassiveSyncMessage> list) {
        passiveSyncProducer.sendManyMessage(JSONObject.toJSONString(list));
    }
}
