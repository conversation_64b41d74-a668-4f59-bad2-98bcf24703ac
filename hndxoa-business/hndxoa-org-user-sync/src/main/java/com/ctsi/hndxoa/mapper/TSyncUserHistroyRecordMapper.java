package com.ctsi.hndxoa.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 同步机构历史记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@DS(DbConst.MASTER)
public interface TSyncUserHistroyRecordMapper extends MybatisBaseMapper<TSyncUserHistroyRecord> {

    Integer selectRecordCount(@Param("entityDTO") TSyncUserHistroyRecordDTO entityDTO);

    @InterceptorIgnore(tenantLine = "true")
    Integer selectRecordCountNoAdd(@Param("entityDTO") TSyncUserHistroyRecordDTO entityDTO);


    List<TSyncUserHistroyRecordDTO> selectRecordList(@Param("entityDTO") TSyncUserHistroyRecordDTO entityDTO, @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize);

    @InterceptorIgnore(tenantLine = "true")
    List<TSyncUserHistroyRecordDTO> selectRecordListNoAdd(@Param("entityDTO") TSyncUserHistroyRecordDTO entityDTO, @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize);

    @InterceptorIgnore(tenantLine = "true")
    List<Long> queryCscpUserList( @Param("orgId") Long orgId );

    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT COUNT(*) AS total FROM t_sync_user_histroy_record ${ew.customSqlSegment}")
    Integer selectCountWithAlias(@Param(Constants.WRAPPER) Wrapper<TSyncUserHistroyRecord> queryWrapper);

}
