package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * 同步机构历史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TSyncUserHistroyRecordDTO对象", description="同步机构历史记录表")
public class TSyncUserHistroyRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应用系统ID
     */
    @ApiModelProperty(value = "应用系统ID")
    private Long appId;

    @ApiModelProperty(value = "应用系统名称")
    private String appName;

    @ApiModelProperty(value = "系统编号")
    private String appCode;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 商信用户统一id
     */
    @ApiModelProperty(value = "商信用户统一id")
    private String strId;

    /**
     * 用户登录名
     */
    @ApiModelProperty(value = "用户登录名")
    private String strUserId;

    /**
     * 用户密级（10:普通　20:秘密　30:机密）
     */
    @ApiModelProperty(value = "用户密级（10:普通　20:秘密　30:机密）")
    private String strClassified;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String strCname;

    @ApiModelProperty(value = "用户名")
    private String loginName;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String strEmail;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String strMobile;

    /**
     * 办公电话
     */
    @ApiModelProperty(value = "办公电话")
    private String strOPhone;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String strPosttalAddress;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String strSex;

    /**
     * 信任号
     */
    @ApiModelProperty(value = "信任号")
    private String strTrustNo;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String strDuty;

    /**
     * 岗位
     */
    @ApiModelProperty(value = "岗位")
    private String strPost;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号")
    private String strIdCardNo;

    /**
     * 单位信任号
     */
    @ApiModelProperty(value = "单位信任号")
    private String strUnitTrustNo;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private String strUnitId;

    /**
     * 同步操作类型
     */
    @ApiModelProperty(value = "同步操作类型")
    private String strOperaType;

    /**
     * 同步是否成功
     */
    @ApiModelProperty(value = "同步是否成功")
    private String syncSuccess;

    /**
     * 同步异常信息
     */
    @ApiModelProperty(value = "同步异常信息")
    private String syncMessage;

    /**
     * 同步响应状态码 （200:正常响应　1001:信任号已存在　1002:父级单位不存在）
     */
    @ApiModelProperty(value = "同步响应状态码 （200:正常响应　1001:信任号已存在　1002:父级单位不存在）")
    private String syncStatus;

    /**
     * 是否内部系统推送 0-否，1-是
     */
    @ApiModelProperty(value = "是否内部系统推送 0-否，1-是")
    private Integer inSystemFlag;

    @ApiModelProperty(value = "推送时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "推送方式")
    private String requestMode;

    /**
     * 0-手动推送，1-自动推送
     */
    @ApiModelProperty(value = "0-手动推送，1-自动推送")
    private String operateType;

    @ApiModelProperty(value = "创建时间从")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;
    @ApiModelProperty(value = "创建时间至")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty(value = "拉取次数")
    private Integer syncCount;
}
