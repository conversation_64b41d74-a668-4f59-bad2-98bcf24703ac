package com.ctsi.hndxoa.pull.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * MQ消息实体：记录计数器类型、historyId和计数
 */
@Data
public class CountMessage implements Serializable {
    // 计数类型（org/user）
    private int type;
    // 历史ID
    private Long historyId;
    // 当前计数
    private Long count;

    public CountMessage(int type, Long historyId, Long count) {
        this.type = type;
        this.historyId = historyId;
        this.count = count;
    }

    public CountMessage() {}


    /**
     * 重写toString方法，返回对象属性的字符串表示
     */
    @Override
    public String toString() {
        return "CountMessage{" +
                "type=" + type +
                ", historyId=" + historyId +
                ", count=" + count +
                '}';
    }
}