package com.ctsi.hndxoa.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.hndxoa.entity.dto.SyncReceiveOrgDTO;
import com.ctsi.hndxoa.entity.dto.SyncReceiveOrgTree;
import com.ctsi.hndxoa.service.ISyncReceiveOrgService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("ALL")
public class SyncReceiveReceiveOrgServiceImpl implements ISyncReceiveOrgService {

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private ISysImportService iSysImportService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReceiveOrg(SyncReceiveOrgDTO data) {
        if (CollectionUtil.isEmpty(data.getDataList())) {
            throw new BusinessException("参数不能为空!");
        }
        if (null == data.getRootId()) {
            throw new BusinessException("添加目标根节点不存在!");
        }
        // 根节点 org对象
        LambdaQueryWrapper<CscpOrg> rootOrgLqw = Wrappers.lambdaQuery(CscpOrg.class);
        rootOrgLqw.eq(CscpOrg::getExternalId, data.getRootId());
        CscpOrg rootOrg = cscpOrgRepository.selectOneNoAdd(rootOrgLqw);
        if (null == rootOrg) {
            throw new BusinessException("根节点获取失败!");
        }

        for (SyncReceiveOrgDTO orgDTO : data.getDataList()) {
            LambdaQueryWrapper<CscpOrg> dataOrgLqw = Wrappers.lambdaQuery(CscpOrg.class);
            // 如果数据中的parentId为null或者为0,获取上级机构信息, 否则将rootId查询出来的机构当作它的上级机构
            if (null != orgDTO.getParentId() || !"0".equals(orgDTO.getParentId())) {
                dataOrgLqw.eq(CscpOrg::getExternalId, orgDTO.getParentId());
            } else {
                dataOrgLqw.eq(CscpOrg::getOrgCode, rootOrg.getOrgCode());
            }
            CscpOrg parentOrg = cscpOrgRepository.selectOneNoAdd(dataOrgLqw);
            if (null == parentOrg) {
                throw new BusinessException("机构: {}, 上级机构不存在!", orgDTO.getName());
            }

            dataOrgLqw.clear();
            dataOrgLqw.eq(CscpOrg::getExternalId, orgDTO.getId());
            CscpOrg thisOrg = cscpOrgRepository.selectOneNoAdd(dataOrgLqw);
            // 如果机构外部id已经存在，则修改机构信息; 否则生成新的机构数据
            if (null != thisOrg) {
                // 修改->仅变更机构名称
                thisOrg.setOrgName(orgDTO.getName());
                cscpOrgRepository.updateById(thisOrg);
            } else {
                CscpOrg cscpOrg = getCscpOrg(orgDTO, parentOrg);
                cscpOrgRepository.insert(cscpOrg);
            }

        }
    }

    /**
     * 接口新增数据时SyncReceiveOrgDTO对象转储Cscp_Org对象并生产新的机构编码
     * @param orgDTO
     * @param parentOrg
     * @return
     */
    private CscpOrg getCscpOrg(SyncReceiveOrgDTO orgDTO, CscpOrg parentOrg) {
        CscpOrg cscpOrg = new CscpOrg();
        cscpOrg.setOrgName(orgDTO.getName());
        String code = parentOrg.getOrgCode() + generateLevelCode(1, parentOrg.getMaxNumber());
        cscpOrg.setOrgCode(code);
        cscpOrg.setParentId(parentOrg.getParentId());
        cscpOrg.setOrgCodePath(parentOrg.getOrgCodePath() + "|" + code);
        cscpOrg.setOrgIdPath(StringUtils.isEmpty(parentOrg.getOrgIdPath()) ?
                parentOrg.getId().toString() : parentOrg.getOrgIdPath() + "|" + parentOrg.getId());
        cscpOrg.setOrderBy(90000);
        cscpOrg.setOrderByPath(StringUtils.isNotEmpty(parentOrg.getOrderByPath()) ?
                (parentOrg.getOrderByPath() + (100000 + cscpOrg.getOrderBy())) : String.valueOf((100000 + cscpOrg.getOrderBy())));
        cscpOrg.setHasSms(0);
        cscpOrg.setHasWatermark(0);
        cscpOrg.setType(2);
        cscpOrg.setExternalId(orgDTO.getId());
        return cscpOrg;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importReceiveOrg(String rootId, List<SyncReceiveOrgTree> dataList) {
        List<SyncReceiveOrgTree> failedList = new ArrayList<>();

        log.info("外部导入机构数据总数: {}", dataList.size());

        if (CollectionUtil.isEmpty(dataList)) {
            throw new BusinessException("导入数据不存在!");
        }
        // 将导入集合转成Map对象
        Map<String, SyncReceiveOrgTree> dataMap =
                dataList.stream().collect(Collectors.toMap(SyncReceiveOrgTree::getId, Function.identity()));

        // 根据入参id获取顶级节点
        SyncReceiveOrgTree rootOrgDTO = dataMap.get(rootId);

        // 根据cscp_org表中的外部业务id、机构名称获取顶级机构信息
        LambdaQueryWrapper<CscpOrg> rootOrgLqw = Wrappers.lambdaQuery(CscpOrg.class)
                .eq(CscpOrg::getExternalId, rootOrgDTO.getId()).or().eq(CscpOrg::getOrgName, rootOrgDTO.getName());
        CscpOrg cscpOrg = cscpOrgService.selectOneNoAdd(rootOrgLqw);
        if(null == cscpOrg) {
            throw new BusinessException("根节点获取错误!");
        }

        // 设置顶级机构的externalId(外部业务主键id)
        LambdaUpdateWrapper<CscpOrg> orgLuw = Wrappers.lambdaUpdate();
        orgLuw.set(CscpOrg::getExternalId, rootOrgDTO.getId());
        orgLuw.eq(CscpOrg::getId, cscpOrg.getId());
        cscpOrgService.update(orgLuw);

        // 顶级节点必定存在，且其参数无须更改。故而将它从map中移除，防止影响后续操作
        dataMap.remove(rootId);
        dataList = new ArrayList<>(dataMap.values());

        // 将一级节点的parentId,先改为null值,方便后面的迭代方法
        dataList = dataList.stream().peek(d -> {
            if(rootId.equals(d.getParentId())) d.setParentId(null);
        }).collect(Collectors.toList());

        // 获取顶级机构最大编码号和机构编码
        int maxNumber = cscpOrg.getMaxNumber();
        String rootCode = cscpOrg.getOrgCode();

        // 查询顶级机构的子集，已存在的最大编码
        LambdaQueryWrapper<CscpOrg> childrenOrgLqw = Wrappers.lambdaQuery(CscpOrg.class);
        childrenOrgLqw.eq(CscpOrg::getParentId, cscpOrg.getId());
        childrenOrgLqw.last(" and CHAR_LENGTH(org_code) = 16 order by max_number desc limit 1");
        CscpOrg maxOrg = cscpOrgRepository.selectOneNoAdd(childrenOrgLqw);
        if (null != maxOrg) maxNumber = maxOrg.getMaxNumber();

        // 根据统一机构现有行政代码标识，将数据取出，并重新组装
        List<SyncReceiveOrgTree> existsList = dataList.stream().filter(d -> StringUtils.isNotEmpty(d.getIsExistsOrgCode())).collect(Collectors.toList());
        Set<String> existsOrgCodeList = existsList.stream().map(SyncReceiveOrgTree::getIsExistsOrgCode).collect(Collectors.toSet());
        if (existsList.size() != existsOrgCodeList.size()) {
            throw new BusinessException("填写统一机构现有行政代码,行数不一致!");
        }
        LambdaQueryWrapper<CscpOrg> existsLqw = Wrappers.lambdaQuery(CscpOrg.class);
        existsLqw.in(CscpOrg::getOrgCode, existsOrgCodeList);
        List<CscpOrg> existsOrgList = cscpOrgService.selectListNoAdd(existsLqw);
        if (CollectionUtils.isNotEmpty(existsOrgList)) {
            Map<String, CscpOrg> existsMap = existsOrgList.stream().collect(Collectors.toMap(CscpOrg::getOrgCode, Function.identity()));

            List<CscpOrg> oldChildrenOrgList = cscpOrgRepository.selectOrgChildrenMaxNumber(existsOrgCodeList);
            Map<Long, Integer> oldChildrenMaxNumberMap = oldChildrenOrgList.stream().collect(Collectors.toMap(CscpOrg::getParentId, CscpOrg::getMaxNumber));

            for (SyncReceiveOrgTree existsData : existsList) {
                CscpOrg existsOrg = existsMap.get(existsData.getIsExistsOrgCode());
                if (null != existsOrg) {
                    Integer childrenMaxNumber = oldChildrenMaxNumberMap.get(existsOrg.getId());
                    if (null != childrenMaxNumber) {
                        existsData.setOldMaxNumber(childrenMaxNumber);
                    }
                    existsData.setOldOrg(existsOrg);
                }
            }

            // 将已存在机构数据放回Map对象中
            for (SyncReceiveOrgTree existsData : existsList) {
                SyncReceiveOrgTree data = dataMap.get(existsData.getId());
                if (null != data) data = existsData;
            }
        }
        // 更新dataList的结果,后续组装Tree
        dataList = new ArrayList<>(dataMap.values());

        // 1、将数据组装成Map<Node>树层级,并排序
        Map<String, SyncReceiveOrgTree> tree = buildTree(dataList);
        Map<String, SyncReceiveOrgTree> sortTree = tree.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.comparing(SyncReceiveOrgTree::getOrderBy)))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));

        // 2、遍历Map<node>获取List<Node>集合
        List<SyncReceiveOrgTree> roots = sortTree.values().stream().filter(node -> node.getParentId() == null).collect(Collectors.toList());

        // 3、生成对应的机构编码，建立树层级父子节点的关联关系
        for (SyncReceiveOrgTree root : roots) {
            root.setGenerateParentId(cscpOrg.getId());
            generateCodes(root, rootCode, ++maxNumber);
            if (root.getGenerateOrgIdPath() == null) {
                root.setGenerateOrgIdPath(StringUtils.isEmpty(cscpOrg.getOrgIdPath()) ?
                        cscpOrg.getId().toString() : cscpOrg.getOrgIdPath()+ "|" + cscpOrg.getId());
            }
            if (root.getGenerateOrgCodePath() == null) {
                root.setGenerateOrgCodePath(StringUtils.isEmpty(cscpOrg.getOrgCodePath()) ?
                        root.getGenerateCode() : cscpOrg.getOrgCodePath() + "|" + root.getGenerateCode());
            }
        }

        // 4、迭代树结构,得到一个List<E>对象
        List<SyncReceiveOrgTree> treeList = this.levelOrderTraversal(roots);

        treeList = treeList.stream().sorted((t1, t2) -> Integer.compare(t1.getGenerateMaxNumber(), t2.getGenerateMaxNumber())).collect(Collectors.toList());
        // 5、导入数据转持久化数据
        LinkedHashMap<SyncReceiveOrgTree, CscpOrg> dmMap = new LinkedHashMap<>();
        for (SyncReceiveOrgTree data : treeList) {
            CscpOrg newOrg = importCreateOrg(data);
            dmMap.putIfAbsent(data, newOrg);
        }

        // 6、save导入结果
        if (CollectionUtil.isNotEmpty(dmMap)) {
            SecurityContext securityContext = SecurityContextHolder.getContext();
            log.info(">>>>>>>>>>>>>>外部导入机构开始 start<<<<<<<<<<<<<");
            // 使用线程池管理线程
            ExecutorService executorService = Executors.newFixedThreadPool(100);
            List<Future<?>> futures = new ArrayList<>();
            List<Map.Entry<SyncReceiveOrgTree, CscpOrg>> entryList = new ArrayList<>(dmMap.entrySet());
            // 将 entryList 均分到 100 个线程
            int batchSize = (entryList.size() + 99) / 100;
            for (int i = 0; i < entryList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, dmMap.size());
                List<Map.Entry<SyncReceiveOrgTree, CscpOrg>> batch = entryList.subList(i, end);

                futures.add(executorService.submit(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    for (Map.Entry<SyncReceiveOrgTree, CscpOrg> data : batch) {
                        try {
                            // 如果导入数据统一机构现有行政代码不为null,则修改机构
                            if (StringUtils.isNotEmpty(data.getKey().getIsExistsOrgCode())) {
                                cscpOrgRepository.updateById(data.getValue());
                            } else {
                                cscpOrgRepository.insert(data.getValue());
                                // 如果新增的机构，类型是单位，则创建单位管理员
                                if (2 == data.getValue().getType()) {
                                    if (StringUtils.isNotEmpty(data.getValue().getOrgCodePath())) {
                                        cscpOrgService.createCompanyAdmin2(data.getValue().getOrgCodePath());
                                    } else {
                                        cscpOrgService.createCompanyAdmin(data.getValue().getOrgCode());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            SyncReceiveOrgTree syncReceiveOrgTree = data.getKey();
                            log.error("导入机构{}失败, 失败原因:{}", syncReceiveOrgTree.getName(), e.getMessage(), e);
                            syncReceiveOrgTree.setFailedReason("机构: "+syncReceiveOrgTree.getName()+",导入错误! 原因: " + e.toString());
                            failedList.add(syncReceiveOrgTree);
                        }
                    }
                }));
            }

            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("任务执行失败: {}", e.getMessage(), e);
                }
            }

            // 关闭线程池
            executorService.shutdown();

            log.info(">>>>>>>>>>>>>>外部导入机构结束 end<<<<<<<<<<<<<");
        }

        TSysImportDTO sysOrgImportDTO = new TSysImportDTO();
        sysOrgImportDTO.setTotalNo(dataList.size());
        sysOrgImportDTO.setFailedNo(failedList.size());
        sysOrgImportDTO.setSuccessNo(dataList.size() - failedList.size());
        // 导入数据类型
        sysOrgImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.CHANG_DE_ORG_IMPORT));


        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysOrgImportDTO);
            return true;
        }

        // 保存导入记录，并上传Excel失败文件
        iSysImportService.saveAndUploadFile(sysOrgImportDTO, failedList, SyncReceiveOrgDTO.class, FileBasePathName.CHANG_DE_ORG_IMPORT);

        log.info(">>>>>>>>>>>>>>数据处理完成<<<<<<<<<<<<<");
        return true;
    }

    /**
     * 组装树结构
     * @param nodes
     * @return
     */
    public Map<String, SyncReceiveOrgTree> buildTree(List<SyncReceiveOrgTree> nodes) {
        Map<String, SyncReceiveOrgTree> nodeMap = new HashMap<>();
        Map<String, SyncReceiveOrgTree> tree = new HashMap<>();

        // 创建所有节点映射
        for (SyncReceiveOrgTree node : nodes) {
            nodeMap.put(node.getId(), node);
        }

        // 构建树结构
        for (SyncReceiveOrgTree node : nodes) {
            if (node.getParentId() == null || node.getParentId().isEmpty()) {
                tree.put(node.getId(), node);
            } else {
                SyncReceiveOrgTree parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    if (CollectionUtil.isEmpty(parent.getChildren())) parent.setChildren(new ArrayList<>());
                    parent.getChildren().add(node);
                }
            }
        }
        return tree;
    }

    /**
     * 为树层级生成Code
     * @param root
     * @param parentCode
     * @param maxNumber
     */
    public void generateCodes(SyncReceiveOrgTree root,
                              String parentCode,
                              int maxNumber) {
        if (root == null) return;

        Queue<SyncReceiveOrgTree> queue = new LinkedList<>();

        if (null != root.getOldOrg()) {
            // 如果机构之前就存在，则不走生成逻辑，但需要将存在的值放入SyncReceiveOrgTree，方便后面编码生成
            this.setOldOrgValue(root.getOldOrg(), root);
            root.setGenerateParentCode(parentCode);
        } else {
            root.setGenerateId(SnowflakeIdUtil.getSnowFlakeLongId());
            root.setGenerateMaxNumber(maxNumber);
            root.setGenerateType(2);
            root.setGenerateLevel(3);
            String generateCode = parentCode + generateLevelCode(1, maxNumber);
            root.setGenerateParentCode(parentCode);
            root.setGenerateCode(generateCode);
        }
        queue.add(root);

        while (!queue.isEmpty()) {
            SyncReceiveOrgTree node = queue.poll();
            int childIndex = 1;


            if (CollectionUtil.isNotEmpty(node.getChildren())) {
                for (SyncReceiveOrgTree child : node.getChildren()) {
                    if (null != child.getOldOrg()) {
                        this.setOldOrgValue(child.getOldOrg(), child);
                        child.setGenerateParentCode(node.getGenerateCode());
                    } else {
                        child.setGenerateId(SnowflakeIdUtil.getSnowFlakeLongId());
                        child.setGenerateParentId(node.getGenerateId());
                        child.setGenerateType(3);

                        int childrenMaxNumber = childIndex;
                        // 生成子节点编码 = 父编码 + 当前子节点序号
                        if (null != node.getOldMaxNumber()) {
                            childrenMaxNumber = node.getOldMaxNumber() + childIndex;
                        }
                        child.setGenerateMaxNumber(childrenMaxNumber);
                        int childrenLevel = 4;
                        if (null != node.getGenerateLevel()) {
                            childrenLevel = node.getGenerateLevel();
                            childrenLevel++;
                        }
                        child.setGenerateLevel(childrenLevel);

                        String childrenGenerateCode = node.getGenerateCode() + generateLevelCode(child.getLevel(),childrenMaxNumber);
                        child.setGenerateParentCode(node.getGenerateCode());
                        child.setGenerateCode(childrenGenerateCode);
                        child.setGenerateOrgIdPath(StringUtils.isEmpty(node.getGenerateOrgIdPath()) ?
                                node.getGenerateId().toString() : node.getGenerateOrgIdPath()+ "|" + node.getGenerateId());
                        child.setGenerateOrgCodePath(StringUtils.isEmpty(node.getGenerateOrgCodePath()) ?
                                child.getGenerateCode() : node.getGenerateOrgCodePath() + "|" + child.getGenerateCode());
                        childIndex++;
                    }
                    queue.add(child);
                }
            }
        }
    }

    /**
     * 编码向后追加4位
     * @param level
     * @param sequence
     * @return
     */
    private String generateLevelCode(int level, int sequence) {
        return String.format("%04d", sequence);
    }

    /**
     * 迭代平铺树
     * @param roots
     * @return
     */
    public List<SyncReceiveOrgTree> levelOrderTraversal(List<SyncReceiveOrgTree> roots) {
        List<SyncReceiveOrgTree> result = new ArrayList<>();
        if (roots == null || roots.isEmpty()) {
            return result;
        }

        Queue<SyncReceiveOrgTree> queue = new LinkedList<>();

        // 将所有根节点加入队列
        for (SyncReceiveOrgTree root : roots) {
            queue.offer(root);
        }

        while (!queue.isEmpty()) {
            SyncReceiveOrgTree node = queue.poll();
            result.add(node);

            if (CollectionUtil.isNotEmpty(node.getChildren())) {
                // 将当前节点的所有子节点加入队列
                for (SyncReceiveOrgTree child : node.getChildren()) {
                    queue.offer(child);
                }
            }
        }

        return result;
    }

    /**
     * 将Cscp_Org表中的关键数据存入SyncReceiveOrgTree
     * @param cscpOrg
     * @param node
     */
    private void setOldOrgValue(CscpOrg cscpOrg, SyncReceiveOrgTree node) {
        node.setName(StringUtils.isNotEmpty(node.getNewImportOrgName()) ? node.getNewImportOrgName() : cscpOrg.getOrgName());
        node.setGenerateId(cscpOrg.getId());
        node.setGenerateParentId(cscpOrg.getParentId());
        node.setGenerateType(cscpOrg.getType());
        node.setGenerateLevel(cscpOrg.getLevel());
        node.setGenerateMaxNumber(cscpOrg.getMaxNumber());
        node.setGenerateCode(cscpOrg.getOrgCode());
        node.setGenerateOrgIdPath(cscpOrg.getOrgIdPath());
        node.setGenerateOrgCodePath(cscpOrg.getOrgCodePath());
    }

    /**
     * SyncReceiveOrgTree导入数据转储成Cscp_Org机构表数据
     * @param data
     * @return
     */
    public CscpOrg importCreateOrg(SyncReceiveOrgTree data) {
        CscpOrg cscpOrg = new CscpOrg();
        if (null != data.getOldOrg()) {
            cscpOrg = data.getOldOrg();
            cscpOrg.setExternalId(data.getId());
        } else {
            cscpOrg.setId(data.getGenerateId());
            cscpOrg.setOrgName(data.getName());
            cscpOrg.setCode(data.getCode());
            cscpOrg.setParentId(data.getGenerateParentId());
            cscpOrg.setOrgCode(data.getGenerateCode());
            cscpOrg.setHasSms(0);
            cscpOrg.setHasWatermark(0);
            cscpOrg.setType(data.getGenerateType());
            cscpOrg.setCompanyId(cscpOrg.getId());
            cscpOrg.setOrderBy(Integer.valueOf(data.getOrderBy()));
            cscpOrg.setOrderByPath(100000 + cscpOrg.getOrderBy() + "");
            cscpOrg.setExternalId(data.getId());
            cscpOrg.setLevel(data.getGenerateLevel());
            cscpOrg.setMaxNumber(data.getGenerateMaxNumber());
            cscpOrg.setOrgOrigin("常德");
            cscpOrg.setOrgIdPath(data.getGenerateOrgIdPath());
            cscpOrg.setOrgCodePath(data.getGenerateOrgCodePath());
        }
        return cscpOrg;
    }
}
