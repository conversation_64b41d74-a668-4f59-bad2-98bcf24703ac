package com.ctsi.hndxoa.mq.consumer;


import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper;
import com.ctsi.hndxoa.mq.dto.MqResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消费下游端返回结果消息
 *
 * <AUTHOR>
 */
@Component
@SuppressWarnings("all")
public class OrgConsumer {

    @Autowired
    private TSyncOrgHistroyRecordMapper tSyncOrgHistroyRecordMapper;

    private static final Logger log = LoggerFactory.getLogger(OrgConsumer.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "orgQueue", durable = "true"),
            exchange = @Exchange(value = "orgExchange", type = "topic"),
            key = "orgResponse"
    ))
    public void onMessage(String message, Message amqpMessage) {
        String routingKey = amqpMessage.getMessageProperties().getReceivedRoutingKey();
        log.info("{},{} orgResponse: {}",
                this.getClass().getSimpleName(),
                routingKey,
                message);
        MqResponseDTO mqResponseDTO = JSONObject.parseObject(message, MqResponseDTO.class);
        TSyncOrgHistroyRecord orgRecord = tSyncOrgHistroyRecordMapper.selectById(mqResponseDTO.getSyncHistoryId());
        if (null != orgRecord) {
            try {
                if ("200".equals(mqResponseDTO.getStatus())) {
                    orgRecord.setStrOperaType(mqResponseDTO.getStrOperaType());
                    orgRecord.setSyncSuccess("true");
                    orgRecord.setSyncStatus("200");
                    orgRecord.setSyncMessage("操作成功");
                } else {
                    orgRecord.setStrOperaType(mqResponseDTO.getStrOperaType());
                    orgRecord.setSyncSuccess("false");
                    orgRecord.setSyncStatus("500");
                    orgRecord.setSyncMessage(null != mqResponseDTO.getMessage() ? mqResponseDTO.getMessage() : "应用处理异常");
                }
            } finally {
                tSyncOrgHistroyRecordMapper.updateById(orgRecord);
            }
        }
    }
}