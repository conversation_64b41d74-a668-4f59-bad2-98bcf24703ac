package com.ctsi.hndxoa.service.constant;


/**
 * 同步用户、组织信息常量
 *
 * <AUTHOR>
 */
public interface OrgUserConstants {

    public interface ThroughputType {

        /**
         * 全部
         */
        public static final String ALL = "ALL";
    }

    public interface SyncStatus {

        /**
         * 成功
         */
        public static final String S = "S";

        /**
         * 失败
         */
        public static final String F = "F";

        /**
         * 部分成功
         */
        public static final String P = "P";
    }


    public interface RedisKey {

        /**
         * t_sync_org_histroy_record
         */
        public static final String SYNC_OGR_HISTORY_ID = "tyjg:org:snyc:history";
        /**
         * t_sync_org_histroy_record
         */
        public static final String SYNC_USER_HISTORY_ID = "tyjg:user:snyc:history";

    }

    public interface RequestMode {
        public static final String REQUEST_MODE_MQ = "0";

        public static final String REQUEST_MODE_HTTP = "1";

        public static final String REQUEST_MODE_PULL_HTTP = "2";
    }

    public interface PushType {
        public static final int PUSH_UNIT = 0;

        public static final int PUSH_USER = 1;
    }

    public interface OperaType {
        public static final String ADD = "add";

        public static final String UPDATE = "update";

        public static final String DELETE = "delete";
    }

    public interface UnitType {
        /**
         * 省委办公厅单位id
         */
        public static final Long SWBGTID = 1697170157779361794L;
        /**
         * 省委办公厅单位orgCode
         */
        public static final String SWBGTCODE = "43000000000000100002";
    }

}
