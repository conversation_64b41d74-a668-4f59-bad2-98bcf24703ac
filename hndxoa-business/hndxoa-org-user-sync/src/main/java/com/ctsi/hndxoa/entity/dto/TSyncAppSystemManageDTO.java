package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 同步应用系统管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TSyncAppSystemManageDTO对象", description="同步应用系统管理表")
public class TSyncAppSystemManageDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应用系统名称
     */
    @ApiModelProperty(value = "应用系统名称")
    private String appName;

    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号")
    private String appCode;

    /**
     * 数据同步url
     */
    @ApiModelProperty(value = "数据同步url")
    private String syncUrl;

    /**
     * 状态 0-禁用，1-可用
     */
    @ApiModelProperty(value = "状态 0-禁用，1-可用")
    private Integer status;

    /**
     * 鉴权 0-不需要鉴权，1-需要鉴权
     */
    @ApiModelProperty(value = "鉴权 0-不需要鉴权，1-需要鉴权")
    private Integer authentication;

    /**
     * 三方 appId 用于后续鉴权
     */
    @ApiModelProperty(value = "三方 appId 用于后续鉴权")
    private String appId;

    /**
     * 三方 appKey 用于后续鉴权
     */
    @ApiModelProperty(value = "三方 appKey 用于后续鉴权")
    private String appKey;

    /**
     * 是否内部系统 0-否，1-是
     */
    @ApiModelProperty(value = "是否内部系统 0-否，1-是")
    private Integer inSystemFlag;

    /**
     * 内部系统同步接口url
     */
    @ApiModelProperty(value = "内部系统同步接口url")
    private String inSystemUrl;

    /**
     * 拥有机构编码
     */
    @ApiModelProperty(value = "拥有机构编码")
    private String inOwnedOrgId;

    @ApiModelProperty(value = "请求方式(0:MQ,1:HTTP[默认])")
    private String requestMode;

    @ApiModelProperty(value = "是否自动同步用户")
    private Integer isAutoSyncUser;

    @ApiModelProperty(value = "是否自动同步机构")
    private Integer isAutoSyncOrg;

    @ApiModelProperty(value = "自动推送(0:关闭,1:开启)")
    private String autoPush;

    @ApiModelProperty(value = "应用角色ID")
    private Long roleId;

    @ApiModelProperty(value = "应用角色名称")
    private String roleName;

    private Long companyId;

    @ApiModelProperty(value = "是否移交版主管理, 0-否, 1-是")
    private Integer moderatorFlag;

    private Map<Long, String> orgMap;

    @ApiModelProperty(value = "被动模式url")
    private String passiveUrl;

}
