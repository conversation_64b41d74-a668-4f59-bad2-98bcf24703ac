package com.ctsi.hndxoa.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistoryRecordBackDTO;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步机构历史记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Slf4j
@Service
@DS(DbConst.MASTER)
public class TSyncUserHistroyRecordServiceImpl extends SysBaseServiceImpl<TSyncUserHistroyRecordMapper, TSyncUserHistroyRecord> implements ITSyncUserHistroyRecordService {

    @Autowired
    private TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper;

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private CscpUserRepository cscpUserRepository;
    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSyncUserHistroyRecordDTO> queryListPage(TSyncUserHistroyRecordDTO entityDTO, BasePageForm basePageForm) {
        String loginUserName = SecurityUtils.getCurrentUserName();
        Integer recordCount;
        Integer startIndex = (basePageForm.getCurrentPage() - 1) * basePageForm.getPageSize();
        /*//设置条件
        if ("admin".equals(loginUserName)) {
            recordCount = tSyncUserHistroyRecordMapper.selectRecordCount(entityDTO);
        } else {
            entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
            recordCount = tSyncUserHistroyRecordMapper.selectRecordCountNoAdd(entityDTO);
        }



        List<TSyncUserHistroyRecordDTO> recordDTOList = new ArrayList<>();
        if(recordCount > 0){
            if ("admin".equals(loginUserName)) {
                recordDTOList =  tSyncUserHistroyRecordMapper.selectRecordList(entityDTO, startIndex, basePageForm.getPageSize());
            } else {
                entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
                recordDTOList = tSyncUserHistroyRecordMapper.selectRecordListNoAdd(entityDTO, startIndex, basePageForm.getPageSize());
            }
        }*/
        //改造第一步根据条件查询出APP数据
        if (!"admin".equals(loginUserName)) {
            entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
        }
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperApp = new LambdaQueryWrapper();
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppName()),TSyncAppSystemManage::getAppName,entityDTO.getAppName());
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppCode()),TSyncAppSystemManage::getAppCode,entityDTO.getAppCode());
        List<TSyncAppSystemManage> tSyncAppSystemManages = tSyncAppSystemManageService.selectListNoAdd(queryWrapperApp);
        Map<String, TSyncAppSystemManage> appMap = tSyncAppSystemManages.stream()
                .collect(Collectors.toMap(
                        app -> app.getId().toString(), // 提取 appId 作为 key
                        app -> app // value 就是对象本身
                ));
        //获取APPid集合
        List<String> appIdList = tSyncAppSystemManages.stream().map(i -> i.getId().toString()).collect(Collectors.toList());
        //组装数据
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapperRecord = new LambdaQueryWrapper();
        queryWrapperRecord.in(!CollectionUtils.isEmpty(appIdList),TSyncUserHistroyRecord::getAppId,appIdList)
                .eq(TSyncUserHistroyRecord::getDeleted,"0")
                .eq(StringUtils.isNotEmpty(entityDTO.getStrOperaType()),TSyncUserHistroyRecord::getStrOperaType,entityDTO.getStrOperaType())
                .eq(entityDTO.getUserId() != null,TSyncUserHistroyRecord::getUserId,entityDTO.getUserId())
                .eq(StringUtils.isNotEmpty(entityDTO.getSyncSuccess()),TSyncUserHistroyRecord::getSyncSuccess,entityDTO.getSyncSuccess())
                .like(StringUtils.isNotEmpty(entityDTO.getStrCname()),TSyncUserHistroyRecord::getStrCname,entityDTO.getStrCname())
                .like(StringUtils.isNotEmpty(entityDTO.getStrMobile()),TSyncUserHistroyRecord::getStrMobile,entityDTO.getStrMobile())
                .eq(!Objects.isNull(entityDTO.getCreateBy()),TSyncUserHistroyRecord::getCreateBy,entityDTO.getCreateBy())
                .ge(entityDTO.getCreateTimeStart()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeStart())
                .le(entityDTO.getCreateTimeEnd()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeEnd());
        if (StringUtils.isNotEmpty(entityDTO.getLoginName())) {
            queryWrapperRecord.and(qw ->
                    qw.like(StringUtils.isNotEmpty(entityDTO.getLoginName()), TSyncUserHistroyRecord::getLoginName,
                                    entityDTO.getLoginName())
                            .or()
                            .like(StringUtils.isNotEmpty(entityDTO.getLoginName()),
                                    TSyncUserHistroyRecord::getStrUserId, entityDTO.getLoginName()));
        }

        // 先执行COUNT查询（不包含ORDER BY）
        // 如果selectCountNoAdd有问题，临时使用标准方法
        recordCount = Math.toIntExact(tSyncUserHistroyRecordMapper.selectCountWithAlias(queryWrapperRecord));

        // 为数据查询添加ORDER BY
        queryWrapperRecord.orderByDesc(TSyncUserHistroyRecord::getCreateTime);
        List<TSyncUserHistroyRecord> recordList = new ArrayList<>();
        if (recordCount!=null && recordCount > 0 ) {
            queryWrapperRecord.last("limit " + startIndex + "," + basePageForm.getPageSize());
            recordList = tSyncUserHistroyRecordMapper.selectListNoAdd(
                    queryWrapperRecord);
        }
        List<TSyncUserHistroyRecordDTO> recordDTOList = ListCopyUtil.copy(recordList, TSyncUserHistroyRecordDTO.class);
        // 姓名、手机号码脱敏处理
        recordDTOList.forEach(x -> {
            x.setAppName(appMap.get(x.getAppId().toString()).getAppName());
            x.setAppCode(appMap.get(x.getAppId().toString()).getAppCode());
            x.setLoginName(StringUtils.isNotEmpty(x.getLoginName()) ? x.getLoginName() : x.getStrUserId());
            String strMobile = x.getStrMobile();
            if (null != strMobile && !"".equals(strMobile)) {
                x.setStrMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getStrMobile()));
            }
            String strCname = x.getStrCname();
            if (null != strCname && !"".equals(strCname)) {
                x.setStrCname(DesensitizeUtil.desensitizedName(x.getStrCname()));
            }
        });
        return new PageResult<TSyncUserHistroyRecordDTO>(recordDTOList,
                recordCount, basePageForm.getCurrentPage());
    }

    @Override
    @SuppressWarnings("all")
    public PageResult<TSyncUserHistroyRecordDTO> queryListPageByRole(TSyncUserHistroyRecordDTO entityDTO, BasePageForm basePageForm) {
        Integer recordCount;
        Integer startIndex = (basePageForm.getCurrentPage() - 1) * basePageForm.getPageSize();

        //改造第一步根据条件查询出APP数据
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperApp = new LambdaQueryWrapper();
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppName()),TSyncAppSystemManage::getAppName,entityDTO.getAppName());
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppCode()),TSyncAppSystemManage::getAppCode,entityDTO.getAppCode());
        List<TSyncAppSystemManage> tSyncAppSystemManages = tSyncAppSystemManageService.selectListNoAdd(queryWrapperApp);
        Map<String, TSyncAppSystemManage> appMap = tSyncAppSystemManages.stream()
                .collect(Collectors.toMap(
                        app -> app.getId().toString(), // 提取 appId 作为 key
                        app -> app // value 就是对象本身
                ));
        //获取APPid集合
        List<String> appIdList = tSyncAppSystemManages.stream().map(i -> i.getId().toString()).collect(Collectors.toList());
        //组装数据
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapperRecord = new LambdaQueryWrapper();
        queryWrapperRecord.in(!CollectionUtils.isEmpty(appIdList),TSyncUserHistroyRecord::getAppId,appIdList)
                .eq(StringUtils.isNotEmpty(entityDTO.getStrOperaType()),TSyncUserHistroyRecord::getStrOperaType,entityDTO.getStrOperaType())
                .eq(entityDTO.getUserId() != null,TSyncUserHistroyRecord::getUserId,entityDTO.getUserId())
                .eq(StringUtils.isNotEmpty(entityDTO.getSyncSuccess()),TSyncUserHistroyRecord::getSyncSuccess,entityDTO.getSyncSuccess())
                .like(StringUtils.isNotEmpty(entityDTO.getStrCname()),TSyncUserHistroyRecord::getStrCname,entityDTO.getStrCname())
                .like(StringUtils.isNotEmpty(entityDTO.getStrMobile()),TSyncUserHistroyRecord::getStrMobile,entityDTO.getStrMobile())
                .eq(!Objects.isNull(entityDTO.getCreateBy()),TSyncUserHistroyRecord::getCreateBy,entityDTO.getCreateBy())
                .ge(entityDTO.getCreateTimeStart()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeStart())
                .le(entityDTO.getCreateTimeEnd()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeEnd());
        if (StringUtils.isNotEmpty(entityDTO.getLoginName())) {
            queryWrapperRecord.and(qw ->
                    qw.like(StringUtils.isNotEmpty(entityDTO.getLoginName()), TSyncUserHistroyRecord::getLoginName,
                                    entityDTO.getLoginName())
                            .or()
                            .like(StringUtils.isNotEmpty(entityDTO.getLoginName()),
                                    TSyncUserHistroyRecord::getStrUserId, entityDTO.getLoginName()));
        }

        // 先执行COUNT查询（不包含ORDER BY）
        // 如果selectCountNoAdd有问题，临时使用标准方法
        recordCount = Math.toIntExact(tSyncUserHistroyRecordMapper.selectCountWithAlias(queryWrapperRecord));

        // 为数据查询添加ORDER BY
        queryWrapperRecord.orderByDesc(TSyncUserHistroyRecord::getCreateTime);
        List<TSyncUserHistroyRecord> recordList = new ArrayList<>();
        if (recordCount!=null && recordCount > 0 ) {
            IPage<TSyncUserHistroyRecord> pageData = tSyncUserHistroyRecordMapper.selectPageNoAdd(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm),queryWrapperRecord);
            recordList = pageData.getRecords();
        }
        List<TSyncUserHistroyRecordDTO> recordDTOList = ListCopyUtil.copy(recordList, TSyncUserHistroyRecordDTO.class);
        // 姓名、手机号码脱敏处理
        recordDTOList.forEach(x -> {
            x.setAppName(appMap.get(x.getAppId().toString()).getAppName());
            x.setAppCode(appMap.get(x.getAppId().toString()).getAppCode());
            x.setLoginName(StringUtils.isNotEmpty(x.getLoginName()) ? x.getLoginName() : x.getStrUserId());
            String strMobile = x.getStrMobile();
            if (null != strMobile && !"".equals(strMobile)) {
                x.setStrMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getStrMobile()));
            }
            String strCname = x.getStrCname();
            if (null != strCname && !"".equals(strCname)) {
                x.setStrCname(DesensitizeUtil.desensitizedName(x.getStrCname()));
            }
        });
        return new PageResult<TSyncUserHistroyRecordDTO>(recordDTOList,
                recordCount, basePageForm.getCurrentPage());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSyncUserHistroyRecordDTO> queryList(TSyncUserHistroyRecordDTO entityDTO) {
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper();
            List<TSyncUserHistroyRecord> listData = tSyncUserHistroyRecordMapper.selectList(queryWrapper);
            List<TSyncUserHistroyRecordDTO> TSyncUserHistroyRecordDTOList = ListCopyUtil.copy(listData, TSyncUserHistroyRecordDTO.class);
        return TSyncUserHistroyRecordDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSyncUserHistroyRecordDTO findOne(Long id) {
        TSyncUserHistroyRecord  tSyncUserHistroyRecord =  tSyncUserHistroyRecordMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSyncUserHistroyRecord,TSyncUserHistroyRecordDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncUserHistroyRecordDTO create(TSyncUserHistroyRecordDTO entityDTO) {
       TSyncUserHistroyRecord tSyncUserHistroyRecord =  BeanConvertUtils.copyProperties(entityDTO,TSyncUserHistroyRecord.class);
       // 手动设置create_time，确保分表路由正确
       if (tSyncUserHistroyRecord.getCreateTime() == null) {
           tSyncUserHistroyRecord.setCreateTime(LocalDateTime.now());
       }
        save(tSyncUserHistroyRecord);
        return  BeanConvertUtils.copyProperties(tSyncUserHistroyRecord,TSyncUserHistroyRecordDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSyncUserHistroyRecordDTO entity) {
        TSyncUserHistroyRecord tSyncUserHistroyRecord = BeanConvertUtils.copyProperties(entity,TSyncUserHistroyRecord.class);
        return tSyncUserHistroyRecordMapper.updateById(tSyncUserHistroyRecord);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSyncUserHistroyRecordMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSyncUserHistroyRecordId
     * @return
     */
    @Override
    public boolean existByTSyncUserHistroyRecordId(Long TSyncUserHistroyRecordId) {
        if (TSyncUserHistroyRecordId != null) {
            LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSyncUserHistroyRecord::getId, TSyncUserHistroyRecordId);
            List<TSyncUserHistroyRecord> result = tSyncUserHistroyRecordMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSyncUserHistroyRecordDTO> dataList) {
        List<TSyncUserHistroyRecord> result = ListCopyUtil.copy(dataList, TSyncUserHistroyRecord.class);
        // 手动设置create_time，确保分表路由正确
        LocalDateTime now = LocalDateTime.now();
        for (TSyncUserHistroyRecord record : result) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(now);
            }
        }
        return saveBatch(result);
    }

    @Override
    public void syncCallback(TSyncUserHistoryRecordBackDTO dto) {
        if (!"zwylz".equals(dto.getSourceKey())) {
            return;
        }
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSyncUserHistroyRecord::getEventId, dto.getEventId());
        List<TSyncUserHistroyRecord> result = tSyncUserHistroyRecordMapper.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        for (TSyncUserHistroyRecord record : result) {
            record.setSyncSuccess("false");
            record.setSyncMessage(dto.getSyncMessage());
            record.setSyncStatus(dto.getSyncStatus());
            if (StringUtils.isNotEmpty(dto.getPhone())) {
                List<String> imuUserids = Arrays.asList(dto.getPhone().split(","));
                if (imuUserids.contains(record.getStrMobile())) {
                    record.setSyncSuccess("true");
                    record.setSyncMessage("同步成功");
                    record.setSyncStatus("200");
                }
            }
        }
        this.updateBatchById(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncUserHistroyRecordDTO generateUserHistoryRecord(Long userId, TSyncAppSystemManage appSystemManage, CscpUserDetail cscpUserDetail) {
        if (null == cscpUserDetail) {
            cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        }
        CscpUserDTO cscpUserDTO = cscpUserRepository.selectUserById(userId);
        if (null == cscpUserDTO) {
            throw new BusinessException("生成推送记录异常!");
        }
        TSyncUserHistroyRecord userRecord = new TSyncUserHistroyRecord();
        userRecord.setAppId(appSystemManage.getId());
        userRecord.setUserId(cscpUserDTO.getId());
        userRecord.setStrCname(cscpUserDTO.getRealName());
        userRecord.setStrUserId(cscpUserDTO.getLoginName());
        userRecord.setLoginName(cscpUserDTO.getLoginName());
        userRecord.setStrId(cscpUserDTO.getStrId());
        userRecord.setStrOperaType("add");
        userRecord.setRequestMode(appSystemManage.getRequestMode());
        userRecord.setCreateBy(cscpUserDetail.getId());
        userRecord.setCreateName(cscpUserDetail.getRealName());
        userRecord.setCreateTime(DateUtils.getLocalDateTime());
        userRecord.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
        userRecord.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
        userRecord.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
        userRecord.setInSystemFlag(appSystemManage.getInSystemFlag());
        userRecord.setStrMobile(cscpUserDTO.getMobile());
        // 同步中
        userRecord.setSyncSuccess("ing");
        userRecord.setSyncMessage("推送中");
        userRecord.setSyncStatus("205");
        tSyncUserHistroyRecordMapper.insert(userRecord);
        return BeanConvertUtils.copyProperties(userRecord, TSyncUserHistroyRecordDTO.class);
    }
}
