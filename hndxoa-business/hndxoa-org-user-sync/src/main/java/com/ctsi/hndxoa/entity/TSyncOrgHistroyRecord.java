package com.ctsi.hndxoa.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 同步机构历史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sync_org_histroy_record")
@ApiModel(value="TSyncOrgHistroyRecord对象", description="同步机构历史记录表")
public class TSyncOrgHistroyRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应用系统ID
     */
    @ApiModelProperty(value = "应用系统ID")
    private Long appId;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long orgId;

    /**
     * 父级机构ID
     */
    @ApiModelProperty(value = "父级机构ID")
    private Long parentId;


    @ApiModelProperty(value = "商信机构ID")
    private String strId;

    @ApiModelProperty(value = "商信父级机构ID")
    private String strParentId;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String strUnitName;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    private String strUnitCode;

    /**
     * 机构区域编码
     */
    @ApiModelProperty(value = "机构区域编码")
    private String strAreaCode;

    /**
     * 同步操作类型
     */
    @ApiModelProperty(value = "同步操作类型")
    private String strOperaType;

    /**
     * 同步是否成功
     */
    @ApiModelProperty(value = "同步是否成功")
    private String syncSuccess;

    /**
     * 同步异常信息
     */
    @ApiModelProperty(value = "同步异常信息")
    private String syncMessage;

    /**
     * 同步响应状态码 （200:正常响应　1001:信任号已存在　1002:父级单位不存在）
     */
    @ApiModelProperty(value = "同步响应状态码 （200:正常响应　1001:信任号已存在　1002:父级单位不存在  205:推送中）")
    private String syncStatus;

    /**
     * 是否内部系统推送 0-否，1-是
     */
    @ApiModelProperty(value = "是否内部系统推送 0-否，1-是")
    private Integer inSystemFlag;


    @ApiModelProperty(value = "请求方式(0:MQ,1:HTTP[默认])")
    private String requestMode;

    /**
     * 0-手动推送，1-自动推送
     */
    @ApiModelProperty(value = "0-手动推送，1-自动推送")
    private String operateType;

    @ApiModelProperty(value = "数据来源ID")
    private Long sourceId;

    @ApiModelProperty(value = "批次号")
    private Long eventId;

    @ApiModelProperty(value = "拉取次数")
    private Integer syncCount;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;
}
