package com.ctsi.hndxoa.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO;
import com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步机构历史记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Slf4j
@Service
@DS(DbConst.MASTER)
public class TSyncOrgHistroyRecordServiceImpl extends SysBaseServiceImpl<TSyncOrgHistroyRecordMapper, TSyncOrgHistroyRecord> implements ITSyncOrgHistroyRecordService {

    @Autowired
    private TSyncOrgHistroyRecordMapper tSyncOrgHistroyRecordMapper;


    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSyncOrgHistroyRecordDTO> queryListPage(TSyncOrgHistroyRecordDTO entityDTO, BasePageForm basePageForm) {
        String loginUserName = SecurityUtils.getCurrentUserName();
        Integer startIndex = (basePageForm.getCurrentPage() - 1) * basePageForm.getPageSize();
        /*Integer recordCount;
        //设置条件
        if ("admin".equals(loginUserName)) {
            recordCount = tSyncOrgHistroyRecordMapper.selectRecordCount(entityDTO);
        } else {
            entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
            recordCount = tSyncOrgHistroyRecordMapper.selectRecordCountNoAdd(entityDTO);
        }
        List<TSyncOrgHistroyRecordDTO> recordDTOList = new ArrayList<>();
        if(recordCount > 0){
            if ("admin".equals(loginUserName)) {
                recordDTOList =  tSyncOrgHistroyRecordMapper.selectRecordList(entityDTO, startIndex, basePageForm.getPageSize());
            } else {
                entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
                recordDTOList = tSyncOrgHistroyRecordMapper.selectRecordListNoAdd(entityDTO, startIndex, basePageForm.getPageSize());
            }
        }*/

        //改造第一步根据条件查询出APP数据
        if (!"admin".equals(loginUserName)) {
            entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
        }
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperApp = new LambdaQueryWrapper();
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppName()),TSyncAppSystemManage::getAppName,entityDTO.getAppName());
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppCode()),TSyncAppSystemManage::getAppCode,entityDTO.getAppCode());
        List<TSyncAppSystemManage> tSyncAppSystemManages = tSyncAppSystemManageService.selectListNoAdd(queryWrapperApp);
        Map<String, TSyncAppSystemManage> appMap = tSyncAppSystemManages.stream()
                .collect(Collectors.toMap(
                        app -> app.getId().toString(), // 提取 appId 作为 key
                        app -> app // value 就是对象本身
                ));
        //获取APPid集合
        List<String> appIdList = tSyncAppSystemManages.stream().map(i -> i.getId().toString()).collect(Collectors.toList());
        //组装数据
        LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapperRecord = new LambdaQueryWrapper();
        queryWrapperRecord.in(!CollectionUtils.isEmpty(appIdList),TSyncOrgHistroyRecord::getAppId,appIdList)
                .eq(StringUtils.isNotEmpty(entityDTO.getStrOperaType()),TSyncOrgHistroyRecord::getStrOperaType,entityDTO.getStrOperaType())
                .eq(StringUtils.isNotEmpty(entityDTO.getSyncSuccess()),TSyncOrgHistroyRecord::getSyncSuccess,entityDTO.getSyncSuccess())
                .like(StringUtils.isNotEmpty(entityDTO.getStrUnitName()),TSyncOrgHistroyRecord::getStrUnitName,entityDTO.getStrUnitName())
                .eq(entityDTO.getCreateBy()!=null,TSyncOrgHistroyRecord::getCreateBy,entityDTO.getCreateBy())
                .ge(entityDTO.getCreateTimeStart()!=null,TSyncOrgHistroyRecord::getCreateTime,entityDTO.getCreateTimeStart())
                .le(entityDTO.getCreateTimeEnd()!=null,TSyncOrgHistroyRecord::getCreateTime,entityDTO.getCreateTimeEnd())
                .eq(entityDTO.getSourceId()!=null,TSyncOrgHistroyRecord::getSourceId,entityDTO.getSourceId())
                .eq(entityDTO.getOrgId()!=null,TSyncOrgHistroyRecord::getOrgId,entityDTO.getOrgId());

        // 先执行COUNT查询（不包含ORDER BY）
        // 如果selectCountNoAdd有问题，临时使用标准方法
        Integer recordCount = Math.toIntExact(tSyncOrgHistroyRecordMapper.selectCountWithAlias(queryWrapperRecord));

        // 为数据查询添加ORDER BY
        queryWrapperRecord.orderByDesc(TSyncOrgHistroyRecord::getCreateTime);
        List<TSyncOrgHistroyRecord> tSyncOrgHistroyRecords = new ArrayList<>();
        if (recordCount!=null && recordCount > 0 ) {
            IPage<TSyncOrgHistroyRecord> pageData = tSyncOrgHistroyRecordMapper.selectPageNoAdd(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm),queryWrapperRecord);
            tSyncOrgHistroyRecords = pageData.getRecords();
        }

        List<TSyncOrgHistroyRecordDTO> recordDTOList = ListCopyUtil.copy(tSyncOrgHistroyRecords, TSyncOrgHistroyRecordDTO.class);
        recordDTOList.stream().forEach(e->{
            TSyncAppSystemManage tSyncAppSystemManage = appMap.get(Objects.toString (e.getAppId()));
            e.setAppName(tSyncAppSystemManage.getAppName());
            e.setAppCode(tSyncAppSystemManage.getAppCode());
        });
        return new PageResult<TSyncOrgHistroyRecordDTO>(recordDTOList,
                recordCount, basePageForm.getCurrentPage());
    }

    @Override
    @SuppressWarnings("all")
    public PageResult<TSyncOrgHistroyRecordDTO> queryListPageByRole(TSyncOrgHistroyRecordDTO entityDTO, BasePageForm basePageForm) {

        Integer startIndex = (basePageForm.getCurrentPage() - 1) * basePageForm.getPageSize();
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperApp = new LambdaQueryWrapper();
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppName()),TSyncAppSystemManage::getAppName,entityDTO.getAppName());
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppCode()),TSyncAppSystemManage::getAppCode,entityDTO.getAppCode());
        List<TSyncAppSystemManage> tSyncAppSystemManages = tSyncAppSystemManageService.selectListNoAdd(queryWrapperApp);
        Map<String, TSyncAppSystemManage> appMap = tSyncAppSystemManages.stream()
                .collect(Collectors.toMap(
                        app -> app.getId().toString(), // 提取 appId 作为 key
                        app -> app // value 就是对象本身
                ));
        //获取APPid集合
        List<String> appIdList = tSyncAppSystemManages.stream().map(i -> i.getId().toString()).collect(Collectors.toList());
        //组装数据
        LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapperRecord = new LambdaQueryWrapper();
        queryWrapperRecord.in(!CollectionUtils.isEmpty(appIdList),TSyncOrgHistroyRecord::getAppId,appIdList)
                .eq(StringUtils.isNotEmpty(entityDTO.getStrOperaType()),TSyncOrgHistroyRecord::getStrOperaType,entityDTO.getStrOperaType())
                .eq(StringUtils.isNotEmpty(entityDTO.getSyncSuccess()),TSyncOrgHistroyRecord::getSyncSuccess,entityDTO.getSyncSuccess())
                .like(StringUtils.isNotEmpty(entityDTO.getStrUnitName()),TSyncOrgHistroyRecord::getStrUnitName,entityDTO.getStrUnitName())
                .ge(entityDTO.getCreateTimeStart()!=null,TSyncOrgHistroyRecord::getCreateTime,entityDTO.getCreateTimeStart())
                .le(entityDTO.getCreateTimeEnd()!=null,TSyncOrgHistroyRecord::getCreateTime,entityDTO.getCreateTimeEnd())
                .eq(entityDTO.getSourceId()!=null,TSyncOrgHistroyRecord::getSourceId,entityDTO.getSourceId())
                .eq(entityDTO.getOrgId()!=null,TSyncOrgHistroyRecord::getOrgId,entityDTO.getOrgId());

        // 先执行COUNT查询（不包含ORDER BY）
        // 如果selectCountNoAdd有问题，临时使用标准方法
        Integer recordCount = Math.toIntExact(tSyncOrgHistroyRecordMapper.selectCountWithAlias(queryWrapperRecord));

        // 为数据查询添加ORDER BY
        queryWrapperRecord.orderByDesc(TSyncOrgHistroyRecord::getCreateTime);
        List<TSyncOrgHistroyRecord> tSyncOrgHistroyRecords = new ArrayList<>();
        if (recordCount!=null && recordCount > 0 ) {
            queryWrapperRecord.last("limit " + startIndex + "," + basePageForm.getPageSize());
            tSyncOrgHistroyRecords = tSyncOrgHistroyRecordMapper.selectListNoAdd(
                    queryWrapperRecord);
        }

        List<TSyncOrgHistroyRecordDTO> recordDTOList = ListCopyUtil.copy(tSyncOrgHistroyRecords, TSyncOrgHistroyRecordDTO.class);
        recordDTOList.stream().forEach(e->{
            TSyncAppSystemManage tSyncAppSystemManage = appMap.get(Objects.toString (e.getAppId()));
            e.setAppName(tSyncAppSystemManage.getAppName());
            e.setAppCode(tSyncAppSystemManage.getAppCode());
        });
        return new PageResult<TSyncOrgHistroyRecordDTO>(recordDTOList,
                recordCount, basePageForm.getCurrentPage());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSyncOrgHistroyRecordDTO> queryList(TSyncOrgHistroyRecordDTO entityDTO) {
        LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapper = new LambdaQueryWrapper();
            List<TSyncOrgHistroyRecord> listData = tSyncOrgHistroyRecordMapper.selectList(queryWrapper);
            List<TSyncOrgHistroyRecordDTO> TSyncOrgHistroyRecordDTOList = ListCopyUtil.copy(listData, TSyncOrgHistroyRecordDTO.class);
        return TSyncOrgHistroyRecordDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSyncOrgHistroyRecordDTO findOne(Long id) {
        TSyncOrgHistroyRecord  tSyncOrgHistroyRecord =  tSyncOrgHistroyRecordMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSyncOrgHistroyRecord,TSyncOrgHistroyRecordDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncOrgHistroyRecordDTO create(TSyncOrgHistroyRecordDTO entityDTO) {
       TSyncOrgHistroyRecord tSyncOrgHistroyRecord =  BeanConvertUtils.copyProperties(entityDTO,TSyncOrgHistroyRecord.class);
       // 手动设置create_time，确保分表路由正确
       if (tSyncOrgHistroyRecord.getCreateTime() == null) {
           tSyncOrgHistroyRecord.setCreateTime(LocalDateTime.now());
       }
        save(tSyncOrgHistroyRecord);
        return  BeanConvertUtils.copyProperties(tSyncOrgHistroyRecord,TSyncOrgHistroyRecordDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSyncOrgHistroyRecordDTO entity) {
        TSyncOrgHistroyRecord tSyncOrgHistroyRecord = BeanConvertUtils.copyProperties(entity,TSyncOrgHistroyRecord.class);
        return tSyncOrgHistroyRecordMapper.updateById(tSyncOrgHistroyRecord);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSyncOrgHistroyRecordMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSyncOrgHistroyRecordId
     * @return
     */
    @Override
    public boolean existByTSyncOrgHistroyRecordId(Long TSyncOrgHistroyRecordId) {
        if (TSyncOrgHistroyRecordId != null) {
            LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSyncOrgHistroyRecord::getId, TSyncOrgHistroyRecordId);
            List<TSyncOrgHistroyRecord> result = tSyncOrgHistroyRecordMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSyncOrgHistroyRecordDTO> dataList) {
        List<TSyncOrgHistroyRecord> result = ListCopyUtil.copy(dataList, TSyncOrgHistroyRecord.class);
        // 手动设置create_time，确保分表路由正确
        LocalDateTime now = LocalDateTime.now();
        for (TSyncOrgHistroyRecord record : result) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(now);
            }
        }
        return saveBatch(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncOrgHistroyRecordDTO generateOrgHistoryRecord(Long orgId, TSyncAppSystemManage appSystemManage, CscpUserDetail cscpUserDetail) {
        if (null == cscpUserDetail) {
            cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        }
        CscpOrg cscpOrg = cscpOrgRepository.getOrgIgnoreDeleted(orgId);
        if (null == cscpOrg) {
            throw new BusinessException("生成推送记录异常!");
        }
        TSyncOrgHistroyRecord orgRecord = new TSyncOrgHistroyRecord();
        orgRecord.setAppId(appSystemManage.getId());
        orgRecord.setOrgId(cscpOrg.getId());
        orgRecord.setParentId(cscpOrg.getParentId());
        orgRecord.setStrId(cscpOrg.getStrId());
        orgRecord.setStrParentId(cscpOrg.getStrParentId());
        orgRecord.setStrUnitName(cscpOrg.getOrgName());
        orgRecord.setStrUnitCode(cscpOrg.getOrgCode());
        orgRecord.setStrAreaCode(cscpOrg.getRegionCode());
        orgRecord.setStrOperaType("add");
        orgRecord.setRequestMode(appSystemManage.getRequestMode());
        orgRecord.setCreateBy(cscpUserDetail.getId());
        orgRecord.setCreateName(cscpUserDetail.getRealName());
        orgRecord.setCreateTime(DateUtils.getLocalDateTime());
        orgRecord.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
        orgRecord.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
        orgRecord.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
        orgRecord.setInSystemFlag(appSystemManage.getInSystemFlag());
        // 同步中
        orgRecord.setSyncSuccess("ing");
        orgRecord.setSyncMessage("已通知同步");
        orgRecord.setSyncStatus("205");
        tSyncOrgHistroyRecordMapper.insert(orgRecord);
        return BeanConvertUtils.copyProperties(orgRecord, TSyncOrgHistroyRecordDTO.class);
    }
}
