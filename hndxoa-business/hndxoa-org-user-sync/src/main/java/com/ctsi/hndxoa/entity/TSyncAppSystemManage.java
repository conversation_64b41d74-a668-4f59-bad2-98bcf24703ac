package com.ctsi.hndxoa.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 同步应用系统管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sync_app_system_manage")
@ApiModel(value="TSyncAppSystemManage对象", description="同步应用系统管理表")
public class TSyncAppSystemManage extends BaseEntity {

    private static final Map<String, String> ORG_CODE_CACHE = new ConcurrentHashMap<>();

    private static final long serialVersionUID = 1L;

    /**
     * 应用系统名称
     */
    @ApiModelProperty(value = "应用系统名称")
    private String appName;

    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号")
    private String appCode;

    /**
     * 数据同步url
     */
    @ApiModelProperty(value = "数据同步url")
    private String syncUrl;

    /**
     * 状态 0-禁用，1-可用
     */
    @ApiModelProperty(value = "状态 0-禁用，1-可用")
    private Integer status;


    /**
     * 鉴权 0-不需要鉴权，1-需要鉴权
     */
    @ApiModelProperty(value = "鉴权 0-不需要鉴权，1-需要鉴权")
    private Integer authentication;

    /**
     * 三方 appId 用于后续鉴权
     */
    @ApiModelProperty(value = "三方 appId 用于后续鉴权")
    private String appId;

    /**
     * 三方 appKey 用于后续鉴权
     */
    @ApiModelProperty(value = "三方 appKey 用于后续鉴权")
    private String appKey;

    /**
     * 是否内部系统 0-否，1-是
     */
    @ApiModelProperty(value = "是否内部系统 0-否，1-是")
    private Integer inSystemFlag;

    /**
     * 内部系统同步接口url
     */
    @ApiModelProperty(value = "内部系统同步接口url")
    private String inSystemUrl;

    @ApiModelProperty(value = "被动模式url")
    private String passiveUrl;

    /**
     * 拥有机构编码
     */
    @ApiModelProperty(value = "拥有机构编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED) // 设置字段策略为：忽略判断
    private String inOwnedOrgId;

    /**
     * 请求方式(0:MQ,1:HTTP[默认])
     */
    @ApiModelProperty(value = "请求方式(0:MQ,1:HTTP[默认])")
    private String requestMode;

    /**
     * 自动推送(0:关闭,1:开启)
     */
    @ApiModelProperty(value = "自动推送(0:关闭,1:开启)")
    private String autoPush;

    @ApiModelProperty(value = "应用角色ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long roleId;

    @ApiModelProperty(value = "应用角色名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String roleName;

    @ApiModelProperty(value = "是否移交版主管理, 0-否, 1-是")
    private Integer moderatorFlag;

    public String getAppRegionCode(CscpOrgRepository cscpOrgRepository) {
        if (StringUtils.isEmpty(inOwnedOrgId) || appCode.contains("4300")) {
            return appCode;
        }

        String orgId = inOwnedOrgId.trim();
        if (orgId.contains(",")) {
            orgId = StringUtils.substringBefore(orgId, ",").trim();
        }
        String cachedOrgCode = ORG_CODE_CACHE.get(orgId);
        if (cachedOrgCode != null) {
            return appCode + StringUtils.substring(cachedOrgCode, 0, Math.min(4, cachedOrgCode.length()));
        }

        try {
            CscpOrg cscpOrg = cscpOrgRepository.selectById(orgId);
            if (cscpOrg != null && StringUtils.isNotEmpty(cscpOrg.getOrgCode())) {
                String orgCode = cscpOrg.getOrgCode();
                ORG_CODE_CACHE.put(orgId, orgCode);
                return appCode + StringUtils.substring(orgCode, 0, Math.min(4, orgCode.length()));
            }
        } catch (Exception e) {
            // 捕获数据库查询异常，记录日志并返回默认值
            throw new BusinessException("Error querying database for orgId: " + orgId + ", Exception: " + e.getMessage());
        }

        return appCode;
    }

}
