package com.ctsi.hndxoa.pull.utils;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Component
@SuppressWarnings("all")
public class PassiveSyncCountTool {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 计数器默认过期时间（24小时）
    private static final long DEFAULT_EXPIRE_HOURS = 24;
    // 缓存名称（区分不同类型的缓存）
    private static final String ORG_COUNT_CACHE = "orgCountCache";
    private static final String USER_COUNT_CACHE = "userCountCache";

    // 确保key使用字符串序列化（避免乱码）
    @PostConstruct
    public void init() {
        if (redisTemplate.getKeySerializer() == null) {
            redisTemplate.setKeySerializer(new StringRedisSerializer());
        }
    }

    // ==================== Key生成规则（Redis的key） ====================
    private String getOrgKey(Long historyId) {
        if (historyId == null) {
            throw new IllegalArgumentException("historyId must not be null");
        }
        return "sync:count:org:" + historyId;
    }

    private String getUserKey(Long historyId) {
        if (historyId == null) {
            throw new IllegalArgumentException("historyId must not be null");
        }
        return "sync:count:user:" + historyId;
    }

    // ==================== 初始化方法（从0开始） ====================
    @Caching(evict = {
            @CacheEvict(value = ORG_COUNT_CACHE, key = "#historyId")
    })
    public boolean initOrgCount(Long historyId, boolean force) {
        String key = getOrgKey(historyId);
        if (force) {
            // 强制重置为0
            return redisTemplate.execute((RedisCallback<Boolean>) connection ->
                    connection.stringCommands().set(
                            key.getBytes(StandardCharsets.UTF_8),
                            "0".getBytes(StandardCharsets.UTF_8),
                            Expiration.from(DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS),
                            RedisStringCommands.SetOption.SET_IF_PRESENT // 仅当key存在时覆盖
                    )
            );
        } else {
            // 仅当key不存在时初始化
            Boolean setIfAbsent = redisTemplate.execute((RedisCallback<Boolean>) connection ->
                    connection.stringCommands().set(
                            key.getBytes(StandardCharsets.UTF_8),
                            "0".getBytes(StandardCharsets.UTF_8),
                            Expiration.from(DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS),
                            RedisStringCommands.SetOption.SET_IF_ABSENT // 仅当key不存在时设置
                    )
            );
            return Boolean.TRUE.equals(setIfAbsent); // 首次初始化成功返回true
        }
    }

    @CacheEvict(value = ORG_COUNT_CACHE, key = "#historyId")
    public boolean initOrgCount(Long historyId) {
        return initOrgCount(historyId, false);
    }

    // 用户计数初始化方法（与机构计数逻辑一致）
    @Caching(evict = {
            @CacheEvict(value = USER_COUNT_CACHE, key = "#historyId")
    })
    public boolean initUserCount(Long historyId, boolean force) {
        String key = getUserKey(historyId);
        if (force) {
            return redisTemplate.execute((RedisCallback<Boolean>) connection ->
                    connection.stringCommands().set(
                            key.getBytes(StandardCharsets.UTF_8),
                            "0".getBytes(StandardCharsets.UTF_8),
                            Expiration.from(DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS),
                            RedisStringCommands.SetOption.SET_IF_PRESENT
                    )
            );
        } else {
            Boolean setIfAbsent = redisTemplate.execute((RedisCallback<Boolean>) connection ->
                    connection.stringCommands().set(
                            key.getBytes(StandardCharsets.UTF_8),
                            "0".getBytes(StandardCharsets.UTF_8),
                            Expiration.from(DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS),
                            RedisStringCommands.SetOption.SET_IF_ABSENT
                    )
            );
            return Boolean.TRUE.equals(setIfAbsent);
        }
    }

    @CacheEvict(value = USER_COUNT_CACHE, key = "#historyId")
    public boolean initUserCount(Long historyId) {
        return initUserCount(historyId, false);
    }

    // ==================== 自增方法（存在则+1，不存在返回null） ====================
    @CacheEvict(value = ORG_COUNT_CACHE, key = "#historyId")
    public Long incrementOrgCount(Long historyId) {
        String key = getOrgKey(historyId);

        // 检查key是否存在
        Boolean exists = redisTemplate.hasKey(key);
        if (Boolean.FALSE.equals(exists)) {
            // 不存在直接返回null
            return null;
        }

        // 存在则执行自增，返回自增后的值
        return redisTemplate.execute((RedisCallback<Long>) connection ->
                connection.stringCommands().incr(key.getBytes(StandardCharsets.UTF_8))
        );
    }

    @CacheEvict(value = USER_COUNT_CACHE, key = "#historyId")
    public Long incrementUserCount(Long historyId) {
        String key = getUserKey(historyId);

        // 检查key是否存在
        Boolean exists = redisTemplate.hasKey(key);
        if (Boolean.FALSE.equals(exists)) {
            // 不存在直接返回null
            return null;
        }

        // 存在则执行自增，返回自增后的值
        return redisTemplate.execute((RedisCallback<Long>) connection ->
                connection.stringCommands().incr(key.getBytes(StandardCharsets.UTF_8))
        );
    }

    // ==================== 直接设置值的方法 ====================
    /**
     * 直接设置机构计数的指定值
     * @param historyId 历史ID
     * @param value 要设置的计数值
     * @return 是否设置成功
     */
    @CacheEvict(value = ORG_COUNT_CACHE, key = "#historyId")
    public boolean setOrgCount(Long historyId, Object value) {
        String key = getOrgKey(historyId);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        byte[] valueBytes = String.valueOf(value).getBytes(StandardCharsets.UTF_8);
        Expiration expiration = Expiration.from(DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);

        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            // 先判断key是否存在
            Boolean exists = connection.keyCommands().exists(keyBytes);
            if (Boolean.TRUE.equals(exists)) {
                // 存在则覆盖（使用SET_IF_PRESENT）
                return connection.stringCommands().set(keyBytes, valueBytes, expiration, RedisStringCommands.SetOption.SET_IF_PRESENT);
            } else {
                // 不存在则新增（使用SET_IF_ABSENT）
                return connection.stringCommands().set(keyBytes, valueBytes, expiration, RedisStringCommands.SetOption.SET_IF_ABSENT);
            }
        });
    }

    /**
     * 直接设置用户计数的指定值
     * @param historyId 历史ID
     * @param value 要设置的计数值
     * @return 是否设置成功
     */
    @CacheEvict(value = USER_COUNT_CACHE, key = "#historyId")
    public boolean setUserCount(Long historyId, Object value) {
        String key = getUserKey(historyId);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        byte[] valueBytes = String.valueOf(value).getBytes(StandardCharsets.UTF_8);
        Expiration expiration = Expiration.from(DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);

        return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            // 先判断key是否存在
            Boolean exists = connection.keyCommands().exists(keyBytes);
            if (Boolean.TRUE.equals(exists)) {
                // 存在则覆盖（使用SET_IF_PRESENT）
                return connection.stringCommands().set(keyBytes, valueBytes, expiration, RedisStringCommands.SetOption.SET_IF_PRESENT);
            } else {
                // 不存在则新增（使用SET_IF_ABSENT）
                return connection.stringCommands().set(keyBytes, valueBytes, expiration, RedisStringCommands.SetOption.SET_IF_ABSENT);
            }
        });
    }

    // ==================== 查询方法 ====================
    @Cacheable(value = ORG_COUNT_CACHE, key = "#historyId", unless = "#result == null")
    public Integer getOrgCount(Long historyId) {
        Object value = redisTemplate.opsForValue().get(getOrgKey(historyId));
        return convertToInteger(value);
    }

    @Cacheable(value = USER_COUNT_CACHE, key = "#historyId", unless = "#result == null")
    public Integer getUserCount(Long historyId) {
        Object value = redisTemplate.opsForValue().get(getUserKey(historyId));
        return convertToInteger(value);
    }

    // ==================== 类型转换工具方法 ====================
    private Integer convertToInteger(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Long) {
            return ((Long) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
