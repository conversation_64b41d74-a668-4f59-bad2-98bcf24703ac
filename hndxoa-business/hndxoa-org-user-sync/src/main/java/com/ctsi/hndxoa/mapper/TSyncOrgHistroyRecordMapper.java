package com.ctsi.hndxoa.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO;
import com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 同步机构历史记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@DS(DbConst.MASTER)
public interface TSyncOrgHistroyRecordMapper extends MybatisBaseMapper<TSyncOrgHistroyRecord> {

    //@InterceptorIgnore(tenantLine = "true")
    Integer selectRecordCount(@Param("entityDTO") TSyncOrgHistroyRecordDTO entityDTO);

    //@InterceptorIgnore(tenantLine = "true")
    List<TSyncOrgHistroyRecordDTO> selectRecordList(@Param("entityDTO") TSyncOrgHistroyRecordDTO entityDTO, @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize);

    @InterceptorIgnore(tenantLine = "true")
    Integer selectRecordCountNoAdd(@Param("entityDTO") TSyncOrgHistroyRecordDTO entityDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<TSyncOrgHistroyRecordDTO> selectRecordListNoAdd(@Param("entityDTO") TSyncOrgHistroyRecordDTO entityDTO, @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize);

    /**
     * 查询需要同步的组织机构列表：根据当前组织机构，查询所有的上级组织机构，根据组织机构当前节点查询所有的父级节点，列表展示
     * @param entityDTO
     * @return
     */
    //@InterceptorIgnore(tenantLine = "true")
    List<SyncWestoneOrgDTO> selectParentSyncOrgList(@Param("entityDTO") SyncOrgUserDTO entityDTO);

    /**
     * 查询需要同步的组织机构列表：根据当前组织机构，查询所有的子级组织机构，根据组织机构当前节点查询所有的子级节点，列表展示
     * @param entityDTO
     * @return
     */
    //@InterceptorIgnore(tenantLine = "true")
    List<SyncWestoneOrgDTO> selectChildrenSyncOrgList(@Param("entityDTO") SyncOrgUserDTO entityDTO);

    /**
     * 查询需要同步的组织机构列表：查询所有的顶级组织机构，列表展示
     * @param entityDTO
     * @return
     */
    //@InterceptorIgnore(tenantLine = "true")
    List<SyncWestoneOrgDTO> selectRootSyncOrgList(@Param("entityDTO") SyncOrgUserDTO entityDTO);

    /**
     * 分页查询所有的组织机构列表
     * @param entityDTO
     * @param startIndex
     * @param pageSize
     * @return
     */
    //@InterceptorIgnore(tenantLine = "true")
    List<SyncWestoneOrgDTO> selectAllSyncOrgList(@Param("entityDTO") SyncOrgUserDTO entityDTO, @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize);

    /**
     * 分页查询所有未推送的组织机构列表
     * @param entityDTO
     * @param startIndex
     * @param pageSize
     * @return
     */
    //@InterceptorIgnore(tenantLine = "true")
    List<SyncWestoneOrgDTO> selectUnPushedSyncOrgList(@Param("entityDTO") SyncOrgUserDTO entityDTO, @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize);

    /**
     * 查询所有未推送的组织机构数量
     * @param entityDTO
     * @return
     */
    //@InterceptorIgnore(tenantLine = "true")
    Integer selectUnPushedSyncOrgCount(@Param("entityDTO") SyncOrgUserDTO entityDTO);

    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT COUNT(*) AS total FROM t_sync_org_histroy_record ${ew.customSqlSegment}")
    Integer selectCountWithAlias(@Param(Constants.WRAPPER) Wrapper<TSyncOrgHistroyRecord> queryWrapper);
}
